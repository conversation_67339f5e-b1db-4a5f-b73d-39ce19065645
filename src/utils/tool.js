// 检测是对象且不为空
export function hasObjLen(obj) {
  return obj && typeof obj === 'object' && !Array.isArray(obj) && Object.keys(obj).length > 0;
}

// 判断是否是数组且长度大于0
export function hasArrLen(arr) {
  return Array.isArray(arr) && arr.length > 0
}

// 获取文件后缀
export function getFileExt(fileName) {
  return typeof fileName === 'string' ? fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase() : ''
}

// 正则匹配http/https开头
export function isHttpUrl(str) {
  return /^http(s)?:\/\//.test(str)
}

export function isProd() {
  return process.env.VUE_APP_ENV === 'production'
}
