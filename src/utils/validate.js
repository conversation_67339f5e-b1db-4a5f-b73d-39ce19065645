/*
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-03-21 18:38:05
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-11 08:55:36
 */
/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

export function isNumber(value) {
  return (typeof value === 'number' ||
    (typeof value === 'string' && value.trim() !== '' && !isNaN(value) && isFinite(parseFloat(value))));
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ['admin', 'editor']
  return valid_map.indexOf(str.trim()) >= 0
}

/**
 * @param {string} url
 * @returns {Boolean}
 */
export function validURL(url) {
  const reg = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
  return reg.test(url)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validLowerCase(str) {
  const reg = /^[a-z]+$/
  return reg.test(str)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUpperCase(str) {
  const reg = /^[A-Z]+$/
  return reg.test(str)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validAlphabets(str) {
  const reg = /^[A-Za-z]+$/
  return reg.test(str)
}

/**
 * @param {string} email
 * @returns {Boolean}
 */
export function validEmail(email) {
  const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return reg.test(email)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function isString(str) {
  return typeof str === 'string' || str instanceof String;
}

/**
 * @param {Array} arg
 * @returns {Boolean}
 */
export function isArray(arg) {
  if (typeof Array.isArray === 'undefined') {
    return Object.prototype.toString.call(arg) === '[object Array]'
  }
  return Array.isArray(arg)
}

/* input输入框check身份证号*/
import { default as valiId } from '@/utils/functions'
export function valiIdCard(rule, value, callback) {
  var validres = valiId(value)
  if (validres.pass) {
    return callback(new Error(validres.tip))
  }
  return callback()
}

/* input输入框修改密码*/
export function valiPassword(rule, value, callback) {
  if (value !== '' && value !== null && value !== undefined) {
    if (!/^(?=.*\d)(?=.*[a-zA-Z])[\da-zA-Z~!@#$%^&*]{6,}$/.test(value)) {
      return callback(new Error('密码至少6位，由数字、字母组成'))
    }
  }
  return callback()
}

/* input输入框修改密码*/
export function valiPassword1(rule, value, callback) {
  if (value !== '' && value !== null && value !== undefined) {
    if (!/^[0-9]\d{5}$/.test(value)) {
      return callback(new Error('密码为6位纯数字'))
    }
  }
  return callback()
}
// 手机号验证
export function valiPhone(rule, value, callback) {
  if (!value) {
    return callback(new Error("手机号不能为空"));
  }
  if (!/^1\d{10}$/.test(value)) {
    return callback(new Error('请输入正确的手机号'))
  }
  return callback()
}

export function parserInt(value) {
  // console.log(typeof value, value)
  return value ? value.replace(/\D/g, '') : value
}

export function parserFloat(value, precision) {
  // console.log(typeof value, value)
  if (!isNumber(value)) return value
  value = value.toString()
  return value && value.indexOf('.') >= 1 ? value.slice(0, value.indexOf('.') + precision + 1) : value
}

export function isMultiple(value) {
  return /^[1-9]\d*:[1-9]\d*$/.test(value)
}
