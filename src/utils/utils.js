import { parse } from 'querystring';
import _ from 'lodash';
import { appCodeEnum } from './enumeration';
import {Message} from "element-ui";
import {hasArrLen} from "@/utils/tool";
/* eslint no-useless-escape:0 import/prefer-default-export:0 */

const reg =
  /(((^https?:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[\w]*))?)$/;
export const isUrl = (path) => reg.test(path);
export const isAntDesignPro = () => {
  if (ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION === 'site') {
    return true;
  }

  return window.location.hostname === 'preview.pro.ant.design';
}; // 给官方演示站点用，用于关闭真实开发环境不需要使用的特性

export const isAntDesignProOrDev = () => {
  const { NODE_ENV } = process.env;

  if (NODE_ENV === 'development') {
    return true;
  }

  return isAntDesignPro();
};
export const getPageQuery = () => parse(window.location.href.split('?')[1]);

export const arrToObj = (arr) => {
  return arr.reduce((last, current) => {
    const values = {};
    Object.entries(last).forEach(([key, value]) => {
      if (!value && value !== 0) {
        return;
      }
      values[key] = [value, current[key]].join(',');
    });
    return values;
  });
};

export function openNewPage(url) {
  const a = document.createElement('a');
  a.href = `${window.location.origin}${url}`;
  a.target = '_blank';
  a.style.display = 'none';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

export function postMessage(data) {
  try {
    window.postMessage(data, window.location.origin);
  } catch (e) {
    Message.warning('请手动刷新数据');
  }
}

export function restfulUrlReplace(url, params) {
  for (const t in params) {
    url = url.replace(new RegExp(`\{${t}\}`, 'g'), params[t]);
  }
  return url;
}

export function modelHelp(config) {
  const init = () => config.state;
  const subscriptions = config.subscriptions || {};
  const reducers = config.reducers || {};
  return {
    ...config,
    reducers: {
      ...reducers,
      _init(state, { payload }) {
        return init();
      },
    },
    subscriptions: {
      ...subscriptions,
      _init: ({ dispatch, history }) => {
        if (false) {
          dispatch({
            type: 'init',
          });
        }
      },
    },
  };
}

export const transConfigToObj = (arr, visitor) => {
  return arr.map((item) => {
    const newItem = {
      ...item,
    };
    if (newItem.config) {
      newItem.config = JSON.parse(newItem.config);
    }
    if (Array.isArray(newItem.childList) && newItem.childList.length) {
      newItem.childList = transConfigToObj(newItem.childList);
    }
    if (visitor) {
      return visitor(newItem.type, newItem);
    }
    return newItem;
  });
};

export const transValueToObj = (valueList = []) => {
  return valueList.map((item) => {
    let { value } = item;
    if (item.value) {
      try {
        value = JSON.parse(item.value);
      } catch (e) {
        value = item.value;
      }
    }
    return {
      ...item,
      value,
    };
  });
};

export const renderAuth = (auths) => {
  let result = [];
  // if (auths?.length > 0) {
  //   auths.forEach()
  // }
  // auths.map(aItem => {
  //     return aItem.cusAuthoritys.map(cItem => {
  //       return result.push(`${aItem.appCode}_${cItem}`)
  //     })
  //
  // })
  const auth = auths.find((item) => item.appCode === appCodeEnum.ADMIN);
  if (auth) {
    result = auth.cusAuthoritys;
  }
  return result;
};

export const quillToolBar = [
  ['bold', 'italic', 'underline', 'strike'], // toggled buttons
  ['blockquote', 'code-block'],

  [{ header: 1 }, { header: 2 }], // custom button values
  [{ list: 'ordered' }, { list: 'bullet' }],
  [{ script: 'sub' }, { script: 'super' }], // superscript/subscript
  [{ indent: '-1' }, { indent: '+1' }], // outdent/indent
  [{ direction: 'rtl' }], // text direction
  ['image'],
  ['video'],
  [{ size: ['small', false, 'large', 'huge'] }], // custom dropdown
  [{ header: [1, 2, 3, 4, 5, 6, false] }],

  [{ color: [] }, { background: [] }], // dropdown with defaults from theme
  [{ font: [] }],
  [{ align: [] }],

  ['clean'],
];
/**
 * Base64加密
 */
export function Base64() {
  const _keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
  const _keyStrUrl = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=';
  this.encode = function (input) {
    return encodebase(input, _keyStr);
  };

  // public method for decoding
  this.decode = function (input) {
    return decodebase(input, _keyStr);
  };

  this.urlEncode = function (input) {
    //将/号替换为_  将+号替换为-  后端采用 new String(Base64.getUrlDecoder().decode(encrypted.getBytes())) 进行解码
    return encodebase(input, _keyStrUrl);
  };

  this.urlDecode = function (input) {
    //将_号替换为/ 将-号替换为+
    return decodebase(input, _keyStrUrl);
  };

  const encodebase = (input, _keyStr) => {
    let output = '';
    let chr1, chr2, chr3, enc1, enc2, enc3, enc4;
    let i = 0;
    input = _utf8_encode(input);
    while (i < input.length) {
      chr1 = input.charCodeAt(i++);
      chr2 = input.charCodeAt(i++);
      chr3 = input.charCodeAt(i++);
      enc1 = chr1 >> 2;
      enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
      enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
      enc4 = chr3 & 63;
      if (isNaN(chr2)) {
        enc3 = enc4 = 64;
      } else if (isNaN(chr3)) {
        enc4 = 64;
      }
      output =
        output +
        _keyStr.charAt(enc1) +
        _keyStr.charAt(enc2) +
        _keyStr.charAt(enc3) +
        _keyStr.charAt(enc4);
    }
    return output;
  };

  const decodebase = (input, _keyStr) => {
    let output = '';
    let chr1, chr2, chr3;
    let enc1, enc2, enc3, enc4;
    let i = 0;
    input = input.replace(/[^A-Za-z0-9\+\/\=]/g, '');
    while (i < input.length) {
      enc1 = _keyStr.indexOf(input.charAt(i++));
      enc2 = _keyStr.indexOf(input.charAt(i++));
      enc3 = _keyStr.indexOf(input.charAt(i++));
      enc4 = _keyStr.indexOf(input.charAt(i++));
      chr1 = (enc1 << 2) | (enc2 >> 4);
      chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
      chr3 = ((enc3 & 3) << 6) | enc4;
      output = output + String.fromCharCode(chr1);
      if (enc3 != 64) {
        output = output + String.fromCharCode(chr2);
      }
      if (enc4 != 64) {
        output = output + String.fromCharCode(chr3);
      }
    }
    output = _utf8_decode(output);
    return output;
  };

  // private method for UTF-8 encoding
  const _utf8_encode = (string) => {
    string = string.replace(/\r\n/g, '\n');
    let utftext = '';
    for (let n = 0; n < string.length; n++) {
      let c = string.charCodeAt(n);
      if (c < 128) {
        utftext += String.fromCharCode(c);
      } else if (c > 127 && c < 2048) {
        utftext += String.fromCharCode((c >> 6) | 192);
        utftext += String.fromCharCode((c & 63) | 128);
      } else {
        utftext += String.fromCharCode((c >> 12) | 224);
        utftext += String.fromCharCode(((c >> 6) & 63) | 128);
        utftext += String.fromCharCode((c & 63) | 128);
      }
    }
    return utftext;
  };

  // private method for UTF-8 decoding
  const _utf8_decode = (utftext) => {
    let string = '';
    let i = 0;
    let c,
      c2,
      c3 = 0;
    while (i < utftext.length) {
      c = utftext.charCodeAt(i);
      if (c < 128) {
        string += String.fromCharCode(c);
        i++;
      } else if (c > 191 && c < 224) {
        c2 = utftext.charCodeAt(i + 1);
        string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
        i += 2;
      } else {
        c2 = utftext.charCodeAt(i + 1);
        c3 = utftext.charCodeAt(i + 2);
        string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
        i += 3;
      }
    }
    return string;
  };
}

export const transToEnum = (arr) => {
  if (!(Array.isArray(arr) && arr.length)) return {};
  return Object.fromEntries(
    arr.map(({ label, value, ...rest }) => [value === null ? '' : value, { ...rest, text: label }]),
  );
};

export function getPath(value) {
  const valueMap = {};
  const path = [];
  let current = valueMap[value];
  while (current) {
    path.unshift(current.value);
    current = current.parent;
  }
  return path;
}

// 是否紫砂易
export function isDeal() {
  return process.env.VUE_APP_NAME === 'new-tea-pi-fe'
}
export const findItemKey = (arr, val, key, searchKey = 'value' , placeholder = '') => {
  if (!(hasArrLen(arr))) return placeholder
  const findKey = (arr.find(item => item[searchKey] === val) || {})[key]
  if (!findKey && findKey !== 0) return placeholder
  return findKey
}
