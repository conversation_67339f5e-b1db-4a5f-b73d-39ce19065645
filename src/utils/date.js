import moment from "moment";

export function isDate(date) {
  return date && moment(date).isValid()
}

export function formatDate(date, fmt = 'YYYY-MM-DD', placeholder = '-') {
  if (!isDate(date)) return placeholder
  return moment(date).format(fmt)
}

export function formatNowDate(date = new Date(), fmt = 'YYYY-MM-DD') {
  return formatDate(date, fmt)
}

export function formatMd(date, fmt = 'YYYY-M-D') {
  return formatDate(date, fmt)
}

export function formatMinute(date, fmt = 'YYYY-MM-DD HH:mm') {
  return formatDate(date, fmt)
}

export function formatTime(date, fmt = 'YYYY-MM-DD HH:mm:ss') {
  return formatDate(date, fmt)
}

export function formatTimeStart(date, fmt= "YYYY-MM-DD 00:00:00") {
  return formatDate(date, fmt)
}

export function formatTimeEnd(date, fmt= "YYYY-MM-DD 23:59:59") {
  return formatDate(date, fmt)
}

export function formatNowTime(date, fmt = 'YYYY-MM-DD HH:mm:ss') {
  return formatDate(new Date(), fmt)
}

export function formatDateRange(start, end, fmt = 'YYYY-MM-DD') {
  return (isDate(start) || isDate(end)) ? `${formatDate(start, fmt, '')} ~ ${formatDate(end, fmt, '')}` : '-'
}

export function formatMinuteRange(start, end, fmt1 = 'YYYY-MM-DD HH:mm', fmt2 = 'HH:mm') {
  return (isDate(start) || isDate(end)) ? `${formatDate(start, fmt1, '')} ~ ${formatDate(end, fmt2, '')}` : '-'
}

export function formatTimeRange(start, end, fmt = 'YYYY-MM-DD HH:mm:ss') {
  return formatDateRange(start, end, fmt)
}

export function formatHmRange(start, end, fmt = 'HH:mm') {
  return formatDateRange(start, end, fmt)
}

export function formatYear(date, fmt = 'YYYY') {
  return formatDate(date, fmt)
}

export function formatMonth(date, fmt = 'MM') {
  return formatDate(date, fmt)
}

export function formatMonthDateStart(date = new Date(), fmt = 'YYYY-MM-DD') {
  return isDate(date) ? moment(date).startOf('month').format(fmt) : undefined
}

export function formatDay(date, fmt = 'DD') {
  return formatDate(date, fmt)
}

export function formatHm(date, fmt = 'HH:mm') {
  return formatDate(date, fmt)
}

export function getMoment(date = new Date(), placeholder = '-') {
  return isDate(date) ? moment(date) : placeholder
}

export function getTime(date) {
  if (!isDate(date)) return undefined
  return moment(date).valueOf()
}

export function getNowTime() {
  return new Date().valueOf()
}

export function getStartTime(date) {
  return getTime(moment(date).startOf('day'))
}

export function getEndTime(date) {
  return getTime(moment(date).endOf('day'))
}

// 判断是时间戳
export function isTimestamp(timestamp) {
  const date = new Date(timestamp);
  return !isNaN(date.getTime());
}

// 根据时间戳获取天数
export function countDays(timeStamp) {
  if (isNaN(timeStamp) || timeStamp === undefined || timeStamp === null) return ''
  if (timeStamp < 0) return 0
  const d = Math.floor(timeStamp / 1000 / 60 / 60 / 24)
  return d;
}

export function getMonthFirst(timestamp) {
  let date = isTimestamp(timestamp) ? new Date(timestamp) : new Date()
  let year = date.getFullYear()
  let month = date.getMonth()
  return moment(new Date(year, month, 1)).startOf('day').valueOf()
}

export function getMonthLast(timestamp) {
  let date = isTimestamp(timestamp) ? new Date(timestamp) : new Date()
  let year = date.getFullYear()
  let month = date.getMonth()
  return moment(new Date(year, month + 1, 0)).endOf('day').valueOf()
}

export function startDayValue(date) {
  if (!isDate(date)) {
    return undefined
  }
  return moment(date).startOf('day').valueOf()
}

export function endDayValue(date) {
  if (!isDate(date)) {
    return undefined
  }
  return moment(date).endOf('day').valueOf()
}

// 是否超过当前时间
export function isOverNow(timeStamp) {
  if (isNaN(timeStamp) || timeStamp === undefined || timeStamp === null) return false
  return timeStamp < getNowTime()
}

// 获取前一天
export function getPrevDay(date = new Date()) {
  if (!isDate(date)) return undefined
  return moment(date).subtract(1, 'days').toDate()
}
