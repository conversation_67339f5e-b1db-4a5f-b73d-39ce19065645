// 加权因子
var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
// 校验位
var parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2]

const validateIdent = {
  aIdentityCode_City: {
    // 城市代码列表
    11: '北京',
    12: '天津',
    13: '河北',
    14: '山西',
    15: '内蒙古',
    21: '辽宁',
    22: '吉林',
    23: '黑龙江 ',
    31: '上海',
    32: '江苏',
    33: '浙江',
    34: '安徽',
    35: '福建',
    36: '江西',
    37: '山东',
    41: '河南',
    42: '湖北 ',
    43: '湖南',
    44: '广东',
    45: '广西',
    46: '海南',
    50: '重庆',
    51: '四川',
    52: '贵州',
    53: '云南',
    54: '西藏 ',
    61: '陕西',
    62: '甘肃',
    63: '青海',
    64: '宁夏',
    65: '新疆',
    71: '台湾',
    81: '香港',
    82: '澳门',
    91: '国外 '
  },
  // 号码是否符合规范，包括长度，类型检查
  IdentityCode_isCardNo({ iCard, ...obj }) {
    var reg = /(^\d{15}$)|(^\d{17}(\d|X)$)/ // 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
    if (reg.test(iCard) === false) {
      obj.pass = true
      obj.tip = '身份证号码位数、格式错误'
    }
    return obj
  },
  // 取身份证前两位，省份检查
  IdentityCode_checkProvince({ iCard, ...obj }) {
    var province = iCard.substr(0, 2)
    if (validateIdent.aIdentityCode_City[province] == undefined) {
      obj.pass = true
      obj.tip = '身份证号码前两位错误'
    }
    return obj
  },
  // 出生日期检查
  IdentityCode_checkBirthday({ iCard, ...obj }) {
    var card = validateIdent.IdentityCode_changeFivteenToEighteen(iCard) // 15位转18位
    var len = card.length
    // 身份证15位时，次序为省（3位）市（3位）年（2位）月（2位）日（2位）校验位（3位），皆为数字
    // 身份证18位时，次序为省（3位）市（3位）年（4位）月（2位）日（2位）校验位（4位），校验位末尾可能为X
    // var re_fifteen = /^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/
    // var re_eighteen = /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X)$/

    obj.pass = true
    obj.tip = '身份证号码出生日期错误'

    if (len == '18') {
      var re_eighteen = /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X)$/
      var arr_data = card.match(re_eighteen) // 正则取号码内所含出年月日数据
      var year = arr_data[2]
      var month = arr_data[3]
      var day = arr_data[4]
      var birthday = new Date(year + '/' + month + '/' + day)

      obj.birthday = birthday
      obj.xbSign = parseInt(card.charAt(16)) % 2 == 0 ? '2' : '1'

      return validateIdent.IdentityCode_verifyBirthday(
        year,
        month,
        day,
        birthday,
        obj
      )
    }
    return obj
  },
  IdentityCode_verifyBirthday(year, month, day, birthday, obj) {
    var now = new Date()
    var now_year = now.getFullYear()

    // 判断年月日是否合理
    if (
      birthday.getFullYear() == year &&
      birthday.getMonth() + 1 == month &&
      birthday.getDate() == day
    ) {
      // 判断年份的范围（3岁到150岁之间)
      var age = now_year - year
      obj.ageSign = age
      if (age >= 0 && age <= 150) {
        obj.pass = false
        obj.tip = ''
      }
    }
    return obj
  },
  // 校验位检查
  IdentityCode_checkParity({ iCard, ...obj }) {
    var card = validateIdent.IdentityCode_changeFivteenToEighteen(iCard) // 15位转18位
    var len = card.length

    obj.pass = true
    obj.tip = '身份证号码校验位验证错误'
    if (len == '18') {
      var cardTemp = 0
      var i
      var valnum
      for (i = 0; i < 17; i++) {
        cardTemp += card.substr(i, 1) * factor[i]
      }
      valnum = parity[cardTemp % 11]
      if (valnum == card.substr(17, 1)) {
        obj.pass = false
        obj.tip = ''
      }
    }
    return obj
  },
  // 15位转18位身份证号
  IdentityCode_changeFivteenToEighteen(card) {
    if (card.length == '15') {
      var cardTemp = 0
      var i
      card = card.substr(0, 6) + '19' + card.substr(6, card.length - 6)
      for (i = 0; i < 17; i++) {
        cardTemp += card.substr(i, 1) * factor[i]
      }
      card += parity[cardTemp % 11]
      return card
    }
    return card
  },

  // 身份证号码检验主入口
  IdentityCodeValid(card) {
    var result = {
      iCard: card,
      pass: false,
      birthday: '',
      xbSign: '',
      ageSign: '',
      tip: ''
    }
    // 是否为空
    if (card === '') {
      result.tip = '身份证号码不能为空'
      return result
    }

    var isCardNo = validateIdent.IdentityCode_isCardNo(result)
    var checkProvince = validateIdent.IdentityCode_checkProvince(result)
    var checkBirthday = validateIdent.IdentityCode_checkBirthday(result)
    var checkParity = validateIdent.IdentityCode_checkParity(result)

    return (
      (isCardNo.pass && isCardNo) ||
      (checkProvince.pass && checkProvince) ||
      (checkBirthday.pass && checkBirthday) ||
      checkParity
    )
  }
}

export default validateIdent.IdentityCodeValid
