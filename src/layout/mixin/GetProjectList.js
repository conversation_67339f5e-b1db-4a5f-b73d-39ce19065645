import {getProjectPage} from "@/api/message/message";
export default {
  data() {
    return {
      projectList: [],
    }
  },
  methods: {
    async getProjectData() {
      const params = {
        pageNum: 1,
        pageSize: 9999,
      };
      await getProjectPage(params).then((res) => {
        if (res.code == 200) {
          this.projectList = res.data.records;
          if (this.defaultSelect) {
            this.formModel.projectKeyword = this.projectList?.[0]?.code || '';
          }
        }
      })
    },
  }
}
