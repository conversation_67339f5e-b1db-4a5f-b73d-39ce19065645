<template>
  <div>
    <div id="ali-captcha-element"></div>
  </div>
</template>

<script>
export default {
  name: 'AliyunCaptcha',
  props: {
    captchaStr: {
      type: String,
      default: ''
    },
    sceneId: {
      type: String,
      default: 'gna88ogf'
    },
    prefix: {
      type: String,
      default: 'aganp3'
    },
    buttonText: {
      type: String,
      default: '请滑动验证'
    },
    slideStyle: {
      type: Object,
      default: () => ({
        width: 350,
        height: 40
      })
    },
    language: {
      type: String,
      default: 'cn'
    },
    region: {
      type: String,
      default: 'cn'
    },
    immediate: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      captchaInstance: null
    };
  },
  mounted() {
    this.initCaptcha();
  },
  methods: {
    initCaptcha() {
      this.$emit('update:captchaStr', '')

      // 确保 AliyunCaptcha 脚本已加载
      if (typeof window.initAliyunCaptcha === 'undefined') {
        console.error('AliyunCaptcha.js script is not loaded.');
        return;
      }

      // 嵌入式
      window.initAliyunCaptcha({
        SceneId: this.sceneId,
        prefix: this.prefix,
        mode: 'embed',
        element: '#ali-captcha-element',
        button: '#ali-verify-button',
        captchaVerifyCallback: this.captchaVerifyCallback,
        onBizResultCallback: this.onBizResultCallback,
        getInstance: this.getInstance,
        slideStyle: this.slideStyle,
        language: this.language,
        immediate: this.immediate,
        region: this.region
      });
    },
    getInstance(instance) {
      this.captchaInstance = instance;
    },
    captchaVerifyCallback(captchaVerifyParam) {
      // console.log('captchaVerifyCallback', captchaVerifyParam, JSON.parse(captchaVerifyParam));
      this.$emit('update:captchaStr', captchaVerifyParam)
      document.getElementById('aliyunCaptcha-sliding-text').innerHTML = '验证成功！';
      // // 1.向后端发起业务请求，获取验证码验证结果和业务结果
      // const result = await this.$axios.post('http://您的业务请求地址', {
      //   captchaVerifyParam: captchaVerifyParam,
      //   // 添加其他业务参数
      // });
      //
      // // 2.构造标准返回参数
      // const verifyResult = {
      //   captchaResult: result.data.captchaVerifyResult, // 验证码验证是否通过，boolean类型，必选
      //   bizResult: result.data.bizResult // 业务验证是否通过，boolean类型，可选
      // };
      // return verifyResult;
    },
    onBizResultCallback(bizResult) {
      console.log('onBizResultCallback', bizResult);
      if (bizResult === true) {
        // window.location.href = 'https://www.aliyun.com/';
      } else {
        console.log('业务验证不通过！');
      }
    },
  }
};
</script>

<style scoped lang="less">
/* 添加你的样式 */
::v-deep {
  #aliyunCaptcha-sliding-wrapper #aliyunCaptcha-sliding-body.sliding {
    width: 100%;
    #aliyunCaptcha-sliding-slider {
      background-color: #1890ff;
      &.ok {
        background-color: #52C41A;
      }
    }
  }
}

</style>
