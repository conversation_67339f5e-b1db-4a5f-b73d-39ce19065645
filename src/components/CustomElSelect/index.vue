<template>
  <el-select
    v-bind="computedAttrs"
    v-on="$listeners"
    v-model="modelValue"
  >
    <template v-for="(_, slotName) in $scopedSlots" v-slot:[slotName]="slotProps">
      <slot :name="slotName" v-bind="slotProps" />
    </template>
  </el-select>
</template>

<script>
import ElSelect from 'element-ui/lib/select';

export default {
  name: 'CustomElSelect',
  components: {
    ElSelect,
  },
  props: {
    value: {
      type: [String, Number, Array, Boolean, Object],
      default: null,
    },
  },
  computed: {
    computedAttrs() {
      return {
        ...this.$attrs,
        clearable: this.$attrs.clearable !== false, // 默认设置 clearable 为 true，除非明确设置为 false
      };
    },
    modelValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      },
    },
  },
};
</script>
