<template>
  <el-dialog title="选择商品" :visible.sync="visible" @close="handleClose">
    <el-table :data="filteredGoodsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column prop="productNo" label="商品编码"></el-table-column>
      <el-table-column prop="productName" label="商品名称"></el-table-column>
      <el-table-column prop="specifications" label="规格"></el-table-column>
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'GoodsProductSelector',
  props: {
    goodsRecord: Array,
    onSelectionChange: Function,
  },
  data() {
    return {
      visible: false,
      selectedGoods: [],
    };
  },
  computed: {
    filteredGoodsList() {
      return this.goodsRecord.filter((item) => !item.selected);
    },
  },
  methods: {
    handleSelectionChange(selection) {
      this.selectedGoods = selection;
    },
    handleConfirm() {
      this.onSelectionChange(this.selectedGoods);
      this.handleClose();
    },
    handleClose() {
      this.visible = false;
    },
  },
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
