<template>
  <!-- 渲染动态表单 -->
  <el-form :model="model" inline :rules="rules" ref="form">
    <el-form-item
      v-for="column in search.columns"
      :key="column.dataIndex"
      v-if="column.search !== false"
      :label="column.title"
      :prop="column.dataIndex"
    >
      <template v-if="column.dateTime">
        <el-date-picker
          style="width: 260px;"
          v-model="model[column.dataIndex]"
          type="daterange"
          :clearable="!column.dateNoAllowClear"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </template>
      <template v-else-if="column.valueEnum">
        <el-select v-model="model[column.dataIndex]" placeholder="请选择"  v-if="!column.customEnum">
          <el-option
            v-for="(item, key) in column.valueEnum"
            :key="key"
            :label="item.text"
            :value="Number(key)"
          />
        </el-select>
        <el-select :clearable="column.allowClear?true:false" v-model="model[column.dataIndex]" placeholder="请选择"  filterable v-else>
          <el-option
            v-for="item in column.valueEnum"
            :key="item.value"
            :label="item.text"
            :value="item.value"
          />
        </el-select>
      </template>
      <template v-else>
        <el-input v-model="model[column.dataIndex]" :placeholder="column.placeholderText ? column.placeholderText : '请输入'"></el-input>
      </template>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="handleSearch">搜索</el-button>
      <el-button @click="handleReset">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  props: {
    search: {
      type: Object,
      default: () => {
        return {}
      }
    },
    rules: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    model() {
      return this.search && this.search.model ? this.search.model : {}
    }
  },
  methods: {
    handleSearch() {
      // 校验是否必填
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit('onSearch', this.model,false)
        }
      })
      // this.$emit('onSearch', this.model)
    },
    handleReset() {
      this.$refs.form.clearValidate()
      for (let key in this.model) {
        if (this.model.hasOwnProperty(key)) {
          if(key == 'times' && this.search?.columns?.find(item => item.dataIndex == 'times')?.dateNoAllowClear) {
            this.model[key] = [this.$dayjs().startOf('month').format('YYYY-MM-DD'), this.$dayjs().format('YYYY-MM-DD')]
          } else {
            this.model[key] = undefined
          }
        }
      }
      // 重置后查询
      this.$emit('onSearch', this.model, true)
    }
  }
}
</script>

<style scoped lang="scss">

</style>
