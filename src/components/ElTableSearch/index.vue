<template>
  <!-- 渲染动态表单 -->
  <el-form :model="model" inline>
    <el-form-item
      v-for="column in search.columns"
      :key="column.dataIndex"
      v-if="column.search !== false"
      :label="column.title"
    >
      <template v-if="column.valueEnum">
        <el-select v-model="model[column.dataIndex]" placeholder="请选择">
          <el-option
            v-for="(item, key) in column.valueEnum"
            :key="key"
            :label="item.text"
            :value="Number(key)"
          />
        </el-select>
      </template>
      <template v-else>
        <el-input v-model="model[column.dataIndex]" placeholder="请输入"></el-input>
      </template>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="handleSearch">搜索</el-button>
      <el-button @click="handleReset">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  props: {
    search: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    model() {
      return this.search && this.search.model ? this.search.model : {}
    }
  },
  methods: {
    handleSearch() {
      this.$emit('onSearch', this.model)
    },
    handleReset() {
      for (let key in this.model) {
        if (this.model.hasOwnProperty(key)) {
          this.model[key] = undefined
        }
      }
      this.$emit('onSearch', this.model)
    }
  }
}
</script>

<style scoped lang="scss">

</style>
