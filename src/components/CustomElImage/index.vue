<template>
  <el-image
    v-bind="$attrs"
    v-on="$listeners"
  >
    <template v-if="$slots.error" v-slot:error>
      <slot name="error"></slot>
    </template>
    <template v-else v-slot:error>
      <div class="el-image__error">
        <span>暂无图片</span>
      </div>
    </template>
    <template v-if="$slots.placeholder" v-slot:placeholder>
      <slot name="placeholder"></slot>
    </template>
<!--    <template v-if="$slots.default" v-slot:default>-->
<!--      <slot></slot>-->
<!--    </template>-->
  </el-image>
</template>

<script>
import ElImage from 'element-ui/lib/image';
export default {
  name: 'CustomElImage',
  components: {
    ElImage
  },
  methods: {
  }
};
</script>

<style scoped>
</style>
