<template>
  <el-dialog :title="title" :visible="open" width="400px" append-to-body @close="closeDialog">
    <el-upload
      ref="upload" :limit="1" accept=".xlsx, .xls" :headers="headers"
      :action="actionUrl" :disabled="isUploading" :data="data"
      :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess"
      :auto-upload="false" drag
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <div class="el-upload__tip text-center" slot="tip">
        <!--        <div class="el-upload__tip" slot="tip">-->
        <!--          <el-checkbox v-model="updateSupport" /> 是否更新已经存在的用户数据-->
        <!--        </div>-->
        <span>仅允许导入xls、xlsx格式文件。</span>
        <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
                 @click="importTemplate">下载模板
        </el-link>
      </div>
    </el-upload>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="isUploading" @click="submitFileForm">确 定</el-button>
      <el-button @click="closeDialog">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {getToken} from "@/utils/auth";

export default {
  name: 'ImportDialog',
  props: {
    open: {
      type: Boolean,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    actionUrl: {
      type: String,
      required: true
    },
    downloadUrl: {
      type: String,
      required: true
    },
    downloadName: {
      type: String,
      default: '模板'
    },
    data: {
      type: Object,
      default: () => {
        return {}
      }
    },
    onFinish: {
      type: Function,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      isUploading: false,
      updateSupport: 0,
      headers: {Authorization: "Bearer " + getToken()},
    };
  },
  methods: {
    importTemplate() {
      if (/^http(s)?:\/\//.test(this.downloadUrl)) {
        window.open(this.downloadUrl)
        return
      }
      this.download(this.downloadUrl, {}, `${this.downloadName}.xlsx`)
    },
    handleFileUploadProgress(event, file, fileList) {
      this.isUploading = true;
    },
    async handleFileSuccess(response, file, fileList) {
      console.log('response...', response)
      this.$refs.upload.clearFiles();
      // this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
      if (response.code !== 200) {
        this.isUploading = false;
        this.$message.warning(response.msg)
        return
      }
      await this.onFinish(response);
      this.isUploading = false;
      this.$message.success(response.msg || '导入成功')
      this.closeDialog();
    },
    submitFileForm() {
      this.$refs.upload.submit();
    },
    closeDialog() {
      this.$emit('update:open', false);
    }
  }
};
</script>

<style scoped>
/* 这里可以放置组件的样式 */
</style>
