<template>
  <div :class="label ? 'el-input-group select-group areafix' : ''">
    <template v-if="label">
      <span class="el-input-group__prepend">{{ label }}</span>
    </template>
    <el-cascader
      ref="areaCascader"
      clearable
      filterable
      :options="areas"
      :placeholder="placeholder || '请选择地址'"
      :props="cascaderProps"
      v-model="selectedValue"
      @change="handleAreaChange"
      style="width: 100%; min-width: 350px"
    />
  </div>
</template>

<script>
import { loadProvince, loadCity, loadDistrict, loadListByIds, loadStreet } from '@/api/AreaSelectService';
import {hasArrLen} from '@/utils/tool'

export default {
  name: 'AreaSelect',
  props: {
    value: Array,
    addressInfo: Object,
    placeholder: String,
    label: String,
    hasNoArea: Boolean,
    hasNoStreet: Boolean,
  },
  data() {
    return {
      areas: [],
      first: true,
      selectedValue: [],
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(next, old) {
        if (next && old && next.toString() === old.toString()) return
        this.handleInit()
      },
    },
  },
  created() {
    // this.handleInit()
  },
  methods: {
    handleInit() {
      this.selectedValue = this.value
      // console.log('value init', this.value)
      if (this.value && this.value[0] && this.value[1]) {
        this.initEdit(true, this.value[0], this.value[1], this.value[2]);
      } else {
        this.initProv();
      }
    },
    async loadData(selectedOption, resolve) {
      const targetOption = {...selectedOption.data || {}};
      // console.log('targetOption', selectedOption, targetOption, JSON.parse(JSON.stringify(this.areas)))
      if (this.hasNoArea && targetOption.type === 1) return;
      if (this.hasNoStreet && targetOption.type === 2) return;
      let options = []
      if (!hasArrLen(targetOption.children)) {
        try {
          switch (targetOption.type) {
            case 0:
              options = await this.initCity(targetOption.value)
              break
            case 1:
              options = await this.initDistrict(targetOption.provId, targetOption.value)
              break
            case 2:
              options = await this.initStreet(targetOption.provId, targetOption.cityId, targetOption.value)
              break
          }
        } catch (e) {
          console.error(e)
          options = []
        }
      }
      // console.log('options', options)
      this.first = false;
      resolve(options);
    },
    async initProv() {
      if (hasArrLen(this.areas)) return
      const res = await loadProvince();
      const prov = res.data.map(v => ({
        label: v.addrName,
        value: v.addrId,
        leaf: v.leafFlag,
        type: 0,
      }));
      this.areas = prov;
    },
    async initCity(provId) {
      const res = await loadCity({ addrId: provId });
      if (res.code === 200) {
        const cityList = res.data.map(v => ({
          label: v.addrName,
          value: v.addrId,
          leaf: v.leafFlag,
          type: 1,
          provId,
          cityId: v.addrId,
        }));
        if (this.hasNoArea) {
          cityList.forEach(v => {
            v.children = null;
            v.leaf = true;
          });
        }
        this.updateAreas(provId, cityList);
        return cityList
      }
    },
    async initDistrict(provId, cityId) {
      const res = await loadDistrict({ addrId: cityId });
      if (res.code === 200) {
        const districtList = res.data.map(v => ({
          label: v.addrName,
          value: v.addrId,
          leaf: v.leafFlag,
          type: 2,
          provId,
          cityId,
          districtId: v.addrId,
        }));
        if (this.hasNoStreet) {
          districtList.forEach(v => {
            v.children = null;
            v.leaf = true;
          });
        }
        this.updateAreas(provId, cityId, districtList);
        return districtList
      }
    },
    async initStreet(provId, cityId, districtId) {
      const res = await loadStreet({ addrId: districtId });
      if (res.code === 200) {
        const streetList = res.data.map(v => ({
          label: v.addrName,
          value: v.addrId,
          leaf: true,
          children: null,
          type: 3,
          provId,
          cityId,
          districtId,
        }));
        this.updateAreas(provId, cityId, districtId, streetList);
        return streetList
      }
    },
    async initEdit(isInit, provId, cityId, districtId) {
      const ids = [provId, cityId, districtId].filter(Boolean);
      if (isInit) ids.push('0');
      // if (!streetId) this.hasNoStreet = true;
      const res = await loadListByIds({ addrParentIdList: ids, leafFlag: true });
      if (res.code === 200) {
        let cityList = [];
        let districtList = [];
        let streetList = [];

        if (districtId && !this.hasNoStreet) {
          streetList = this.mapResult(res.data, districtId, 3, provId, cityId, districtId);
        }
        if (cityId) {
          districtList = this.mapResult(res.data, cityId, 2, provId, cityId);
          if (!this.hasNoStreet) {
            districtList.forEach(v => {
              v.children = streetList.filter(c => c.districtId === v.value);
            });
          }
        }
        if (provId) {
          cityList = this.mapResult(res.data, provId, 1, provId);
          cityList.forEach(v => {
            v.children = districtList.filter(c => c.cityId === v.value);
          });
        }

        if (isInit) {
          const tmp_areas = this.mapResult(res.data, '0', 0, provId, cityId);
          tmp_areas.forEach(v => {
            v.children = cityList.filter(c => c.provId === v.value);
          });
          console.log('tmp_areas', tmp_areas)
          this.areas = tmp_areas;
        } else {
          this.updateAreas(provId, cityList);
        }
      }
    },
    mapResult(result, parentId, type, provId, cityId, districtId) {
      return result.filter(v => v.addrParentId === parentId).map(v => ({
        label: v.addrName,
        value: v.addrId,
        leaf: type === 3 ? true : v.leafFlag,
        type,
        provId,
        cityId,
        districtId,
      }));
    },
    updateAreas(provId, cityId, districtId, updatedData) {
      // console.log('updateAreas', provId, cityId, districtId, updatedData)
      const tmp_areas = this.areas; // 浅拷贝 areas

      tmp_areas.forEach(province => {
        if (province.value === provId) {
          !province.children ? (Array.isArray(cityId) ? province.children = cityId : '') :
            province.children.forEach(city => {
            if (city.value === cityId) {
              !city.children ? (Array.isArray(districtId) ? city.children = districtId : '') :
              city.children.forEach(district => {
                if (district.value === districtId) {
                  district.children = updatedData; // 更新目标节点
                }
              });
            }
          });
        }
      });

      this.areas = tmp_areas; // 更新状态
      // console.log('tmp_areas', this.areas)
    },
    getAddressInfoFromNodes(nodes) {
      if (!hasArrLen(nodes)) return {};

      const addressInfo = {};
      let currentNode = nodes[0];

      // 从当前节点向上找到最顶级的节点（省）
      const stack = [];
      while (currentNode) {
        stack.push(currentNode); // 把当前节点压入栈中
        currentNode = currentNode.parent;
      }

      // 从栈顶（省）开始，依次为 province, city, area, street 赋值
      const levels = ["province", "city", "area", "street"];
      for (let i = stack.length - 1, j = 0; i >= 0 && j < levels.length; i--, j++) {
        const { label, value } = stack[i].data;
        addressInfo[levels[j]] = label;
        addressInfo[`${levels[j]}Code`] = value;
      }

      return addressInfo;
    },
    handleAreaChange(value) {
      // console.log('value', value)
      if (!hasArrLen(value)) {
        this.$emit('update:addressInfo', {
          province: '',
          provinceCode: '',
          city: '',
          cityCode: '',
          area: '',
          areaCode: '',
          street: '',
          streetCode: ''
        })
        this.$emit('update:value', [])
        return
      }
      // console.log('getCheckedNodes', this.$refs.areaCascader.getCheckedNodes())
      const nodes = this.$refs.areaCascader.getCheckedNodes()

      const addressInfo = this.getAddressInfoFromNodes(nodes);
      console.log(addressInfo);
      this.$emit('update:addressInfo', addressInfo)
      this.$emit('update:value', value)
      // console.log('addressInfo', addressInfo)
    }
  },
  computed: {
    cascaderProps() {
      return {
        // expandTrigger: 'hover',
        lazy: true,
        lazyLoad: this.loadData
      };
    },
  },
};
</script>

<style scoped>
</style>
