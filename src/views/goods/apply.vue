<template>
  <div class="apply-list p-24">
    <div class="bg-white px-32 py-24 rounded-2 flex a-center">
      <el-form :model="form" label-suffix="：" ref="form" class="el-form-inline">
        <el-row :gutter="32">
          <el-col :span="6">
            <el-form-item label="申请时间">
              <el-date-picker
                style="width: 100%;"
                v-model="form.applyDate"
                type="daterange"
                value-format="yyyy-MM-dd"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="会员ID">
              <el-input v-model="form.userId" placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="昵称/手机号">
              <el-input v-model="form.cellOrNickName" placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="单号">
              <el-input v-model="form.number" placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="shrink-0 ml-32">
        <el-button @click="reset">重置</el-button>
        <el-button type="primary" @click="getList(1)">查询</el-button>
      </div>
    </div>
    <el-card class="list-card-default mt-16" shadow="never">
      <div slot="header" class="flex a-center justify-between">
        <span class="fs-16 text-default">列表</span>
      </div>
      <el-table :data="tableData" style="width: 100%;">
        <el-table-column prop="number" label="单号" min-width="100">
        </el-table-column>
        <el-table-column prop="userInfo" label="会员" min-width="150">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image v-if="scope.row.userInfo.headPortrait" :src="scope.row.userInfo.headPortrait" fit="fill"></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.userInfo.nickName }}</div>
                <div>
                  {{ scope.row.userInfo.accountNumber }} 【{{ scope.row.userInfo.userName }}】
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="eazyGoodsPublishVO" label="商品" min-width="150">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left" v-if="(scope.row.eazyGoodsPublishVO.imgList || []).length > 0">
                <el-image :src="scope.row.eazyGoodsPublishVO.imgList[0].url" fit="fill"></el-image>
              </div>
              <div class="span_right">
                <div>{{ scope.row.eazyGoodsPublishVO.goodsName }}</div>
                <!--<div>【{{ scope.row.eazyGoodsPublishVO.id }}】</div>-->
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="count" label="数量" min-width="80">
        </el-table-column>
        <el-table-column prop="goodsPrice" label="单价" min-width="80">
          <template slot-scope="scope">¥{{ standardAmount(scope.row.eazyGoodsPublishVO.goodsPrice) }}</template>
        </el-table-column>
        <el-table-column prop="priceTotal" label="订单总价" min-width="80">
          <template slot-scope="scope">¥{{ standardAmount(scope.row.priceTotal) }}</template>
        </el-table-column>
        <el-table-column prop="applyTime" label="发生时间" min-width="100">
        </el-table-column>
        <el-table-column prop="statusValue" label="状态" min-width="80">
          <template slot-scope="scope">
            <a @click="changeStatus(scope.row)">{{scope.row.statusValue}}</a>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="120">
          <template slot-scope="scope">{{ scope.row.remark || '-' }}</template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="current"
        :limit.sync="pageSize"
        @pagination="p => getList(p.page, p.limit)"
      />
    </el-card>
    <el-dialog title="编辑" :visible.sync="dialogFormVisible">
      <el-form :model="submitForm" :rules="submitRules" ref="submitForm">
        <el-form-item label="状态" label-width="60px" prop="status">
          <el-select v-model="submitForm.status" placeholder="请选择">
            <el-option v-for="s in Object.keys(statusMap)" :key="s" :label="statusMap[s]" :value="s"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" label-width="60px" prop="remark">
          <el-input type="textarea" :rows="3" :maxlength="200" placeholder="请输入" v-model="submitForm.remark"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { pick, omit } from "lodash";
import { applyList, applyEdit, statusList } from "@/api/goods.js";
import {standardAmount} from "@/utils/index.js";
export default {
  name: "MemberCenter",
  components: {
  },
  data() {
    return {
      form: {
        userId: "",
        number: "",
        cellOrNickName: "",
        applyDate: undefined,
      },
      current: 1,
      pageSize: 10,
      total: 0,
      time: "",
      resetForm: {},
      tableData: [],
      statusMap: {},
      currentRow: {},
      dialogFormVisible: false,
      submitForm: {
        status: undefined,
        remark: ''
      },
      submitRules: {
        status: [{ required: true, message: "请选择状态", trigger: "change" }],
        remark: []
      }
    };
  },
  mounted() {
    this.getStatusList();
    this.reset();
  },
  methods: {
    standardAmount,
    getStatusList() {
      statusList().then(({data}) => {
        this.statusMap = data;
      });
    },
    // 分页接口
    getList(pageNum, pageSize) {
      if (pageNum) {
        this.current = pageNum;
      }
      if (pageSize) {
        this.pageSize = pageSize;
      }
      const params = {
        ...omit(this.form, 'applyDate'),
        current: this.current,
        pageSize: this.pageSize,
      };
      if (this.form.applyDate) {
        params.applyStartDate = this.form.applyDate[0] + ' 00:00:00';
        params.applyEndDate = this.form.applyDate[1] + ' 23:59:59';
      }
      applyList(params).then(({data}) => {
        this.tableData = data.records;
        this.total = data.total;
      });
    },

    // 重置
    reset() {
      this.form = {
        userId: "",
        number: "",
        cellOrNickName: "",
        applyDate: undefined,
      };
      this.getList(1);
    },
    changeStatus(row) {
      this.currentRow = row;
      this.submitForm = pick(row, 'status', 'remark');
      this.dialogFormVisible = true;
    },
    submit() {
      this.$refs["submitForm"].validate((valid) => {
        if (valid) {
          const params = {
            ...this.submitForm,
            ...pick(this.currentRow, 'id', 'goodsId')
          };
          applyEdit(params).then(() => {
            this.$message.success('操作成功');
            this.dialogFormVisible = false;
            this.getList();
          });
        }
      });
    }
  },
};
</script>
<style lang="scss" scoped>
.apply-list {
  min-height: calc(100vh - 84px);
  background-color: #F0F2F5;
}
</style>
