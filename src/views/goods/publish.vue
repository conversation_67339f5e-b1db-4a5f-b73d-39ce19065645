<template>
  <div class="goods-publish p-24">
    <el-card class="list-card-default" shadow="never">
      <div slot="header" class="flex a-center justify-between">
        <span class="fs-16 text-default">基本信息</span>
      </div>
      <el-form ref="form" :model="form" :rules="rules" label-suffix="：">
        <el-row :gutter="32">
          <el-col :span="24">
            <el-form-item label="所属项目" label-width="100px" prop="projectName">
              <el-input
                v-model="form.projectName"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品名称" label-width="100px" prop="goodsName">
              <el-input
                :maxlength="40"
                v-model="form.goodsName"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="商品价格"
              label-width="100px"
              prop="goodsPrice"
            >
              <el-input-number
                size="small"
                placeholder="请输入"
                v-model="form.goodsPrice"
                controls-position="right"
                :min="0"
                :precision="2"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="商品图片" label-width="100px" prop="imgList">
              <el-upload
                :action="uploadUrl"
                list-type="picture-card"
                :limi="10"
                :on-success="handleImgSuccess"
                :before-upload="beforeUpload"
                :file-list="form.imgList"
                :on-preview="handlePictureCardPreview"
                :on-remove="handleRemove"
              >
                <i class="el-icon-plus"></i>
              </el-upload>
              <el-dialog :visible.sync="dialogVisible">
                <img width="100%" :src="dialogImageUrl" alt="" />
              </el-dialog>
              <div>建议尺寸：800*800px，单张大小不超过2M，最多可上传10张</div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="商品标签" label-width="100px" prop="tips">
              <el-tag
                v-for="(tag, index) in form.tips"
                :key="index"
                closable
                :disable-transitions="false"
                @close="handleClose(tag, index)"
              >
                {{ tag }}
              </el-tag>
              <el-input
                :class="`${form.tips.length > 0 ? 'ml-8' : ''}`"
                style="width: 100px;"
                v-if="inputVisible"
                v-model="inputValue"
                ref="saveTagInput"
                size="small"
                @keyup.enter.native="handleInputConfirm"
                @blur="handleInputConfirm"
              >
              </el-input>
              <el-button
                v-else
                :class="`${form.tips.length > 0 ? 'ml-8' : ''}`"
                icon="el-icon-plus"
                @click="showInput"
                >新增标签
              </el-button>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="壶型" label-width="100px" prop="pot">
              <el-input v-model="form.pot" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="泥料" label-width="100px" prop="pug">
              <el-input v-model="form.pug" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="容量" label-width="100px" prop="capacity">
              <el-input v-model="form.capacity" placeholder="请输入">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="等级" label-width="100px" prop="level">
              <el-input v-model="form.level" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="茶叶" label-width="100px" prop="tea">
              <el-input v-model="form.tea" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工艺" label-width="100px" prop="craft">
              <el-input v-model="form.craft" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="作者" label-width="100px" prop="author">
              <el-input v-model="form.author" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职称" label-width="100px" prop="professional">
              <el-input v-model="form.professional" placeholder="请输入">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="商品详情"
              label-width="100px"
              prop="introduction"
              class="hightedit"
            >
              <editor
                v-model="form.introduction"
                ref="editor"
                :min-height="192"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="text-center">
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </div>
    </el-card>
  </div>
</template>
<script>
import { getToken } from "@/utils/auth";
import { pick, omit } from "lodash";
import { add, edit, goodsDetail } from "@/api/goods.js";

export default {
  name: "GoodsPublish",
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + "/pic/upload", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      form: {
        goodsName: "",
        projectName: "",
        goodsPrice: undefined,
        pot: "",
        pug: "",
        capacity: "",
        level: "",
        tea: "",
        craft: "",
        author: "",
        professional: "",
        introduction: "",
        imgList: [],
        tips: [],
      },
      rules: {
        projectName: [
          {
            required: true,
            message: "请选择所属项目",
            trigger: "blur",
            whitespace: true,
          },
        ],
        goodsName: [
          {
            required: true,
            message: "请输入商品名称",
            trigger: "blur",
            whitespace: true,
          },
        ],
        goodsPrice: [
          { required: true, message: "请输入商品价格", trigger: "blur" },
        ],
        imgList: [
          { required: true, message: "请选择商品图片", trigger: "change" },
        ],
        introduction: [
          {
            required: true,
            message: "请输入商品详情",
            trigger: "blur",
            whitespace: true,
          },
        ],
      },
      dialogImageUrl: "",
      dialogVisible: false,
      inputVisible: false,
      inputValue: "",
    };
  },
  mounted() {
    if (!!this.$route.query.goodsId) {
      this.getDetail();
    }
  },
  methods: {
    handleClose(tag, index) {
      let tips = this.form.tips;
      console.log(tag);
      console.log(index);
      tips.splice(index, 1);
      this.form.tips = [...tips];
    },

    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },

    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        this.form.tips.push(inputValue);
      }
      this.inputVisible = false;
      this.inputValue = "";
    },
    getDetail() {
      goodsDetail({ goodsId: this.$route.query.goodsId }).then((res) => {
        this.form = Object.assign(
          pick(
            res,
            "goodsName",
            "projectName",
            "goodsPrice",
            "pot",
            "pug",
            "capacity",
            "level",
            "tea",
            "craft",
            "author",
            "professional",
            "introduction",
            "imgList"
          ),
          {
            tips: (res.tips || "").split(",").filter((t) => !!t),
          }
        );
      });
    },
    handleRemove(file, fileList) {
      this.form.imgList = fileList;
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleImgSuccess(res) {
      if (res.status == "done") {
        this.form.imgList.push({
          name: res.name.substring(res.name.lastIndexOf("/") + 1),
          url: res.name,
        });
      }
    },
    beforeUpload(file) {
      const type = ["image/jpeg", "image/jpg", "image/png", "image/svg"];
      const isJPG = type.includes(file.type);
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error(`图片格式错误!`);
        return false;
      }
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过2MB!");
        return false;
      }
      return isJPG && isLt2M;
    },
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const params = {
            goodsPrice: this.form.goodsPrice,
            imgList: (this.form.imgList || []).map((item) =>
              pick(item, "name", "url")
            ),
            tips: (this.form.tips || []).join(","),
          };
          Object.keys(omit(this.form, "imgList", "goodsPrice", "tips")).forEach(
            (key) => {
              params[key] = (this.form[key] || "").trim();
            }
          );
          if (!!this.$route.query.goodsId) {
            params.id = this.$route.query.goodsId;
            edit(params).then((res) => {
              this.$message.success("编辑成功");
              this.$router.push("/goods/goodsList");
            });
          } else {
            add(params).then((res) => {
              this.$message.success("新增成功");
              this.$router.push("/goods/goodsList");
            });
          }
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.goods-publish {
  min-height: calc(100vh - 84px);
  background-color: #f0f2f5;

  .el-tag {
    height: 32px;
    line-height: 32px;
  }

  .el-tag + .el-tag {
    margin-left: 8px;
  }
}
</style>
