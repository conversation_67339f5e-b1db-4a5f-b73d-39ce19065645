<template>
  <div class="goods-list p-24">
    <div class="bg-white px-32 py-24 rounded-2 flex a-center">
      <el-form :model="form" label-suffix="：" ref="form" class="el-form-inline">
        <el-row :gutter="32">
          <el-col :span="6">
            <el-form-item label="商品名">
              <el-input v-model="form.goodsName" placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="作者">
              <el-input v-model="form.author" placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="价格">
              <el-row :gutter="8">
                <el-col :span="12">
                  <el-input-number size="small" placeholder="最低价" v-model="form.minPrice" controls-position="right"
                                   :min="0" :max="form.maxPrice" :precision="2"></el-input-number>
                </el-col>
                <el-col :span="12">
                  <el-input-number size="small" placeholder="最高价" v-model="form.maxPrice" controls-position="right"
                                   :min="form.minPrice || 0" :precision="2"></el-input-number>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="状态">
              <el-select
                v-model="form.status"
                placeholder="请选择"
                style="width: 100%;"
                size="small"
              >
                <el-option label="上架" :value="1"></el-option>
                <el-option label="下架" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="shrink-0 ml-32">
        <el-button @click="reset">重置</el-button>
        <el-button type="primary" @click="getList(1)">查询</el-button>
      </div>
    </div>
    <el-card class="list-card-default mt-16" shadow="never">
      <div slot="header" class="flex a-center justify-between">
        <span class="fs-16 text-default">列表</span>
        <el-button type="primary" icon="el-icon-plus" @click="link2edit()">新建</el-button>
      </div>
      <el-table :data="tableData" style="width: 100%;">
        <el-table-column prop="seq" label="显示顺序" min-width="80">
          <template slot-scope="scope">
            <el-input v-model="scope.row.seq" placeholder="请输入" size="small"
                      @blur="e => updateRowSeq(e, scope.row)"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="imgList" label="图片" min-width="80">
          <template slot-scope="scope">
            <el-image v-if="(scope.row.imgList || []).length > 0" :src="scope.row.imgList[0].url" fit="fill"
                      style="height: 40px"></el-image>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="goodsName" label="商品名" min-width="100"></el-table-column>
        <el-table-column prop="author" label="作者" min-width="80"></el-table-column>
        <el-table-column prop="goodsPrice" label="价格" min-width="100">
          <template slot-scope="scope">¥{{ standardAmount(scope.row.goodsPrice) }}</template>
        </el-table-column>
        <el-table-column prop="applySales" label="实际销量" min-width="100"></el-table-column>
        <el-table-column prop="waterSales" label="注水销量" min-width="100">
          <template slot-scope="scope">
            <el-input v-model="scope.row.waterSales" placeholder="请输入" size="small"
                      oninput="if(value){value=value.replace(/[^\d]/g,1)} if(value<0){value=0} if(value>99999){value=99999}"
                      @blur="e => updateRowWaterSales(e, scope.row)"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" min-width="80">
          <template slot-scope="scope">
            <div v-if="scope.row.status === 1" class="status status-blue">
              <span class="status-dot"></span>
              <span class="status-text">上架</span>
            </div>
            <div v-else class="status status-gray">
              <span class="status-dot"></span>
              <span class="status-text">下架</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="{ row }">
            <a @click="link2edit(row.id)">编辑</a>
            <a @click="updateRowStatus(row, 0)" v-if="row.status === 1">下架</a>
            <a @click="updateRowStatus(row, 1)" v-else>上架</a>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="current"
        :limit.sync="pageSize"
        @pagination="p => getList(p.page, p.limit)"
      />
    </el-card>
  </div>
</template>
<script>
import {queryAll, updateSeq, updateStatus, updateWaterSales} from "@/api/goods.js";
import {standardAmount} from "@/utils/index.js";

export default {
  name: "GoodsList",
  components: {},
  data() {
    return {
      form: {
        goodsName: "",
        author: "",
        minPrice: undefined,
        maxPrice: undefined,
        status: null,
      },
      current: 1,
      pageSize: 10,
      total: 0,
      time: "",
      resetForm: {},
      tableData: [],
      firstLoad: true, // 是否是第一次加载
    };
  },
  mounted() {
    this.reset()
  },
  methods: {
    standardAmount,
    // 分页接口
    getList(pageNum, pageSize) {
      if (pageNum) {
        this.current = pageNum;
      }
      if (pageSize) {
        this.pageSize = pageSize;
      }
      const params = {
        ...this.form,
        current: this.current,
        pageSize: this.pageSize,
      };
      queryAll(params).then(({data}) => {
        this.tableData = data.records;
        this.total = data.total;
      });
    },
    // 重置
    reset() {
      this.form = {
        goodsName: "",
        author: "",
        minPrice: undefined,
        maxPrice: undefined,
        status: null,
      };
      this.getList(1);
    },

    updateRowSeq(e, row) {
      if (!!e.target.value) {
        if (!/^\d+$/.test(e.target.value)) {
          this.$message.warning('只可输入整数');
          row.seq = ''
          return
        }
        updateSeq({
          goodsId: row.id,
          seq: e.target.value,
        }).then((res) => {
          this.$message.success(res.msg);
          this.getList()
        });
      } else {
        this.$message.warning('显示顺序不可为空');
      }
    },
    updateRowWaterSales(e, row) {
      if (!!e.target.value) {
        if (!/^\d+$/.test(e.target.value)) {
          this.$message.warning('只可输入整数');
          row.waterSales = ''
          return
        }
        updateWaterSales({
          goodsId: row.id,
          waterSales: e.target.value,
        }).then((res) => {
          this.$message.success(res.msg);
          this.getList()
        });
      } else {
        this.$message.warning('注水销量不可为空');
      }
    },

    // 禁用
    updateRowStatus(row, status) {
      this.$confirm(`是否确认${status == 1 ? '上架' : '下架'}该商品?`, "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          updateStatus({goodsId: row.id}).then((res) => {
            this.$message.success(res.msg);
            this.getList();
          });
        })
    },

    // 新增/编辑
    link2edit(id) {
      this.$router.push(`/goods/goodsPublish${id ? ('?goodsId=' + id) : ''}`);
    },
  },
};
</script>
<style lang="scss" scoped>
.goods-list {
  min-height: calc(100vh - 84px);
  background-color: #F0F2F5;
}
</style>
