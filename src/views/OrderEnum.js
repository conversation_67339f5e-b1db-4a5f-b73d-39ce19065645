import { transToEnum } from "@/utils/utils";

// 订单来源
const orderSourceOptions = [
  { value: null, label: '全部' },
  { value: 'PLATFORM', label: '紫砂街平台订单' },
  { value: 'WAN_MI', label: '万米商城订单' },
]

const channelOptions = [
  { value: 'teapighgo132l', label: '中禾文化APP' },
  { value: 'teapimlopqaksj', label: '中禾文化APP(提货)' },
  { value: 'teapik2msksap', label: '中禾文化APP(预约销售)' },
  { value: 'teapips9snasm', label: '中禾文化APP(自主销售)' },
]

const orderSourceEnum = transToEnum(orderSourceOptions)

// 订单状态
const orderStatusOptions = [
  { label: '全部', value: null },
  { label: '待支付', value: 'NOT_PAY' },
  { label: '待发货', value: 'NOT_YET_SHIPPED' },
  { label: '部分发货', value: 'PART_SHIPPED' },
  { label: '已发货', value: 'SHIPPED' },
  { label: '已完成', value: 'COMPLETED' },
  { label: '已作废', value: 'VOID' },
]

const orderStatusEnum = transToEnum(orderStatusOptions);

// 退单状态
const returnOrderStatusOptions = [
  { label: '全部', value: null },
  { label: '已提交', value: 'INIT' },
  { label: '未发货', value: 'UNDELIVERED' },
  { label: '待放款', value: 'NOT_PAY' },
  { label: '待受理', value: 'PROCESSING' },
  { label: '退款失败', value: 'FAILED' },
  { label: '已完成', value: 'FINISH' },
  { label: '已作废', value: 'VOID' },
]

const returnOrderStatusEnum = transToEnum(returnOrderStatusOptions);

// 退单审核状态
const returnOrderAuditStatusOptions = [
  { label: '待审核', value: 'TOCHECK' },
  { label: '审核通过', value: 'SUCCESS' },
  { label: '审核不通过', value: 'FAIL' },
]

const returnOrderAuditStatusEnum = transToEnum(returnOrderAuditStatusOptions);

export {
  orderSourceOptions,
  channelOptions,
  orderSourceEnum,
  orderStatusOptions,
  orderStatusEnum,
  returnOrderStatusOptions,
  returnOrderStatusEnum,
  returnOrderAuditStatusOptions,
  returnOrderAuditStatusEnum,
}
