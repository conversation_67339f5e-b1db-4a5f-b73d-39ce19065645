<template>
  <div class="whole">
    <el-tabs v-model="tabKey">
      <el-tab-pane label="订单详情" name="1">
        <OrderInfo :TradeDetail="TradeDetail" @refresh="init" />
      </el-tab-pane>
      <el-tab-pane label="发货记录" name="2">
        <OrderDelivery
          :TradeDetail="TradeDetail"
          @saveTradeDelivery="saveTradeDelivery"
          @sureSelfPickup="sureSelfPickup"
          @saveTradeDeliveryCancel="saveTradeDeliveryCancel"
        />
      </el-tab-pane>
<!--      <el-tab-pane label="支付记录" name="3">-->
<!--        <OrderPay :TradeDetail="TradeDetail"/>-->
<!--      </el-tab-pane>-->
    </el-tabs>

    <div class="mt-16 text-center">
      <el-button type="primary" @click="$router.go(-1)">返回</el-button>
    </div>
  </div>
</template>

<script>
import { getTradeDetail, loadTradeDelivery, loadTradeDeliveryCancel, loadTradeSelfTake } from '@/api/OrderService';
import OrderInfo from "@/views/order/OrderInfo.vue";
import OrderDelivery from "@/views/order/OrderDelivery.vue";
import OrderPay from "@/views/order/OrderPay.vue";

export default {
  components: {
    OrderInfo,
    OrderDelivery,
    OrderPay
  },
  data() {
    return {
      tabKey: '1',
      TradeDetail: {}
    };
  },
  created() {
    this.init();
  },
  methods: {
    async sureSelfPickup(goods) {
      const {id} = this.$route.query;
      const param = {
        tradeId: id,
        shippingItems: [
          {
            num: goods.deliveringNum,
            tradeGoodsId: goods.id,
            productCodes: []
          }
        ]
      };
      const res = await loadTradeSelfTake(param);
      if (res.code === 200) {
        this.$message.success('自提成功');
        this.init();
      } else {
        this.$message({
          message: res.message,
          type: 'error'
        });
      }
    },
    init() {
      const { id, key } = this.$route.query;
      if (id) {
        this.getTradeDetail({ tradeId: id });
      }
      if (key) {
        this.tabKey = key;
      }
    },
    async getTradeDetail(params) {
      const response = await getTradeDetail(params);
      this.TradeDetail = response.data;
    },
    async saveTradeDelivery(goods, record) {
      if(record.sendManner == '0'){
        const { id } = this.$route.query;
        const param = {
          ...record,
          deliveryTime: `${this.$dayjs(record.deliveryTime).format('YYYY-MM-DD')} 00:00:00`,
          tradeId: id,
          shippingItems: [
            {
              num: goods.deliveringNum,
              tradeGoodsId: goods.id,
              productCodes: record.yhymIdList
            }
          ]
        };
        const res = await loadTradeDelivery(param);
        if (res.code === 200) {
          this.$message.success('处理成功');
          this.init();
        } else {
          this.$message.error(res.message);
        }
      }else {
        this.sureSelfPickup(goods);
      }

    },
    async saveTradeDeliveryCancel(record) {
      const { id } = this.$route.query;
      const param = {
        tradeId: id,
        deliveryId: record.id
      };
      const res = await loadTradeDeliveryCancel(param);
      if (res.code === 200) {
        this.$message.success('处理成功');
        this.init();
      } else {
        this.$message.error(res.message);
      }
    }
  }
};
</script>

<style scoped lang="less">
</style>
