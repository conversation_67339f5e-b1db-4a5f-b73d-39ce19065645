<template>
  <div>
    <el-card>
      <h2 slot="header" class="header">退单详情</h2>
      <el-row>
        <el-col :span="24">
          <span class="tradeStatus">{{ refund.statusValue }}</span>
        </el-col>
        <el-col :span="24" style="margin-top: 10px;">
          <el-descriptions>
            <el-descriptions-item :label="tradeTitle('退单号')">
              <span class="tradeValue">{{ refund.id }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="tradeTitle('客户')">
              <span class="tradeValue">{{ refund.buyerName }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="tradeTitle('客户账号')">
              <span class="tradeValue">{{ refund.buyerUsername }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="tradeTitle('申请时间')">
              <span class="tradeValue">{{ refund.createTime }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="tradeTitle('订单号')">
              <span class="tradeValue">{{ refund.tradeId }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
        <el-col :span="24">
          <el-table :data="refundGoods" border style="width: 100%">
<!--            <el-table-column-->
<!--              prop="goodsSkuNo"-->
<!--              label="SKU编码"-->
<!--              align="center"-->
<!--            >-->
<!--            </el-table-column>-->
            <el-table-column
              prop="goodsSkuName"
              label="商品名称">
            </el-table-column>
            <el-table-column
              prop="num"
              label="数量"
              align="center"
            >
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="24" style="text-align: right;">
          <span class="priceTitle">应退金额：</span>
          <span class="priceValue">￥{{ refund.refundPrice }}</span>
        </el-col>
        <el-col :span="24" style="text-align: right;">
          <span class="priceTitle">实退金额：</span>
          <span class="priceValue">￥{{ refund.realRefundPrice }}</span>
        </el-col>
        <el-col :span="24" style="text-align: right;">
          <span class="priceTitle">手续费：</span>
          <span class="priceValue">￥{{ refundForTradeTOBDetail.tradeServiceFee || 0 }}</span>
        </el-col>
        <el-col :span="24" class="tradeInfo">
          <el-descriptions :column="1">
            <el-descriptions-item label="退货原因">
              {{ refund.refundReason || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="退货说明">
              {{ refund.refundRemark || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="退货方式">
              {{ refund.refundWayValue || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="退货地址">
              {{ refund.receiveAddress || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="退货附件">
              <div v-if="refund.refundFiles && refund.refundFiles.length">
                <el-image
                  v-for="item in refund.refundFiles"
                  :key="item"
                  :src="item"
                  :preview-src-list="refund.refundFiles"
                  :style="{ width: '100px' }">
                </el-image>
              </div>
              <div v-else>-</div>
            </el-descriptions-item>
            <el-descriptions-item label="物流信息">
              <div v-if="refundDeliveryEvent">
                物流公司：{{ refundDeliveryEvent.logisticCompanyName }}<br>
                物流单号：{{ refundDeliveryEvent.logisticNo }}
                <Logistics v-if="refundDeliveryEvent" :companyInfo="refundDeliveryEvent" :userDeliveryAddress="userDeliveryAddress" />
              </div>
              <div v-else>-</div>
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
        <el-col :span="24" style="margin-top: 20px; margin-bottom: 16px;">
          <span class="logTitle">操作日志</span>
        </el-col>
        <el-col :span="24" style="margin-bottom: 16px;">
          <el-table :data="logs" border style="width: 100%">
            <el-table-column
              prop="operatorName"
              label="操作人"
              align="center"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.operatorName || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="createTime"
              label="时间"
              align="center"
              width="220"
            >
            </el-table-column>
            <el-table-column
              prop="content"
              label="操作日志">
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </el-card>
    <div class="bar-button text-center">
      <el-button type="primary" @click="goBack">返回</el-button>
    </div>
  </div>
</template>

<script>
import Logistics from './LogisticPopover.vue';
import {loadRefundDetail} from "@/api/OrderService";
import {mathFunc} from "@/utils/math";

export default {
  name: 'RefundDetail',
  components: {
    Logistics,
  },
  data() {
    return {
      refundForTradeTOBDetail: {}
    };
  },
  computed: {
    refund() {
      return this.refundForTradeTOBDetail.refund || {};
    },
    refundGoods() {
      return this.refundForTradeTOBDetail.refundGoods || [];
    },
    refundDeliveryEvent() {
      return this.refundForTradeTOBDetail.refundDeliveryEvent;
    },
    logs() {
      return this.refundForTradeTOBDetail.logs || [];
    },
    userDeliveryAddress(){
      return {consigneePhone: this.refundForTradeTOBDetail.consigneePhone};
    }
  },
  methods: {
    mathFunc,
    goToOrderList() {
      this.$router.push('/order/orderList');
    },
    goToReturnList() {
      this.$router.push('/order/return');
    },
    goBack() {
      this.$router.go(-1);
    },
    tradeTitle(title) {
      return <span class="tradeTitle">{title}</span>;
    },
  },
  async created() {
    const { id } = this.$route.query;
    if (id) {
      const res = await loadRefundDetail({ id });
      this.refundForTradeTOBDetail = res.data;
    }
  },
};
</script>

<style scoped lang="less">
.tradeStatus {
  margin-right: 8px;
  color: #52C41A;
  font-weight: 400;
  font-size: 18px;
}

.tradeTitle {
  color: #333;
  font-weight: 400;
  font-size: 14px;
}

.tradeValue {
  color: #666;
  font-weight: 400;
  font-size: 14px;
}

.priceTitle {
  color: rgba(0,0,0,0.65);
  font-weight: 400;
  font-size: 14px;
}

.priceValue {
  color: #333;
  font-weight: 500;
  font-size: 18px;
}

.tradeInfo ::v-deep .el-descriptions-row > th,
.tradeInfo ::v-deep .el-descriptions-row > td {
  padding-bottom: 0;
}

.logTitle {
  color: #333;
  font-weight: 500;
  font-size: 16px;
}

.bar-button {
  margin-top: 16px;
}
</style>
