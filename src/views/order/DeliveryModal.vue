<template>
  <el-dialog :visible="modalVisible" title="发货" @close="modalClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="发货方式" prop="sendManner">
        <el-radio-group v-model="form.sendManner">
          <el-radio label="0">快递物流</el-radio>
          <el-radio label="1">自提</el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="form.sendManner === '0'">
        <el-form-item label="物流公司" prop="logisticCompanyId">
          <el-select v-model="form.logisticCompanyId" placeholder="请选择物流公司" @change="logisticCompanyOnSelect">
            <el-option
              v-for="company in ExpressCompanyList"
              :key="company.expressCompanyId"
              :label="company.expressName"
              :value="company.expressCompanyId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="物流单号" prop="logisticNo">
          <el-input v-model="form.logisticNo" placeholder="请输入物流单号" />
        </el-form-item>
        <el-form-item label="发货日期" prop="deliveryTime">
          <el-date-picker v-model="form.deliveryTime" placeholder="请选择发货日期" style="width: 100%"  @change="e => form.deliveryTime = e" />
        </el-form-item>
      </template>

<!--      <el-form-item label="一壶一码">-->
<!--        <GoodsProductSelector-->
<!--          :goodsRecord="goodsRecord"-->
<!--          @change="addGoodsProduct"-->
<!--        >-->
<!--          <el-input type="textarea" :rows="4" :value="yhymCode" placeholder="请选择一壶一码" />-->
<!--        </GoodsProductSelector>-->
<!--      </el-form-item>-->
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="modalClose">取消</el-button>
      <el-button type="primary" @click="handleOK">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import GoodsProductSelector from '@/components/GoodsProductSelector';
import {loadExpressCompanyList} from "@/api/OrderService";

export default {
  name: 'DeliveryModal',
  components: { GoodsProductSelector },
  props: {
    modalVisible: Boolean,
    goodsRecord: Object,
    goodsSelfTake: Number,
  },
  data() {
    return {
      ExpressCompanyList: [],
      form: {
        logisticCompanyId: '',
        logisticNo: '',
        deliveryTime: '',
        yhym: [],
        yhymIdList: [],
        sendManner: '0',
      },
      yhymCode: '',
      rules: {
        logisticCompanyId: [{ required: true, message: '请选择物流公司', trigger: 'change' }],
        logisticNo: [{ required: true, message: '请输入物流单号', trigger: 'blur' }],
        deliveryTime: [{ required: true, message: '请选择发货日期', trigger: 'change' }],
        sendManner: [{ required: true, message: '请选择发货方式', trigger: 'change' }],
      },
    };
  },
  watch: {
    modalVisible(next) {
      if (next) {
        this.form = this.$options.data().form
        this.getExpressCompanyList()
      }
    }
  },
  methods: {
    async getExpressCompanyList() {
      const res = await loadExpressCompanyList()
      this.ExpressCompanyList = res.data;
    },
    logisticCompanyOnSelect(value) {
      const company = this.ExpressCompanyList.find(c => c.expressCompanyId === value);
      if (company) {
        this.form.logisticCompanyName = company.expressName;
      }
    },
    addGoodsProduct(list) {
      const codeList = list.map(item => item.relatedCode);
      this.form.yhymIdList = codeList;
      this.form.yhym = list;
      this.yhymCode = codeList.join('，');
    },
    handleOK() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.$emit('sendData', { ...this.form });
          this.modalClose();
        }
      });
    },
    modalClose() {
      this.$emit('closeDeliveryModal', false);
      this.$refs.formRef.resetFields();
    },
  },
};
</script>

<style scoped lang="less">
</style>
