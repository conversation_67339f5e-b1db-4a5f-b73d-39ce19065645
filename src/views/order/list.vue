<template>
  <div :class="!readOnly ? 'full-content-box' : ''">
    <div class="bg-white px-32 py-24 rounded-2 flex a-center">
      <el-form :model="searchForm" ref="form">
        <el-row :gutter="32">
          <el-col :span="8">
            <el-form-item label="买家ID/昵称/手机号/姓名" label-width="180px">
              <el-input v-model="searchForm.keyword"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="订单编号" label-width="110px">
              <el-input v-model="searchForm.tradeId" placeholder="订单编号" style="width: 100%"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收货人手机" label-width="110px">
              <el-input v-model="searchForm.consigneeCellphone" placeholder="收货人手机"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="下单日期" label-width="110px">
              <el-date-picker
                v-model="searchForm.createTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属机构ID/昵称/手机号/姓名" label-width="190px">
              <el-input v-model="searchForm.referrerKeyword"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属项目" label-width="82px" prop="projectName">
              <el-input v-model="searchForm.projectName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="" label-width="24px">
              <el-button type="primary" @click="handlePagination({ page: 1, limit: 10 })">搜索</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-card class="list-card-default mt-16" shadow="never">
      <div slot="header" class="flex a-center justify-between">
        <span class="fs-16 text-default">订单列表</span>

        <el-radio-group class="ml-auto mr-16" v-model="tabKey" @change="onTabChange">
          <el-radio-button v-for="option in orderStatusOptions" :key="option.value" :label="option.value">
            {{ option.label }}
          </el-radio-button>
        </el-radio-group>

        <el-button @click="exportFile">导出</el-button>
      </div>
      <el-table :data="tableData" :loading="loading" border stripe>
      <el-table-column prop="tradeId" label="订单编号" min-width="130" />
        <el-table-column prop="lotOrderId" label="凭证订单号" min-width="130">
          <template slot-scope="scope">
            {{ scope.row.lotOrderId || '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="lotOrderId" label="凭证收购状态" min-width="130">
          <template slot-scope="scope">
            {{ scope.row.lotOrderTradeStatus || '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="lotOrderId" label="订单来源" min-width="130">
          <template slot-scope="scope">
            {{ scope.row.channelValue || '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="goods" label="商品" width="240">
          <template slot-scope="scope">
            <el-row  type="flex" justify="space-between">
              <el-col>
                <div :size="16">
                  <div :size="24" class="flex">
<!--                    <span>{{ scope.row.id }}</span>-->
<!--                    <el-tag type="info">{{ scope.row.channelValue }}</el-tag>-->
                    <img width="50px" height="50px" v-for="(v, k) in scope.row.goodsInfoImgList || []" :key="k" :src="v || defaultImg" v-if="k < 3" />
                    <img width="50px" height="50px" v-if="scope.row.goodsInfoImgList && scope.row.goodsInfoImgList.length === 0" :src="defaultImg" />
                    <div class="mx-8" v-if="scope.row.goodsList && scope.row.goodsList[0]">
                      {{ scope.row.goodsList[0].goodsSkuName }}
                    </div>
                    <router-link class="flex-none ml-auto" v-if="!readOnly" :to="`/order/detail?id=${scope.row.tradeId}`">
                      <span style="line-height: 50px">详情</span>
                    </router-link>
                  </div>
                  <div :size="4">
                    下单时间：{{ scope.row.createTime }}
                  </div>
                </div>
              </el-col>
            </el-row>
            <div class="rowBorderLeft" />
<!--            <div class="rowBorderBottom" />-->
            <div class="colImg">
              <img
                v-for="(item, index) in scope.row.tradeTOBGoods"
                :key="index"
                :src="item.goodsSpuImage || defaultImg"
                class="goods-image"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="projectName" label="所属项目" width="200"/>
        <el-table-column prop="buyerName" label="买家" min-width="280">
          <template slot-scope="scope">
            <div class="span_all cursor-pointer"  @click="jumpMemberCenter(scope.row.buyerUserId)">
              <div class="span_left">
                <el-image
                  v-if="scope.row.buyerHeadPortrait"
                  :src="scope.row.buyerHeadPortrait"
                  fit="fill"
                ></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.buyerNickName }} <span>({{ scope.row.buyerUserId}})</span></div>
                <div>{{ scope.row.buyerUserName }} 【{{ scope.row.buyerName }}】</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="referrerName" label="所属机构" min-width="280">
          <template slot-scope="scope">
            <div class="span_all cursor-pointer" @click="jumpMemberCenter(scope.row.referrerUserId)">
              <div class="span_left">
                <el-image
                  v-if="scope.row.referrerHeadPortrait"
                  :src="scope.row.referrerHeadPortrait"
                  fit="fill"
                ></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.referrerNickName }} <span>({{ scope.row.referrerUserId}})</span> </div>
                <div>
                  {{ scope.row.referrerUserName }} 【{{
                    scope.row.referrerName
                  }}】
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
      <el-table-column prop="consigneeName" label="收货人" width="150">
        <template slot-scope="scope">
          <div>
            {{ scope.row.consigneeName }}
            <div>{{ scope.row.consigneeCellphone }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="payPrice" label="金额/数量" width="140">
        <template slot-scope="scope">
          <div>
            ￥{{ scope.row.payPrice }}
            <div>({{ scope.row.num }}件)</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="statusValue" label="订单状态" width="100">
        <template slot-scope="scope">
          <div>
            {{ scope.row.statusValue }}
            <div class="rowBorderRight"></div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="60" fixed="right" v-if="noLimit">
        <template slot-scope="{ row }">
          <el-button v-if="['NOT_PAY'].includes(row.status) && !isLot(row.channel)" type="text" @click="handleCancel(row)">
            取消
          </el-button>
        </template>
      </el-table-column>
<!--      <el-table-column label="操作">-->
<!--        <template slot-scope="scope">-->
<!--          <el-button type="primary" v-if="scope.row.canDelivery===true" size="mini" plain @click="goToDetail(scope.row.tradeId, '2')">发货</el-button>-->
<!--          <el-button type="primary" size="mini" plain @click="goToDetail(scope.row.tradeId)">详情</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
    </el-table>
      <pagination
        v-show="pagination.total > 0"
        :total="pagination.total"
        :page.sync="pagination.current"
        :limit.sync="pagination.pageSize"
        @pagination="handlePagination"
      />
    </el-card>
  </div>
</template>

<script>
import { orderStatusOptions } from '../OrderEnum';
import { loadTradeOfflinePay, loadLinkPayOrder, getTradePage } from '@/api/OrderService';
import { loadPlatformAddressTree } from '@/api/AreaSelectService';
import {getPath} from "@/utils/utils";
import defaultImg from '@/assets/images/defaultImg.png';
import {goodsOrderCancel} from '@/api/transferAssets/tradingMarket'
import JumpMemberCenter from "@/layout/mixin/JumpMemberCenter";
import {isLot} from '@/utils/business'
import {mapGetters} from 'vuex'
import dayjs from 'dayjs'

export default {
  name: 'OrderList',
  components: {
  },
  mixins: [JumpMemberCenter],
  props: {
    readOnly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      defaultImg,
      tableData: [],
      loading: false,
      tabKey: null,
      payModalVisible: false,
      prePayModalVisible: false,
      payRecord: {},
      AllChannels: [],
      areaList: [],
      searchForm: {
        keyword: '',
        tradeId: '',
        channel: '',
        consigneeCellphone: '',
        // 默认查询近一周
        createTime: [
          dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD')
        ],
        referrerKeyword: '',
        projectName: '',
      },
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  computed: {
    ...mapGetters(['noLimit']),
    orderStatusOptions() {
      return orderStatusOptions;
    },
  },
  methods: {
    isLot,
    handleParams() {
      return {
        ...this.searchForm,
        size: this.pagination.pageSize,
        current: this.pagination.current,
        createTimeStart: this.searchForm.createTime ? `${this.searchForm.createTime[0]} 00:00:00` : null,
        createTimeEnd: this.searchForm.createTime ? `${this.searchForm.createTime[1]} 23:59:59` : null,
        status: this.tabKey,
      }
    },
    async onSearch() {
      this.loading = true;
      const params = this.handleParams();

      try {
        const res = await getTradePage(params);
        this.tableData = res.data.records;
        this.pagination.total = res.data.total;
      } catch (e) {
      }

      this.loading = false;
    },
    onReset() {
      this.searchForm = this.$options.data().searchForm;
      this.handlePagination({ page: 1, limit: 10 });
    },
    async settingArea(value) {
      this.areaList = getPath(value);
    },
    async onTabChange(value) {
      this.tabKey = value;
      await this.onSearch();
    },
    goToDetail(tradeId, key = 1) {
      this.$router.push(`/order/detail?id=${tradeId}&key=${key}`);
    },
    handlePagination(pagination) {
      this.pagination.current = pagination.page;
      this.pagination.pageSize = pagination.limit;
      this.onSearch();
    },
    async init() {
      this.onSearch();
      // const res = await loadPlatformAddressTree();
      // if (res.code === 200) {
      //   this.treeData = res.data;
      // }
    },
    // 导出
    exportFile() {
      this.download(
        "/zishajie/trade/export",
        this.handleParams(),
        `订单报表.xlsx`,
        {
          headers: { 'Content-Type': 'application/json' },
        }
      );
    },

    handleCancel(row) {
      this.$confirm("确定取消吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        await goodsOrderCancel({ tradeId: row.tradeId })
        this.onSearch();
        this.$message({
          type: "success",
          message: "取消成功!",
        });
      });
    },
  },
  created() {
    // readOnly 场景（如交易市场 Tab 中）不自动加载，避免首次进入即调用接口
    if (!this.readOnly) {
      this.init();
    }
  },
};
</script>

<style scoped lang="less">
.el-form-item {
  ::v-deep .el-form-item__content {
    flex: 1;
  }
}
.el-date-editor.el-range-editor.el-input__inner.el-date-editor--daterange {
  width: 100%;
}
</style>
