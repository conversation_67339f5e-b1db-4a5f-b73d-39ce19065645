<template>
  <el-card>
    <el-row>
      <el-col :span="24">
        <el-descriptions :column="1">
          <el-descriptions-item label="订单号">
            <span class="trade-value">{{ tradeTOB.id }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </el-col>
      <el-col :span="24">
        <el-table :data="dataList" border size="small">
<!--          <el-table-column prop="goodsSkuNo" label="SKU编码" width="240" />-->
          <el-table-column prop="goodsSkuName" label="商品名称" />
          <el-table-column prop="specs" label="规格" width="120" />
          <el-table-column
            prop="price"
            label="单价"

            :formatter="row => `￥${row.price}`"
          />
          <el-table-column prop="num" label="数量" />
          <el-table-column prop="hasDeliveryNum" label="已发货数" />
          <el-table-column label="本次发货数" width="180">
            <template slot-scope="scope">
              <el-input-number
                style="width: 140px"
                :min="0"
                :max="scope.row.num - scope.row.hasDeliveryNum"
                v-model="scope.row.deliveringNum"
                :disabled="true"
                @change="val => deliveringNumOnChange(val, scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button
                v-if="canDelivery && scope.row.hasDeliveryNum < scope.row.num"
                type="primary"
                @click="changeDeliveryModal(scope.row, true)"
              >
                发货
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="24" style="margin-top: 8px;">
        <el-divider orientation="left">发货记录</el-divider>
      </el-col>
      <el-col :span="24" style="margin-top: 8px;">
        <el-table
          :data="deliveryEvents"
          border
          size="small"
          :expand-row-keys="['id']"
          :row-key="record => record.id"
        >
          <el-table-column prop="id" label="发货单编号" width="200" />
          <el-table-column prop="logisticNo" label="物流号" width="200">
            <template slot-scope="scope">
              <template v-if="scope.row.logisticCompanyName == '自提'">
                -
              </template>
              <template v-else>
                {{ scope.row.logisticNo }}
                <Logistics
                  :userDeliveryAddress="userDeliveryAddress"
                  :companyInfo="scope.row"
                  :deliveryTime="scope.row.deliveryTime ? scope.row.deliveryTime.substring(0, 10) : null"
                />
              </template>

            </template>
          </el-table-column>
          <el-table-column prop="logisticCompanyName" label="物流公司" />
          <el-table-column prop="deliveryTime" label="发货时间" width="220">
            <template slot-scope="scope">
              {{ scope.row.deliveryTime ? scope.row.deliveryTime.substring(0, 10) : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="operatorName" label="操作人" width="120" />
          <el-table-column prop="status" label="状态" width="120">
            <template slot-scope="scope">
              {{ scope.row.status === 'N' ? '已作废' : '已发货' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button
                v-if="canCancelDelivery && scope.row.status !== 'N'"
                type="text"
                @click="saveTradeDeliveryCancel(scope.row)"
              >
                作废
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <DeliveryModal
      :modalVisible="deliveryModalVisible"
      @closeDeliveryModal="closeDeliveryModal"
      @sendData="sendData"
      :goodsSelfTake="TradeDetail.goodsSelfTake"
      :goodsRecord="goodsRecord"
    />
  </el-card>
</template>

<script>
import DeliveryModal from './DeliveryModal.vue';
import Logistics from './LogisticPopover.vue';

export default {
  components: {
    DeliveryModal,
    Logistics
  },
  props: {
    TradeDetail: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      deliveryModalVisible: false,
      goodsRecord: {},
      dataList: null
    };
  },
  computed: {
    canDelivery() {
      return this.TradeDetail.canDelivery;
    },
    canCancelDelivery() {
      return this.TradeDetail.canCancelDelivery;
    },
    tradeTOB() {
      return this.TradeDetail.tradeTOB || {};
    },
    goods() {
      return this.TradeDetail.goods || [];
    },
    deliveryEvents() {
      return this.TradeDetail.deliveryEvents || [];
    },
    userDeliveryAddress(){
      return {consigneePhone: this.TradeDetail.consigneePhone};
    }
  },
  watch: {
    goods: {
      immediate: true,
      handler(newGoods) {
        this.dataList = newGoods.map(item => ({ ...item, deliveringNum: item.num - item.hasDeliveryNum }));
      }
    }
  },
  methods: {
    deliveringNumOnChange(val, record) {
      const list = this.dataList.map(item => {
        if (item.id === record.id) {
          item.deliveringNum = val;
        }
        return item;
      });
      this.dataList = list;
    },
    changeDeliveryModal(record, visible) {
      if (record.deliveringNum <= 0) {
        this.$message({
          message: '请填写发货数量',
          type: 'warning'
        });
      } else {
        this.deliveryModalVisible = visible;
        this.goodsRecord = record;
      }
    },
    closeDeliveryModal(visible) {
      this.deliveryModalVisible = visible;
      this.goodsRecord = {};
    },
    sendData(param) {
      this.$emit('saveTradeDelivery', this.goodsRecord, param);
    },
    saveTradeDeliveryCancel(param) {
      this.$emit('saveTradeDeliveryCancel', param);
    }
  }
};
</script>
