<template>
  <el-dialog :visible="modalVisible" :close-on-click-modal="false" title="修改地址" @close="modalClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="所在地区" prop="selectAddress">
        <AreaSelect
          ref="areaSelect"
          :value.sync="form.selectAddress"
          :addressInfo.sync="addressInfo"
          placeholder="请选择所在地区"
        ></AreaSelect>
      </el-form-item>
      <el-form-item label="详细地址" prop="deliveryAddress">
        <el-input v-model="form.deliveryAddress" placeholder="请输入详细地址" />
      </el-form-item>
      <el-form-item label="收货人" prop="consigneeName">
        <el-input v-model="form.consigneeName" placeholder="请输入收货人" />
      </el-form-item>
      <el-form-item label="手机号码" prop="consigneeCellphone">
        <el-input v-model="form.consigneeCellphone" placeholder="请输入手机号码" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="modalClose">取消</el-button>
      <el-button type="primary" :loading="saving" @click="handleOK">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>

import AreaSelect from '@/components/AreaSelect/index.vue'
import {loadTradeUpdate} from '@/api/OrderService'

export default {
  name: 'ModifyAddress',
  components: {AreaSelect},
  data() {
    return {
      modalVisible: false,
      record: {},
      form: {
        deliveryAddress: '',
        consigneeName: '',
        consigneeCellphone: '',
        selectAddress: undefined,
      },
      addressInfo: {
        province: null,
        provinceCode: null,
        city: null,
        cityCode: null,
        area: null,
        areaCode: null,
        street: null,
        streetCode: null,
      },
      rules: {
        selectAddress: [{ required: true, message: '请选择所在地区', trigger: 'blur' }],
        deliveryAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
        consigneeName: [{ required: true, message: '请输入收货人', trigger: 'blur' }],
        consigneeCellphone: [{ required: true, message: '请输入手机号码', trigger: 'blur' }],
      },
      saving: false,
    };
  },
  methods: {
    openDialog(record) {
      this.modalVisible = true
      const {deliveryAddress, consigneeName, consigneeCellphone, province, provinceCode, city, cityCode, area, areaCode, street, streetCode} = record;
      this.record = record
      this.addressInfo = {
        province,
        provinceCode,
        city,
        cityCode,
        area,
        areaCode,
        street,
        streetCode,
      }
      this.form = {
        deliveryAddress,
        consigneeName,
        consigneeCellphone,
        selectAddress: [provinceCode, cityCode, areaCode, streetCode],
      };
    },
    handleOK() {
      this.$refs.formRef.validate(async valid => {
        if (valid) {
          this.saving = true
          const { selectAddress, ...rest } = this.form

          try {
            await loadTradeUpdate({
              tradeId: this.record.tradeId,
              ...this.addressInfo,
              userDeliveryAddress: {
                ...rest,
                ...this.addressInfo,
              }
            })
            this.$emit('refresh')
            this.modalClose()
          } catch (e) {
          }

          this.saving = false
        }
      });
    },
    modalClose() {
      this.modalVisible = false
      // this.$refs.formRef.resetFields();
    },
  },
};
</script>

<style scoped lang="less">
</style>
