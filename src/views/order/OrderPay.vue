<template>
  <el-card>
    <el-row>
      <el-col :span="24">
        <el-descriptions :column="1">
          <el-descriptions-item label="订单号">
            <span class="font-bold">{{ tradeTOB.id }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </el-col>
      <el-col :span="24">
        <el-table :data="payOrders" border size="small">
          <el-table-column prop="businessId" label="支付编号" align="center" width="230" />
          <el-table-column prop="payUserName" label="支付客户" align="center" width="150" />
          <el-table-column prop="payUserUsername" label="支付账号" align="center" width="150" />
          <el-table-column
            prop="price"
            label="支付金额"
            align="center"
            :formatter="row => `￥${row.price}`"
          />
          <el-table-column
            prop="finishTime"
            label="支付完成时间"
            align="center"
            width="220"
            :formatter="row => (row.finishTime ? row.finishTime : '-')"
          />
          <el-table-column prop="payWayValue" label="支付方式" align="center" />
          <el-table-column prop="payFile" label="附件" align="center">
            <template slot-scope="scope">
              <el-link v-if="scope.row.payFile" @click="downLoadFile(scope.row.payFile)">下载</el-link>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="statusValue" label="状态" align="center" width="130" />
        </el-table>
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
export default {
  props: {
    TradeDetail: {
      type: Object,
      required: true
    }
  },
  computed: {
    tradeTOB() {
      return this.TradeDetail.tradeTOB || {};
    },
    payOrders() {
      return this.TradeDetail.payOrders || [];
    }
  },
  methods: {
    downLoadFile(url) {
      window.location.href = url;
    }
  }
};
</script>
