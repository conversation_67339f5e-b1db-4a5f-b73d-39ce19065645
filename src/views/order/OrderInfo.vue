<template>
  <el-card>
    <el-row>
      <el-col :span="24">
        <span class="tradeStatus">{{ tradeTOB.statusValue }}</span>
        <el-tag type="info">{{ tradeTOB.channelValue }}</el-tag>
        <el-tag type="warning" style="margin-left: 5px;">{{ tradeTOB.saleTypeValue }}</el-tag>
      </el-col>
      <el-col :span="24" style="margin-top: 10px;">
        <el-descriptions>
          <el-descriptions-item :label="createLabel('订单号')">
            <span class="tradeValue">{{ tradeTOB.id }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="createLabel('客户')">
            <span class="tradeValue">{{ tradeTOB.buyerName }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="createLabel('客户账号')">
            <span class="tradeValue">{{ tradeTOB.buyerUsername }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="createLabel('下单时间')">
            <span class="tradeValue">{{ tradeTOB.createTime }}</span>
          </el-descriptions-item>
<!--          <el-descriptions-item :label="createLabel('地区')">
            <span class="tradeValue">{{ tradeTOB.agencyArea }}</span>
          </el-descriptions-item>-->
        </el-descriptions>
      </el-col>
      <el-col :span="24">
        <el-table :data="TradeDetail.goods" border size="small">
<!--          <el-table-column prop="goodsSkuNo" label="SKU编码" align="center" width="180" />-->
          <el-table-column prop="goodsSkuNo" label="链上地址" align="center" width="280">
            <template >
              -
            </template>
          </el-table-column>
          <el-table-column prop="goodsSkuName" label="商品名称" align="center" width="150"  />
<!--          <el-table-column prop="brandName" label="品牌" align="center" width="150" />-->
<!--          <el-table-column prop="specs" label="规格" align="center" />-->
          <el-table-column
            prop="price"
            label="单价"
            align="center"
            :formatter="(row) => `￥${row.price}`"
          />
          <el-table-column prop="num" label="数量" align="center"  />
          <el-table-column
            label="金额小计"
            align="center"
            :formatter="(row) => `￥${(row.num * row.price).toFixed(2)}`"
          />
        </el-table>
      </el-col>
      <el-col :span="24" style="text-align: right;">
        <span class="priceTitle">商品金额：</span>
        <span class="priceValue">￥{{ tradeTOB.allPrice }}</span>
      </el-col>
      <el-col :span="24" style="text-align: right;">
        <span class="priceTitle">配送费用：</span>
        <span class="priceValue">￥{{ tradeTOB.deliveryPrice }}</span>
      </el-col>
      <el-col :span="24" style="text-align: right;" v-if="tradeTOB && tradeTOB.channel == 'teapimlopqaksj'">
        <span class="priceTitle">提货金额：</span>
        <span class="priceValue">￥{{ TradeDetail.shouldPrice || 0 }}</span>
      </el-col>
      <el-col :span="24" style="text-align: right;">
        <span class="priceTitle">手续费：</span>
        <span class="priceValue">￥{{ (tradeTOB.properties && tradeTOB.properties.serviceFee) || 0 }}</span>
      </el-col>
      <el-col :span="24" style="text-align: right;">
        <span class="priceTitle">合计总额：</span>
        <span class="priceValue">￥{{ TradeDetail.totalPrice || 0 }}</span>
      </el-col>
      <el-col :span="24" class="tradeInfo">
        <el-descriptions :column="1">
          <el-descriptions-item label="卖家备注">{{ tradeTOB.remark }}</el-descriptions-item>
          <el-descriptions-item label="买家备注">{{ tradeTOB.buyerRemark }}</el-descriptions-item>
          <el-descriptions-item label="订单附件">
            <template v-if="tradeTOB.files && tradeTOB.files.length > 0">
              <el-image
                v-for="file in tradeTOB.files"
                :key="file"
                :src="file"
                :preview-src-list="[file]"
                width="100"
              />
            </template>
            <template v-else>-</template>
          </el-descriptions-item>
          <el-descriptions-item label="发票信息">不需要发票</el-descriptions-item>
          <el-descriptions-item label="配送方式">{{TradeDetail.goodsSelfTake == 0 ? '快递':'自提'}}</el-descriptions-item>

          <el-descriptions-item :label="TradeDetail.goodsSelfTake == 0 ? '收货信息' : '提货地址'">
            {{ userDeliveryAddress.consigneeName }}&nbsp;&nbsp;
            {{ userDeliveryAddress.consigneeCellphone }}&nbsp;&nbsp;
            {{ userDeliveryAddress.province }}
            {{ userDeliveryAddress.city }}
            {{ userDeliveryAddress.area }}
            {{ userDeliveryAddress.street }}
            {{ userDeliveryAddress.deliveryAddress }}
            <el-button v-if="tradeTOB.statusValue === '待发货'" class="ml-8" type="text" @click="handleModifyAddress">修改地址</el-button>
          </el-descriptions-item>
        </el-descriptions>
      </el-col>
      <el-col :span="24" style="margin-top: 20px; margin-bottom: 16px;">
        <span class="logTitle">操作日志</span>
      </el-col>
      <el-col :span="24" style="margin-bottom: 16px;">
        <el-table :data="TradeDetail.logs" border size="small">
          <el-table-column prop="operatorName" label="操作人" width="120" />
          <el-table-column prop="createTime" label="时间" width="220" />
          <el-table-column prop="content" label="操作日志" />
        </el-table>
      </el-col>
    </el-row>
    <ModifyAddress  ref="ModifyAddress" @refresh="$emit('refresh')" />
  </el-card>
</template>

<script>
import ModifyAddress from "@/views/order/cmps/ModifyAddress.vue";
export default {
  name: 'OrderInfo',
  components: { ModifyAddress },
  props: {
    TradeDetail: {
      type: Object,
      default() {
        return {
          tradeTOB: {files: []},
          userDeliveryAddress: { files: [] },
          logs: [],
          goods: [],
        };
      },
    },
  },
  computed: {
    tradeTOB() {
      return this.TradeDetail.tradeTOB || {files: []};
    },
    userDeliveryAddress() {
      return this.TradeDetail.userDeliveryAddress || {files: []};
    }
  },
  methods: {
    createLabel(label) {
      return <span class='tradeTitle'>{label}</span>
    },
    handleModifyAddress() {
      this.$refs.ModifyAddress.openDialog(this.userDeliveryAddress)
    },
  },
};
</script>

<style scoped lang="less">
</style>
