<template>
  <el-dialog
    :visible.sync="rejectRefundModalVisible"
    title="请填写拒绝退款原因"
    :before-close="modalClose"
    :close-on-click-modal="false"
  >
    <el-form ref="formRef" :model="model" :rules="rules">
      <el-form-item label="拒绝退款原因" prop="remark">
        <el-input type="textarea" v-model="model.remark"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="modalClose">取消</el-button>
      <el-button type="primary" @click="handleOK">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    rejectRefundModalVisible: Boolean,
    refundId: String,
    onRefundRejectPay: Function,
    changeRejectRefundModal: Function,
  },
  data() {
    return {
      rules: {
        remark: [
          { required: true, message: '请输入拒绝退款原因', trigger: 'blur' },
        ],
      },
      model: {
        remark: '',
      },
    };
  },
  methods: {
    handleOK() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.onRefundRejectPay({
            refundId: this.refundId,
            ...this.model,
          });
          this.modalClose();
        }
      });
    },
    modalClose() {
      this.changeRejectRefundModal(null, false);
      this.remark = '';
      this.$refs.formRef.resetFields();
    },
  },
};
</script>
