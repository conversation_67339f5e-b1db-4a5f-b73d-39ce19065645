<template>
  <el-popover
    trigger="click"
    width="600"
    v-model="visible"
    popper-class="custom-popover"
  >
    <div class="order-delivery" style="max-width: 560px;">
      <div class="order-delivery-head clearfix">
        <ul style="padding:0">
          <li>
            物流公司：
            {{ companyInfo.logisticCompanyName || '无' }}
          </li>
          <li>
            物流单号：
            {{ companyInfo.logisticNo || '无' }}
          </li>
          <!-- <li>发货时间：{{ deliveryTime || '无' }}</li> -->
        </ul>
      </div>
      <template v-if="logistics.length > 0">
        <el-timeline :reverse="true">
          <el-timeline-item
            v-for="(item, index) in displayLogistics"
            :key="index"
            :timestamp="item.time"
            placement="top"
          >
            {{ item.context }}
          </el-timeline-item>
          <el-timeline-item v-if="showMore" placement="top">
            <div>
              以下为最新跟踪信息
              <a @click="viewAll">查看全部</a>
            </div>
          </el-timeline-item>
        </el-timeline>
      </template>
      <template v-else>
        <div style="text-align: center">暂无物流信息</div>
      </template>
    </div>
    <a slot="reference" v-if="companyInfo && companyInfo.logisticCompanyId" @click="showLogistics(companyInfo)">实时物流</a>
  </el-popover>
</template>

<script>
import { loadExpressByDeliveryInfos } from '@/api/OrderService';

export default {
  name: 'LogisticPopover',
  props: {
    companyInfo: {
      type: Object,
      required: true
    },
    deliveryTime: {
      type: String,
      default: null
    },
    userDeliveryAddress: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      logistics: [],
      showMore: false,
      showAll: false,
      visible: false
    };
  },
  computed: {
    displayLogistics() {
      return this.showAll ? this.logistics : this.logistics.slice(0, 3);
    }
  },
  methods: {
    async showLogistics(logistics) {
      this.showAll = false;
      const res = await loadExpressByDeliveryInfos({
        companyCode: logistics.logisticCompanyId,
        deliveryNo: logistics.logisticNo,
        phone: this.userDeliveryAddress?.consigneePhone || ''
      });
      if (res.code === 200) {
        this.showMore = res.data.length > 3;
        this.logistics = res.data;
        this.visible = true;
      }
    },
    viewAll() {
      this.showAll = true;
      this.showMore = false;
    }
  }
};
</script>
