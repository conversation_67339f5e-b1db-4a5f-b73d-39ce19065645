<template>
  <el-dialog
    :visible.sync="refundModalVisible"
    title="退款"
    :before-close="modalClose"
    @closed="resetFields"
  >
    <el-alert
      type="info"
      description="点击确认后，平台将按照确认的金额将款项返还给客户。"
      show-icon
      style="margin-bottom: 16px;"
    />
    <el-form ref="formRef" :model="refundRecord">
      <el-form-item label="退款金额" prop="refundPrice">
        <el-input-number :value="totalRefundPrice" :disabled="true" placeholder="请输入退款金额"></el-input-number>
      </el-form-item>
      <el-form-item label="退款备注" prop="remark">
        <el-input type="textarea" v-model="remark" placeholder="请输入退款备注"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="modalClose">取消</el-button>
      <el-button type="primary" @click="handleOK">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>

export default {
  props: {
    refundRecord: Object,
    refundModalVisible: Boolean,
    onRefundPay: Function,
    changeRefundModal: Function,
  },
  data() {
    return {
      remark: '',
    };
  },
  computed: {
    totalRefundPrice() {
      return this.refundRecord.refundPrice;
    }
  },
  watch: {
    refundModalVisible(val) {
      if (val) {
        this.$nextTick(() => {
          if (!this.refundModalVisible) return;
          this.refundRecord = { ...this.refundRecord };
        });
      }
    },
  },
  methods: {
    handleOK() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.onRefundPay({
            refundId: this.refundRecord.refundId,
            refundPrice: this.refundRecord.refundPrice,
            remark: this.remark,
          });
          this.modalClose();
        }
      });
    },
    modalClose() {
      this.changeRefundModal({}, false);
      this.remark = '';
      this.resetFields();
    },
    resetFields() {
      this.$refs.formRef.resetFields();
    },
  },
};
</script>
