<template>
  <div>
    <el-card>
      <el-alert
        title="操作提示"
        type="info"
        description="
          1、订单设置关联了订单退单处理的关键流程，请谨慎操作，所有设置在点击保存后生效。
          2、客户逾期未处理的待收货订单，将会自动确认收货。
          3、超过设定时间的已完成订单，客户将无法发起退货退款申请。"
      ></el-alert>

      <setting-form :configs="configs" :editConfigs="editConfigs" />

      <div :authority="['deal_order_setting_update']">
        <div class="bar-button text-center">
          <el-button type="primary" @click="saveSetting">保存</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { loadTradeSetting, modifyTradeSetting } from "@/api/OrderService";
import SettingForm from "./SettingForm.vue";

export default {
  components: {
    SettingForm,
  },
  data() {
    return {
      configs: {},
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      loadTradeSetting().then((res) => {
        if (res.code === 200) {
          this.configs = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    editConfigs(key, value) {
      this.configs[key] = value;
    },
    saveSetting() {
      modifyTradeSetting(this.configs).then((res) => {
        if (res.code === 200) {
          this.$message.success("处理成功");
          this.init();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    navigateToOrderList() {
      this.$router.push('/order/list');
    },
  },
};
</script>

<style scoped lang="less">
</style>
