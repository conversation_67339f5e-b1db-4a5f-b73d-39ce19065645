<template>
  <div class="full-content-box">
    <div class="bg-white px-32 py-24 rounded-2 flex a-center">
      <el-form :model="searchForm" label-suffix="：" ref="form" type="flex" align="middle" class="el-form-inline">
        <el-row :gutter="32">
          <el-col :span="7">
            <el-form-item label="退单编号">
              <el-input v-model="searchForm.refundId" placeholder="订单编号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="订单编号">
              <el-input v-model="searchForm.tradeId" placeholder="订单编号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="申请日期">
              <el-date-picker
                v-model="searchForm.createTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="" label-width="25px">
              <el-button type="primary" @click="handlePagination({ page: 1, limit: 10 })">搜索</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <el-card class="list-card-default mt-16" shadow="never">
      <div slot="header" class="flex a-center justify-between">
        <span class="fs-16 text-default">退单列表</span>
        <el-radio-group v-model="tabKey" @change="onTabChange">
          <el-radio-button v-for="option in returnOrderStatusOptions" :key="option.value" :label="option.value">
            {{ option.label }}
          </el-radio-button>
        </el-radio-group>
      </div>

        <el-table :data="tableData" v-loading="loading" border stripe>
          <el-table-column prop="id" label="退单编号" v-if="false" />
          <el-table-column prop="tradeId" label="订单编号" v-if="false" />
          <el-table-column prop="createTime" label="申请日期" v-if="false" />

          <el-table-column label="商品" width="300">
            <template slot-scope="scope">
              <div>
                <el-row class="" justify="space-between">
                  <div class="imgAndRefundId">
                    <el-space class="colImg">
                      <img v-for="(v, k) in scope.row.goodsInfoImgList || []" :key="k" :src="v || defaultImg" v-if="k < 3" />
                      <img v-if="scope.row.goodsInfoImgList && scope.row.goodsInfoImgList.length === 0" :src="defaultImg" />
                    </el-space>
                    <div style="line-height: 40px">{{ scope.row.refundId }}</div>
                  </div>

                  <div class="flex justify-end gap-16">
                    <div v-if="scope.row.canAudit" :authority="['deal_order_return_audit']">
                      <a @click="showAuditModal(scope.row)">审核</a>
                      <a @click="changeRejectModal(scope.row.refundId, true)">驳回</a>
                    </div>
                    <div v-if="scope.row.canPay" :authority="['deal_order_return_refund']">
                      <a @click="changeRefundModal(scope.row, true)">退款</a>
                    </div>
                    <div v-if="scope.row.canRejectPay" :authority="['deal_order_return_reject']">
                      <a @click="changeRejectRefundModal(scope.row.refundId, true)">拒绝退款</a>
                    </div>
                    <div :authority="['deal_order_return_detail']">
                      <router-link :to="`/order/returnDetail?id=${scope.row.refundId}`">详情</router-link>
                    </div>
                  </div>
                </el-row>
                <div class="rowBorderLeft" />
<!--                <div class="rowBorderBottom" />-->

              </div>
            </template>
          </el-table-column>

          <el-table-column prop="tradeId" label="订单号" width="200" />
          <el-table-column prop="createTime" label="申请时间" width="200" />
<!--          <el-table-column prop="buyerName" label="会员" width="200" />-->
          <el-table-column prop="buyerName" label="会员" width="240">
            <template slot-scope="scope">
              <div class="span_all">
                <div class="span_left">
                  <el-image
                    v-if="scope.row.buyerHeadPortrait"
                    :src="scope.row.buyerHeadPortrait"
                    fit="fill"
                  ></el-image>
                  <img
                    v-else
                    src="@/assets/images/member.png"
                    alt="donate"
                    width="100%"
                  />
                </div>
                <div class="span_right">
                  <div>{{ scope.row.buyerNickName }}<span>({{ scope.row.buyerUserId}})</span></div>
                  <div>
                    {{ scope.row.buyerUserName }} 【{{ scope.row.buyerName }}】
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="refundPrice" label="应退金额">
            <template slot-scope="scope">
              <div>￥{{ scope.row.refundPrice }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="realRefundPrice" label="实退金额">
            <template slot-scope="scope">
              <div v-if="scope.row.realRefundPrice">￥{{ scope.row.realRefundPrice }}</div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <el-table-column prop="statusValue" label="退单状态" width="110">
            <template slot-scope="scope">
              <div>{{ scope.row.statusValue }}</div>
              <div class="rowBorderRight"></div>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="pagination.total > 0"
          :total="pagination.total"
          :page.sync="pagination.current"
          :limit.sync="pagination.pageSize"
          @pagination="handlePagination"
        />
    </el-card>

    <reject-modal
      :refund-id="refundId"
      :reject-modal-visible="rejectModalVisible"
      :changeRejectModal="changeRejectModal"
      :onAuditStatus="onAuditStatus"
    />
    <reject-refund-modal
      :refund-id="refundId"
      :reject-refund-modal-visible="rejectRefundModalVisible"
      :changeRejectRefundModal="changeRejectRefundModal"
      :onRefundRejectPay="onRefundRejectPay"
    />
    <refund-modal
      :refundRecord="refundRecord"
      :refundModalVisible="refundModalVisible"
      :changeRefundModal="changeRefundModal"
      :onRefundPay="onRefundPay"
    />
  </div>
</template>

<script>
import { returnOrderStatusOptions } from '../OrderEnum';
import {
  loadRefundTradeAudit,
  loadRefundTradePay,
  loadRefundRejectPay,
  getRefundTradePage,
} from '@/api/OrderService';
import { loadPlatformAddressTree } from '@/api/AreaSelectService';
import RejectModal from "@/views/order/RejectModal.vue";
import RejectRefundModal from "@/views/order/RejectRefundModal.vue";
import RefundModal from "@/views/order/RefundModal.vue";

import defaultImg from '@/assets/images/defaultImg.png';
import {mathFunc} from "@/utils/math";

const valueMap = {};

function loops(list, parent) {
  return (list || []).map(({ children, value }) => {
    const node = (valueMap[value] = {
      parent,
      value,
    });
    node.children = loops(children, node);
    return node;
  });
}

function getPath(value) {
  const path = [];
  let current = valueMap[value];
  while (current) {
    path.unshift(current.value);
    current = current.parent;
  }
  return path;
}

export default {
  name: 'OrderReturn',
  components: {
    RejectModal,
    RejectRefundModal,
    RefundModal,
  },
  data() {
    return {
      defaultImg,
      tableData: [],
      loading: false,
      tabKey: null,
      rejectModalVisible: false,
      refundId: null,
      rejectRefundModalVisible: false,
      refundRecord: {},
      refundModalVisible: false,
      searchForm: {
        id: '',
        tradeId: '',
        createTime: '',
        searchForm: ''
      },
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  computed: {
    returnOrderStatusOptions() {
      return returnOrderStatusOptions;
    },
  },
  methods: {
    mathFunc,
    async loadTreeData() {
      const res = await loadPlatformAddressTree();
      if (res.code === 200) {
        this.treeData = res.data;
        loops(this.treeData);
      }
    },
    async onSearch() {
      this.loading = true;
      const { current, pageSize } = this.pagination;
      const { searchForm: {createTime, ...formRest}, areaList, tabKey } = this;
      const params = {
        size: pageSize,
        current,
        ...formRest,
        startCreateTime: createTime ? `${createTime[0]} 00:00:00` : null,
        endCreateTime: createTime ? `${createTime[1]} 23:59:59` : null,
        status: tabKey,
        // provinceCode: areaList[0] || null,
        // cityCode: areaList[1] || null,
        // areaCode: areaList[2] || null,
      };

      try {
        const res = await getRefundTradePage(params)
        if (res.code === 200) {
          this.tableData = res.data.records
          this.pagination.total = res.data.total
        }
      } catch (e) {
      }

      this.loading = false;
    },
    onReset() {
      this.searchForm = this.$options.data().searchForm;
      this.handlePagination({ page: 1, limit: 10 });
    },
    async onTabChange(value) {
      this.tabKey = value;
      await this.onSearch();
    },
    async onAuditStatus(param) {
      const res = await loadRefundTradeAudit(param);
      if (res.code === 200) {
        this.$message.success('处理成功');
        this.onSearch();
      } else {
        this.$message.error(res.message);
      }
    },
    showAuditModal(record) {
      this.$confirm('','是否确认审核通过？').then(() => {
        this.onAuditStatus({
          refundId: record.refundId,
          auditStatus: 'SUCCESS',
        });
      })
    },
    changeRejectModal(id, visible) {
      this.rejectModalVisible = visible;
      this.refundId = id;
    },
    changeRejectRefundModal(id, visible) {
      this.rejectRefundModalVisible = visible;
      this.refundId = id;
    },
    changeRefundModal(record, visible) {
      this.refundModalVisible = visible;
      this.refundRecord = record;
    },
    showPayModal(record) {
      this.$confirm({
        title: '退款',
        content: '是否确认退款？',
        onOk: () => {
          this.onRefundPay({ refundId: record.refundId });
        },
      });
    },
    async onRefundPay(param) {
      this.loading = true
      const res = await loadRefundTradePay(param).catch(() => this.loading = false);
      if (res.code === 200) {
        this.$message.success('处理成功');
        this.onSearch();
      } else {
        this.$message.error(res.message);
      }
    },
    async onRefundRejectPay(param) {
      const res = await loadRefundRejectPay(param);
      if (res.code === 200) {
        this.$message.success('处理成功');
        this.onSearch();
      } else {
        this.$message.error(res.message);
      }
    },
    settingArea(value) {
      this.areaList = getPath(value);
    },
    async handlePagination({ page, limit }) {
      this.pagination.current = page;
      this.pagination.pageSize = limit;
      await this.onSearch();
    },
  },
  created() {
    // this.loadTreeData();
    this.onSearch();
  },
};
</script>

<style scoped lang="less">
.el-form-item {
  ::v-deep .el-form-item__content {
    flex: 1;
  }
}
.el-date-editor.el-range-editor.el-input__inner.el-date-editor--daterange {
  width: 100%;
}
.el-form.el-form-inline {
  width: 100%;
}
.imgAndRefundId{
  display: flex;
  align-content: center;
  justify-content: space-between;
}
</style>
