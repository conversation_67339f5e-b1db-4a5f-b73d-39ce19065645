<template>
  <div style="margin: 30px 0 40px 0;">
    <el-form :model="configs" label-suffix="：" label-width="120px"><!--
      <el-form-item label="订单支付顺序" :label-width="formItemLayout.labelCol.sm + 'px'">
        <el-radio-group v-model="configs.paymentOrder" @change="editConfigs('paymentOrder', configs.paymentOrder)">
          <el-radio :label="1">先款后货</el-radio>
          <el-radio :label="0">不限</el-radio>
        </el-radio-group>
        <div style="color: #999;">
          选择“先款后货”，客户必须支付订单后商家才可发货，选择“不限”，无论客户是否支付都可发货
        </div>
      </el-form-item>-->

      <el-form-item v-if="configs.paymentOrder !== 1" label="订单失效时间" :label-width="formItemLayout.labelCol.sm + 'px'">
        <div class="itemBox">
          <el-switch
            v-model="configs.timeoutCancel"
            active-text="开"
            inactive-text="关"
            @change="editConfigs('timeoutCancel', configs.timeoutCancel)"
          ></el-switch>
          <span class="spanBefore">订单提交</span>
          <el-input-number
            v-model="configs.timeoutCancelHour"
            :max="9999"
            :min="1"
            :precision="0"
            style="margin-left: 10px;"
            @change="editConfigs('timeoutCancelHour', configs.timeoutCancelHour)"
          ></el-input-number>
          <span class="spanBefore">小时后，客户逾期未支付，将会自动作废订单。</span>
        </div>
      </el-form-item>

      <el-form-item label="订单自动确认收货" :label-width="formItemLayout.labelCol.sm + 'px'">
        <div class="itemBox">
          <el-switch
            v-model="configs.autoReceive"
            active-text="开"
            inactive-text="关"
            @change="editConfigs('autoReceive', configs.autoReceive)"
          ></el-switch>
          <span class="spanBefore">订单全部发货</span>
          <el-input-number
            v-model="configs.autoReceiveDay"
            :max="9999"
            :min="1"
            :precision="0"
            style="margin-left: 10px;"
            @change="editConfigs('autoReceiveDay', configs.autoReceiveDay)"
          ></el-input-number>
          <span class="spanBefore">天后,客户逾期未处理的待收货订单，将会自动确认收货。</span>
        </div>
      </el-form-item>

      <el-form-item label="已完成订单允许申请退单" :label-width="formItemLayout.labelCol.sm + 'px'">
        <div class="itemBox">
          <el-switch
            v-model="configs.applyRefund"
            active-text="开"
            inactive-text="关"
            @change="editConfigs('applyRefund', configs.applyRefund)"
          ></el-switch>
          <span class="spanBefore">订单完成</span>
          <el-input-number
            v-model="configs.applyRefundDay"
            :max="9999"
            :min="1"
            :precision="0"
            style="margin-left: 10px;"
            @change="editConfigs('applyRefundDay', configs.applyRefundDay)"
          ></el-input-number>
          <span class="spanBefore">天内,允许客户发起退货退款申请，未发货订单随时可退。</span>
        </div>
      </el-form-item>

      <el-form-item label="待审核退单自动审核" :label-width="formItemLayout.labelCol.sm + 'px'">
        <div class="itemBox">
          <el-switch
            v-model="configs.refundAutoAudit"
            active-text="开"
            inactive-text="关"
            @change="editConfigs('refundAutoAudit', configs.refundAutoAudit)"
          ></el-switch>
          <span class="spanBefore">退单提交</span>
          <el-input-number
            v-model="configs.refundAutoAuditDay"
            :max="9999"
            :min="1"
            :precision="0"
            style="margin-left: 10px;"
            @change="editConfigs('refundAutoAuditDay', configs.refundAutoAuditDay)"
          ></el-input-number>
          <span class="spanBefore">天后,商家逾期未处理的待审核退单，将会自动审核通过。</span>
        </div>
      </el-form-item>

      <el-form-item label="结算规则" :label-width="formItemLayout.labelCol.sm + 'px'">
        <div class="itemBox">
          <el-radio-group v-model="configs.settleRule">
            <el-radio :label="0">付款后立即结算</el-radio>
            <el-radio :label="1">周期结算</el-radio>
          </el-radio-group>
        </div>
        <div class="itemBox" v-if="configs.settleRule === 1">
          <span class="wrapBefore">订单</span>
          <el-radio-group class="spanBefore" v-model="configs.settleWay">
            <el-radio :label="0">付款后</el-radio>
            <el-radio :label="1">完成后</el-radio>
          </el-radio-group>
          <el-input-number
            v-model="configs.settleDay"
            :max="9999"
            :min="1"
            :precision="0"
            style="margin-left: 10px;"
          ></el-input-number>
          <span class="spanBefore">天后,结算。</span>
        </div>
      </el-form-item>
      <!--

      <el-form-item label="退单自动确认收货" :label-width="formItemLayout.labelCol.sm + 'px'">
        <div class="itemBox">
          <el-switch
            v-model="configs.refundAutoReceive"
            active-text="开"
            inactive-text="关"
            @change="editConfigs('refundAutoReceive', configs.refundAutoReceive)"
          ></el-switch>
          <span class="spanBefore">退单物流信息提交</span>
          <el-input-number
            v-model="configs.refundAutoReceiveDay"
            :max="9999"
            :min="1"
            :precision="0"
            style="margin-left: 10px;"
            @change="editConfigs('refundAutoReceiveDay', configs.refundAutoReceiveDay)"
          ></el-input-number>
          <span class="spanBefore">天后,商家逾期未处理的待收货退单，将会自动确认收货，非快递退回的退单，在审核通过后开始计时。</span>
        </div>
      </el-form-item>

      <el-form-item label="已完成订单自动好评" :label-width="formItemLayout.labelCol.sm + 'px'">
        <div class="itemBox">
          <el-switch
            v-model="configs.timeoutEvaluate"
            active-text="开"
            inactive-text="关"
            @change="editConfigs('timeoutEvaluate', configs.timeoutEvaluate)"
          ></el-switch>
          <span class="spanBefore">订单完成</span>
          <el-input-number
            v-model="configs.timeoutEvaluateDay"
            :max="9999"
            :min="1"
            :precision="0"
            style="margin-left: 10px;"
            @change="editConfigs('timeoutEvaluateDay', configs.timeoutEvaluateDay)"
          ></el-input-number>
          <span class="spanBefore">天后，未评价的订单，将会自动给予全五星好评，好评文案为</span>
          <el-input
            v-model="configs.timeoutEvaluateComment"
            :maxlength="20"
            style="width: 300px; margin-left: 10px;"
            @change="editConfigs('timeoutEvaluateComment', configs.timeoutEvaluateComment)"
          ></el-input>
        </div>
      </el-form-item>-->
    </el-form>
  </div>
</template>

<script>
export default {
  props: {
    configs: Object,
    editConfigs: Function,
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          span: 2,
          xs: { span: 24 },
          sm: { span: 4 },
        },
        wrapperCol: {
          span: 24,
          xs: { span: 24 },
          sm: { span: 20 },
        },
      },
    };
  },
};
</script>

<style scoped lang="less">
</style>
