<template>
  <el-dialog
    :visible.sync="rejectModalVisible"
    :close-on-click-modal="false"
    title="请填写驳回原因"
    :before-close="modalClose"
  >
    <el-form ref="formRef" :model='model' :rules='rules'>
      <el-form-item label="驳回原因" prop="auditReason">
        <el-input type="textarea" v-model="model.auditReason"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="modalClose">取消</el-button>
      <el-button type="primary" @click="handleOK">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    rejectModalVisible: Boolean,
    refundId: String,
    onAuditStatus: Function,
    changeRejectModal: Function,
  },
  data() {
    return {
      model: {
        auditReason: '',
      },
      rules: {
        auditReason: [
          { required: true, message: "请输入驳回原因", trigger: "blur" }
        ],
      }
    };
  },
  methods: {
    handleOK() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.onAuditStatus({
            refundId: this.refundId,
            auditStatus: 'FAIL',
            ...this.model
          });
          this.modalClose();
        }
      });
    },
    modalClose() {
      this.changeRejectModal(null, false);
      this.auditReason = '';
      this.$refs.formRef.resetFields();
    },
  },
};
</script>
