<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="30%"
    center
    :modal-append-to-body="false"
    :before-close="cleanForm"
    :modal="false"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <el-form ref="form" :model="form" :rules="rules">
      <el-row style="margin-top: 10px;">
        <el-col :span="24">
          <el-form-item label="标题" label-width="100px" prop="title">
            <el-input v-model="form.title">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="视频封面"
            label-width="100px"
            prop="imgUrl"
          >
            <ImageUpload v-model="form.imgUrl" :limit="1"/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="选择视频" label-width="100px" prop="videoUrl">
            <FileUpload v-model="form.videoUrl" :limit="1" :fileType="['mp4']" :fileSize="30">
              <a slot="text">上传视频</a>
            </FileUpload>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="排序" label-width="100px" prop="sort">
            <el-input-number
              v-model="form.sort"
              :min="0"
              controls-position="right"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" size="small" @click="saveForm(0)">保存</el-button>
      <el-button type="primary" size="small" @click="saveForm(1)">保存并上架</el-button>
      <el-button size="small" @click="cleanForm">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { save } from "@/api/system/video.js";
export default {
  name: "AddDialog",
  data() {
    return {
      dialogVisible: false,
      title: "新增",
      saveFlag: false,

      form: {
        id: "",
        title: "",
        imgUrl: "",
        videoUrl: "",
        sort: "",
      },
      rules: {
        title: [
          { required: true, message: "请填写标题", trigger: "change" },
        ],
        imgUrl: [
          { required: true, message: "请上传视频封面", trigger: "change" },
        ],
        videoUrl: [
          { required: true, message: "请上传视频", trigger: "change" },
        ],
      },
    };
  },

  methods: {
    init(row) {
      this.dialogVisible = true;
      if (row) {
        this.title = "编辑";
        this.form = {
          ...row,
        };
      }
    },

    cleanForm() {
      this.dialogVisible = false;
      this.saveFlag = false;
      this.form = {
        id: "",
        title: "",
        imgUrl: "",
        videoUrl: "",
        sort: "",
      };
    },

    saveForm(enable) {
      if (this.saveFlag) {
        return;
      }
      this.saveFlag = true;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const params = {
            ...this.form,
            enable,
            id: this.form.id ? parseInt(this.form.id) : undefined,
          };
          save(params)
            .then((res) => {
              this.$message.success(this.form.id ? "编辑成功" : "新增成功");
              this.cleanForm();
              this.$emit("getData");
            })
            .finally(() => {
              this.saveFlag = false;
            });
        } else {
          this.saveFlag = false;
        }
      });
    },
  },
};
</script>

<style></style>
