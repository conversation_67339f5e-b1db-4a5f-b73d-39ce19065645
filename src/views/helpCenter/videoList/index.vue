<template>
<div class="whole">
    <div>
      <div class="addRight">
        <el-button type="primary" @click="add()">新建</el-button>
      </div>
      <el-table :data="tableData" style="width: 100%;" border>
        <el-table-column prop="sort" label="显示顺序" width="200">
          <template slot-scope="scope">
            <el-input-number
              style="width: 100%;"
              v-model="scope.row.sort"
              @blur="editSort(scope.row)"
              :min="0"
              :controls="false"
            />
          </template>
        </el-table-column>
        <el-table-column prop="imgUrl" label="封面" width="200">
          <template slot-scope="scope">
            <img :src="scope.row.imgUrl" width="50" height="50" />
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" min-width="250">
        </el-table-column>
        <el-table-column label="状态" prop="title" width="100">
        <template slot-scope="scope">
          <span> {{ scope.row.enable == 1 ? "已上架" : "已下架" }}</span>
        </template>
      </el-table-column>
        <el-table-column prop="id" label="操作" min-width="100">
          <template slot-scope="{ row }">
            <el-button type="text" @click="edit(row)">
              编辑
            </el-button>
            <el-button type="text" @click="updateEnable(row)">
             {{ row.enable == '1' ? '下架' : '上架' }}
            </el-button>
            <el-button
              type="text"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="form.pageNum"
        :limit.sync="form.pageSize"
        @pagination="getList"
      />
    </div>
    <add-dialog ref="role" @getData="getData"></add-dialog>
  </div>
</template>
<script>
import { queryVideoPage, delVideo, changeStatus, save } from "@/api/system/video.js";
import AddDialog from "./addDialog.vue";
export default {
  name: "index",
  components: {
    AddDialog,
  },
  data() {
    return {
      tableData: [],
      total: 0,
      form: {
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    async editSort(row) {
      const params = {
        ...row,
      };
      save(params).then((res) => {
        this.$message({
          type: "success",
          message: "编辑成功",
        });
        this.getData();
      });
    },
    // 获取数据
    getData() {
      const params = {
        ...this.form,
        pageNum: 1,
        pageSize: 10,
      };
      queryVideoPage(params).then((res) => {
        this.tableData = res.data.records;
        this.total = res.data.total;
        this.form.pageNum = 1;
      });
    },

    // 分页
    getList() {
      const params = {
        ...this.form,
      };
      queryVideoPage(params).then((res) => {
        this.tableData = res.data.records;
        this.total = res.data.total;
      });
    },

    // 新增
    add() {
      this.$refs.role.init();
    },

    // 编辑
    edit(row) {
      this.$refs.role.init(row);
    },

    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除？').then(function () {
        return delVideo(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },

    // 禁用
    updateEnable(row) {
      if (row.enable === 1) {
        this.$confirm("是否确认下架该条记录?", "下架确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            const params = {
              ...row,
              enable: 0,
            };
            changeStatus(params).then((res) => {
              this.$message({
                type: "success",
                message: "下架成功!",
              });
              this.getData();
            });
          })
          .catch(() => {});
      } else {
        this.$confirm("是否确认上架该记录?", "上架确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            const params = {
              ...row,
              enable: 1,
            };
            changeStatus(params).then((res) => {
              this.$message({
                type: "success",
                message: "上架成功!",
              });
              this.getData();
            });
          })
          .catch(() => {});
      }
    },

    // 导出
    exportFile() {
      const params = {
        ...this.form,
      };
      this.download(
        "/eazyRole/export-role-list",
        { ...params },
        `角色管理.xlsx`
      );
    },
  },
}
</script>

<style scoped lang="scss">
.addRight {
  float: right;
  margin: 10px 0;
}
</style>
