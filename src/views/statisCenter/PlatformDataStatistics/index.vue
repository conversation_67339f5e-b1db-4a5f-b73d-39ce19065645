<template>
  <div class="platform-data-statistics">

    <div class="default-card" v-loading="loading1">
      <div class="search-content">
        <div class="title-text">平台数据统计</div>
        <div class="des-text">平台交易总额 / 退款总额</div>
        <div class="money-text" style="margin-bottom: 24px">¥ {{ allOrderAmount }} / ¥ {{ allOrderAmountVoid }}</div>
        <statistic-search
          :search="tableProps"
          @onSearch="handleSearch"
        >
        </statistic-search>

        <el-row>
          <el-col :span="6" v-for="item in totalData.slice(0,4)" :key="item.title">
            <div class="item-content">
              <div style="border-right: 1px solid #EBEEF5">
                <div class="des-text">
                  {{ item.title }}
                  <span v-if="item.moneyVoid || item.moneyVoid === 0"> / 退款金额</span>
                  <el-tooltip class="item" effect="dark"  placement="top">
                    <i  style="font-size: 16px" class="el-icon-question"></i>
                    <template #content>
                      <div v-html="item.desTitle"></div>
                    </template>
                  </el-tooltip>
                </div>
                <div class="money-text">
                  {{ item.money }}
                  <span v-if="item.moneyVoid || item.moneyVoid === 0"> / {{ item.moneyVoid }}</span>
                </div>
              </div>

            </div>
          </el-col>
        </el-row>
      </div>
      <el-divider></el-divider>
      <div class="search-content">
        <el-row>
          <el-col :span="6" v-for="item in totalData.slice(4,8)" :key="item.title">
            <div class="item-content">
              <div style="border-right: 1px solid #EBEEF5">
                <div class="des-text">
                  {{ item.title }}
                  <span v-if="item.moneyVoid || item.moneyVoid === 0"> / 已消耗凭证数量</span>
                  <el-tooltip class="item" effect="dark"  placement="top">
                    <i  style="font-size: 16px" class="el-icon-question"></i>
                    <template #content>
                      <div v-html="item.desTitle"></div>
                    </template>
                  </el-tooltip>
                </div>
                <div class="money-text">
                  {{ item.money }}
                  <span v-if="item.moneyVoid || item.moneyVoid === 0"> / {{ item.moneyVoid }}</span>
                </div>
              </div>

            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="default-card" style="margin-top: 16px" v-loading="loading2">
      <div class="search-content">
        <div class="title-text flex justify-between mb20">
          <div>平台交易趋势</div>
          <div>
            <el-radio-group v-model="platformType" @change="initEchart">
              <el-radio-button label="day" value="day">日</el-radio-button>
              <el-radio-button label="month" value="month">月</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div v-show="echartsData && echartsData.length > 0">
          <div  id="echart-one" ></div>
        </div>
        <div class="empty-data" v-show="!echartsData || echartsData.length == 0">
          <img src="@/assets/images/empty-data-img.png" alt="">
          <div class="no-data-text">暂无数据</div>
        </div>
      </div>
    </div>

    <div class="default-card" style="margin-top: 16px" v-loading="loading3">
      <div class="search-content">
        <div class="title-text flex justify-between mb20">
          <div>代理商交易统计(TOP10)</div>
          <el-button @click="exportFile">导出</el-button>
        </div>
        <el-table :data="topTenList" style="width: 100%;">
          <el-table-column prop="serviceId" label="ID">
          </el-table-column>
          <el-table-column prop="serviceNickName" label="服务中心昵称" >
          </el-table-column>
          <el-table-column prop="referrerCount" label="会员数量" >
          </el-table-column>
          <el-table-column prop="goodsCount" label="销售产品数">
          </el-table-column>
          <el-table-column prop="lotCount" label="凭证总数">
          </el-table-column>
          <el-table-column prop="orderAmount" label="订单交易总额">
          </el-table-column>
        </el-table>

      </div>
    </div>

  </div>
</template>

<script>
import * as echarts from 'echarts'
import resize from '../../../views/dashboard/mixins/resize'
import {getTotalPlatformPage, getTotalPlatformTotal, getTotalPlatformTrend} from "@/api/basis/basis";
import GetProjectList from "@/layout/mixin/GetProjectList";

export default {
  name: "PlatformDataStatistics",
  mixins: [ GetProjectList],
  data() {
    return {
      formModel: {
        projectKeyword: '',
        times: [this.$dayjs().startOf('month').format('YYYY-MM-DD'), this.$dayjs().format('YYYY-MM-DD')],
        lotId: '',
      },
      totalData: [
        {
          title: '订单支付总额',
          desTitle: '累计订单交易/退款总额：<br/>'  + '平台订单支付/退款总额(含交易手续费)',
          money: '0',
          moneyVoid: '0',
        },
        {
          title: '订单交易总额',
          desTitle: '累计订单交易总额：<br/>' + '平台产品订单总额(不含交易手续费)',
          money: '0',
          moneyVoid: '0',
        },
        {
          title: '平台收益总额',
          desTitle: '累计平台收益总额：<br/>'  + '委托订单收益=交易手续费+平台技术服务费+推荐服务费<br/>'  + '凭证挂单收益=交易手续费+委托服务费',
          money: '0',
        },
        {
          title: '商家收益总额',
          desTitle: '累计商家收益总额：<br/>' +'产品销售产品实际应收金额+<br/>' +'委托订单收益即订单推荐服务费/<br/>'  +'凭证挂单收益即订单委托服务费',
          money: '0',
          moneyVoid: '0',
        },
        {
          title: '项目凭证总数',
          desTitle: '项目凭证总额：<br/>'+'平台内当前项目的凭证发行总数量',
          money: '0',
        },
        {
          title: '已发凭证总数量',
          desTitle: '已发凭证总额：除商家外的所有用户的持仓资产总和',
          money: '0',
        },
        {
          title: '已发放凭证总价值',
          desTitle: '已发放凭证总价值：<br/>' +'已发放凭证总数量*当前价格',
          money: '0',
        },
        {
          title: '产品销售总数量',
          desTitle: '所有实物商品销售数量的总和',
          money: '0',
        }
      ],
      topTenList: [],
      platformType: 'day',
      allOrderAmount: 0,
      allOrderAmountVoid: 0,
      echartsData: [],
      myChart: null,
      loading1: false,
      loading2: false,
      loading3: false,
      defaultSelect: true
    }
  },
  computed: {
    tableProps() {
      return {
        model: this.formModel,
        columns: [
          {
            title: '所属项目：',
            dataIndex: 'projectKeyword',
            customEnum: true,
            valueEnum: this.projectList.map(item => {
              return {
                value: item.code,
                text: item.name
              }
            }),
            allowClear: true
          },
          {
            title: '时间：',
            dataIndex: 'times',
            dateTime: true,
            dateNoAllowClear: true
          },
          {
            title: '凭证ID：',
            dataIndex: 'lotId',
          },
        ],
      }
    }
  },

  created() {
  },

  activated() {
    if(this.myChart){
      this.myChart.resize()
    }
  },
  mounted() {
   this.initAllData()
  },

  methods: {
    async initAllData() {
      await this.initData()
      this.initEchart()
      if (this.myChart) {
        this.myChart.resize()
      }
    },
    async initData() {
      await this.getProjectData();
      this.getList();
      this.getTopTenList();
    },
    // 获取数据
    getTopTenList() {
      this.loading3 = true;
      getTotalPlatformPage({
        ...this.formModel,
        current: 1,
        pageSize: 10,
        statisticsStartTime: this.formModel.times? this.$dayjs(this.formModel.times?.[0]).format('YYYY-MM-DD 00:00:00') : '',
        statisticsEndTime: this.formModel.times? this.$dayjs(this.formModel.times?.[1]).format('YYYY-MM-DD 23:59:59') : '',
        times: undefined
      }).then((res) => {
        this.topTenList = res?.records ||[]
      }).finally(() => {
        this.loading3 = false;
      });
    },
    getList() {
      this.loading1 = true;
      getTotalPlatformTotal({
        ...this.formModel,
        statisticsStartTime: this.formModel.times? this.$dayjs(this.formModel.times?.[0]).format('YYYY-MM-DD 00:00:00') : '',
        statisticsEndTime: this.formModel.times? this.$dayjs(this.formModel.times?.[1]).format('YYYY-MM-DD 23:59:59') : '',
        times: undefined
      }).then((res) => {
        console.log(res.data);
        if( res.code == 200){
          this.allOrderAmount = res.data?.orderAmount || 0
          this.allOrderAmountVoid = res.data?.orderVoidAmount || 0
          this.totalData[0].money = res.data?.orderBuyerPay || 0
          this.totalData[0].moneyVoid = res.data?.orderBuyerVoidPay || 0
          this.totalData[1].money = res.data?.goodsAmount || 0
          this.totalData[1].moneyVoid = res.data?.goodsVoidAmount || 0
          this.totalData[2].money = res.data?.platformIncome || 0
          this.totalData[3].money = res.data?.upbeatIncome || 0
          this.totalData[3].moneyVoid = res.data?.upbeatVoidIncome || 0
          this.totalData[4].money = res.data?.lotCount || 0
          this.totalData[5].money = res.data?.hasSendCount || 0
          this.totalData[5].moneyVoid = res.data?.consumeNum || 0
          this.totalData[6].money = res.data?.hasSendAmount || 0
          this.totalData[7].money = res.data?.goodsCount || 0
        }
      }).finally(() => {
        this.loading1 = false;
      });
    },
    handleSearch(model, isReset) {
      if(isReset) {
        this.formModel.projectKeyword = this.projectList[0].code
      }
      this.getList();
      this.initEchart()
      this.getTopTenList()
    },
    async initEchart() {
      this.loading2 = true;
      const result = await getTotalPlatformTrend({
        ...this.formModel,
        type: this.platformType,
        statisticsStartTime: this.formModel.times ? this.$dayjs(this.formModel.times?.[0]).format('YYYY-MM-DD 00:00:00') : '',
        statisticsEndTime: this.formModel.times ? this.$dayjs(this.formModel.times?.[1]).format('YYYY-MM-DD 23:59:59') : '',
        times: undefined
      }).finally(() => {
        this.loading2 = false;
      });
      console.log(result);
      this.echartsData = result.data || []
      let labelList = []
      let dataList = []
      if(result.code == 200){
        result.data.forEach(item => {
          labelList.push(item?.statisticsDate || '')
          dataList.push(item?.tradeAmount ?(Number(item.tradeAmount)/10000).toFixed(8):0 || 0)
        })
      }
      this.$nextTick(()=>{
        const myChart = echarts.init(document.getElementById('echart-one'))
        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {}
            }
          },

          // 距离左右 顶部距离为0
          grid: {
            left: 16,
            right: 0,
            top: 30,
            bottom: 0,
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              data: labelList,
              axisPointer: {
                type: 'shadow'
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '单位：万元',
              show: true
            },
            {
              show: false,
            }
          ],
          color: ['#1890FF'],
          series: [
            {
              name: '交易额',
              type: 'line',
              smooth: true,
              data: dataList
            }
          ]
        };
        myChart.setOption(option)
        this.myChart = myChart
        //屏幕发生变化时重置图表
        myChart.resize()
        window.addEventListener('resize', () => {
          myChart.resize()
        })
      })
    },
    exportFile() {
      const params = {
        projectKeyword: this.formModel.projectKeyword,
        lotId: this.formModel.lotId,
        statisticsStartTime: this.formModel.times ? this.$dayjs(this.formModel.times?.[0]).format('YYYY-MM-DD 00:00:00') : '',
        statisticsEndTime: this.formModel.times ? this.$dayjs(this.formModel.times?.[1]).format('YYYY-MM-DD 23:59:59') : '',
      };
      this.download(
        "/statistics-platform-center/total-platform-export",
        { ...params },
        `代理商交易统计(TOP10).xlsx`
      );
    },
  },
}
</script>

<style scoped lang="scss">
.platform-data-statistics {
  background-color: #F0F2F5;
  padding: 24px;
  .default-card {
    padding: 20px 0 24px 0;
    background: #FFFFFF;
    border-radius: 2px;
    .title-text {
      font-size: 16px;
      color: #000000;
      line-height: 24px;
      margin-bottom: 16px;
    }
    .des-text{
      font-size: 14px;
      color: rgba(0,0,0,0.45);
      line-height: 22px;
      margin-bottom: 2px;
    }
    .money-text {
      font-size: 16px;
      color: rgba(0,0,0,0.85);
      line-height: 32px;
    }
    .search-content {
      padding: 0 32px;
    }
    .item-content {
      padding: 24px 0  0 24px;
    }
    .el-row {
      .el-col:nth-child(1),
      .el-col:nth-child(5) {
        .item-content {
          padding-left: 0;
        }
      }
    }
    #echart-one {
      height: 270px;
      //background-color: red;
    }
    .empty-data {
      height: 270px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      img {
        width: 66px;
        height: 42px;
      }
      .no-data-text {
        margin-top: 6px;
        font-weight: 400;
        font-size: 16px;
        color: rgba(0,0,0,0.25);
      }
    }
  }
}
</style>
