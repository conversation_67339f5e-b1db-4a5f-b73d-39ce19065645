<template>
  <div class="whole">
    <el-form :model="searchForm" ref="form">
      <el-row>
        <el-col :span="6">
          <el-form-item label="所属项目" label-width="82px" prop="projectKeyword">
            <el-select v-model="searchForm.projectKeyword" style="width: 100%" placeholder="请选择">
              <el-option
                v-for="item in projectList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="凭证代码" label-width="85px">
            <el-input v-model="searchForm.lotId" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="轮次" label-width="85px" prop="times">
            <!--            <el-input v-model="searchForm.rounds" style="width: 100%" placeholder="请输入(例如1-1)"></el-input>-->
            <el-select v-model="searchForm.times" style="width: 100%" placeholder="请选择">
              <el-option
                v-for="item in times"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="日期"
            label-width="90px"
            prop="createTime"
          >
            <el-date-picker
              style="width: 100%;"
              v-model="searchForm.createTime"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订单来源" label-width="85px" prop="channelList">
            <el-select multiple v-model="searchForm.channelList" style="width: 100%" placeholder="请选择">
              <el-option
                v-for="item in channelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="" label-width="43px">
            <el-button @click="onReset">重置</el-button>
            <el-button type="primary" @click="handlePagination({ page: 1, limit: 10 })">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div>
      <el-row  :gutter="10" class="action-flex-end">
        <el-col :span="22.5">
          <el-radio-group class="ml-auto mr-16" v-model="tabKey" @change="onTabChange">
            <el-radio-button v-for="option in orderStatusOptions" :key="option.value" :label="option.value">
              {{ option.label }}
            </el-radio-button>
          </el-radio-group>
        </el-col>
        <el-col :span="1.5">
          <el-button
            @click="exportFile"
          >导出</el-button>
        </el-col>
      </el-row>
      <el-table :data="tableData" :loading="loading" border stripe>
        <el-table-column prop="tradeId" label="订单编号" min-width="130" />
        <el-table-column prop="lotOrderId" label="凭证订单号" min-width="130">
          <template slot-scope="scope">
            {{ scope.row.lotOrderId || '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="lotOrderId" label="凭证收购状态" min-width="130">
          <template slot-scope="scope">
            {{ scope.row.lotOrderTradeStatus || '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="lotOrderId" label="订单来源" min-width="130">
          <template slot-scope="scope">
            {{ scope.row.channelValue || '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="goods" label="商品" width="240">
          <template slot-scope="scope">
            <el-row  type="flex" justify="space-between">
              <el-col>
                <div :size="16">
                  <div :size="24" class="flex">
                    <!--                    <span>{{ scope.row.id }}</span>-->
                    <!--                    <el-tag type="info">{{ scope.row.channelValue }}</el-tag>-->
                    <img width="50px" height="50px" v-for="(v, k) in scope.row.goodsInfoImgList || []" :key="k" :src="v || defaultImg" v-if="k < 3" />
                    <img width="50px" height="50px" v-if="scope.row.goodsInfoImgList && scope.row.goodsInfoImgList.length === 0" :src="defaultImg" />
                    <div class="mx-8" v-if="scope.row.goodsList && scope.row.goodsList[0]">
                      {{ scope.row.goodsList[0].goodsSkuName }}
                    </div>
                    <router-link class="flex-none ml-auto" :to="`/order/detail?id=${scope.row.tradeId}`">
                      <span style="line-height: 50px">详情</span>
                    </router-link>
                  </div>
                  <div :size="4">
                    下单时间：{{ scope.row.createTime }}
                  </div>
                </div>
              </el-col>
            </el-row>
            <div class="rowBorderLeft" />
            <!--            <div class="rowBorderBottom" />-->
            <div class="colImg">
              <img
                v-for="(item, index) in scope.row.tradeTOBGoods"
                :key="index"
                :src="item.goodsSpuImage || defaultImg"
                class="goods-image"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="buyerName" label="买家" min-width="280">
          <template slot-scope="scope">
            <div class="span_all cursor-pointer"  @click="jumpMemberCenter(scope.row.buyerUserId)">
              <div class="span_left">
                <el-image
                  v-if="scope.row.buyerHeadPortrait"
                  :src="scope.row.buyerHeadPortrait"
                  fit="fill"
                ></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.buyerNickName }} <span>({{ scope.row.buyerUserId}})</span></div>
                <div>{{ scope.row.buyerUserName }} 【{{ scope.row.buyerName }}】</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="referrerName" label="所属机构" min-width="280">
          <template slot-scope="scope">
            <div class="span_all cursor-pointer" @click="jumpMemberCenter(scope.row.referrerUserId)">
              <div class="span_left">
                <el-image
                  v-if="scope.row.referrerHeadPortrait"
                  :src="scope.row.referrerHeadPortrait"
                  fit="fill"
                ></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.referrerNickName }} <span>({{ scope.row.referrerUserId}})</span> </div>
                <div>
                  {{ scope.row.referrerUserName }} 【{{
                    scope.row.referrerName
                  }}】
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="consigneeName" label="收货人" width="150">
          <template slot-scope="scope">
            <div>
              {{ scope.row.consigneeName }}
              <div>{{ scope.row.consigneeCellphone }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="payPrice" label="金额/数量" width="140">
          <template slot-scope="scope">
            <div>
              ￥{{ scope.row.payPrice }}
              <div>({{ scope.row.num }}件)</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="statusValue" label="订单状态" width="100">
          <template slot-scope="scope">
            <div>
              {{ scope.row.statusValue }}
              <div class="rowBorderRight"></div>
            </div>
          </template>
        </el-table-column>
<!--        <el-table-column label="操作" width="60" fixed="right" v-if="noLimit">
          <template slot-scope="{ row }">
            <el-button v-if="['NOT_PAY'].includes(row.status) && !isLot(row.channel)" type="text" @click="handleCancel(row)">
              取消
            </el-button>
          </template>
        </el-table-column>-->
        <!--      <el-table-column label="操作">-->
        <!--        <template slot-scope="scope">-->
        <!--          <el-button type="primary" v-if="scope.row.canDelivery===true" size="mini" plain @click="goToDetail(scope.row.tradeId, '2')">发货</el-button>-->
        <!--          <el-button type="primary" size="mini" plain @click="goToDetail(scope.row.tradeId)">详情</el-button>-->
        <!--        </template>-->
        <!--      </el-table-column>-->
      </el-table>
      <pagination
        v-show="pagination.total > 0"
        :total="pagination.total"
        :page.sync="pagination.current"
        :limit.sync="pagination.pageSize"
        @pagination="handlePagination"
      />
    </div>
  </div>
</template>

<script>
import { orderStatusOptions, channelOptions } from '../../OrderEnum';
import JumpMemberCenter from "@/layout/mixin/JumpMemberCenter";
import GetProjectList from "@/layout/mixin/GetProjectList";
import {mapGetters} from "vuex";
import {isLot} from '@/utils/business';
import defaultImg from '@/assets/images/defaultImg.png';
import { getStatisticsGoodsPage, getStatisticsGoodsTimes } from "@/api/basis/basis.js";
import _ from 'lodash';

export default {
  name: "FourPage",
  mixins: [GetProjectList, JumpMemberCenter],
  watch: {
    'searchForm.lotId': {
      handler(val) {
        if (val) {
          this.searchForm.times = ''
          this.debouncedSearch(val)
        } else {
          this.times = [];
          this.searchForm.times = ''
        }
      }
    }
  },
  data() {
    return {
      defaultImg,
      tableData: [],
      times: [],
      loading: false,
      tabKey: null,
      payModalVisible: false,
      prePayModalVisible: false,
      payRecord: {},
      AllChannels: [],
      areaList: [],
      searchForm: {
        projectKeyword: "",
        times: "",
        lotId: "",
        createTime: '',
        channelList: [],
      },
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  computed: {
    ...mapGetters(['noLimit']),
    orderStatusOptions() {
      return orderStatusOptions;
    },
    channelOptions() {
      return channelOptions;
    },
  },
  created() {
    this.debouncedSearch = _.debounce(this.getTimesList, 300);
    this.initData()
  },
  mounted() {
    this.resetForm = {
      ...this.form,
    };
  },
  methods: {
    isLot,
    async onTabChange(value) {
      this.tabKey = value;
      await this.onSearch();
    },
    initData() {
      this.getProjectData();
      this.init();
    },
    handleParams() {
      return {
        ...this.searchForm,
        size: this.pagination.pageSize,
        current: this.pagination.current,
        createTimeStart: this.searchForm.createTime ? `${this.searchForm.createTime[0]} 00:00:00` : null,
        createTimeEnd: this.searchForm.createTime ? `${this.searchForm.createTime[1]} 23:59:59` : null,
        status: this.tabKey,
      }
    },
    async getTimesList(val) {
      await getStatisticsGoodsTimes({lotCode: val}).then(r => {
        this.times = r.data || []
      })
    },
    async onSearch() {
      this.loading = true;
      const params = this.handleParams();

      try {
        const res = await getStatisticsGoodsPage(params);
        this.tableData = res.data.records;
        this.pagination.total = res.data.total;
      } catch (e) {
      }

      this.loading = false;
    },
    onReset() {
      this.searchForm = this.$options.data().searchForm;
      this.handlePagination({page: 1, limit: 10});
    },
    handlePagination(pagination) {
      this.pagination.current = pagination.page;
      this.pagination.pageSize = pagination.limit;
      this.onSearch();
    },
    async init() {
      this.onSearch();
    },
    // 导出
    exportFile() {
      this.download(
        "/statistics-upbeat-center/goods-export",
        this.handleParams(),
        `商城订单.xlsx`,
        {
          headers: { 'Content-Type': 'application/json' },
        }
      );
    },
  }
}
</script>

<style scoped>

</style>
