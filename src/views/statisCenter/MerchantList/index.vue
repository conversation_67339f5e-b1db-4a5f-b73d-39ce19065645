<!--
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-04-07 13:19:17
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-04-07 15:27:04
-->
<template>
  <div class="whole">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="委托挂单" name="first" lazy>
        <one-page ref="OnePage" key="OnePage"></one-page>
      </el-tab-pane>
      <el-tab-pane label="预约销售" name="second" lazy>
        <two-page ref="TwoPage" key="TwoPage"></two-page>
      </el-tab-pane>
      <el-tab-pane label="自主销售" name="third" lazy>
        <three-page ref="ThreePage" key="ThreePage"></three-page>
      </el-tab-pane>
      <el-tab-pane label="商城订单" name="fourth" lazy>
        <four-page ref="FourPage" key="FourPage"></four-page>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import OnePage from "./OnePage.vue";
import TwoPage from "./TwoPage.vue";
import ThreePage from "./ThreePage.vue";
import FourPage from "./FourPage.vue";
export default {
  name: "MerchantList",
  components: {
    OnePage,
    TwoPage,
    ThreePage,
    FourPage,
  },
  data() {
    return {
      form: {
        pageNum: 1,
        pageSize: 10,
        id: "",
      },
      activeName: "first",
      total: 0,
      time: "",
      resetForm: {},
      tableData: [],
    };
  },
  created() {
  },
  methods: {
    handleClick(tab, event) {
      // console.log(tab, event);
    },
  },
};
</script>
<style scoped lang="scss">
</style>
