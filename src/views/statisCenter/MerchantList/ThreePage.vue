<template>
  <div class="whole">
    <el-form :model="form" ref="form" :rules="rules">
      <el-row>
        <el-col :span="5">
          <el-form-item label="所属项目" label-width="82px" prop="projectKeyword">
            <el-select v-model="form.projectKeyword" style="width: 100%" placeholder="请选择">
              <el-option
                v-for="item in projectList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="凭证代码" label-width="85px">
            <el-input  v-model="form.lotId" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="日期"
            label-width="90px"
            prop="userName"
          >
            <el-date-picker
              style="width: 100%;"
              v-model="time"
              type="daterange"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="changeTime"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="服务中心" label-width="85px" prop="">
            <el-input v-model="form.serviceId" style="width: 100%" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="" label-width="43px">
            <el-button @click="reset"> 重置 </el-button>
            <el-button type="primary" @click="getData">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div>
      <el-row  :gutter="10" class="action-flex-end">
        <el-col :span="1.5">
          <el-button
            @click="handleExport"
          >导出</el-button>
        </el-col>
      </el-row>
      <el-table :data="tableData" style="width: 100%;" border v-loading="loading">
        <el-table-column prop="statisticsDate" label="日期" width="180">
        </el-table-column>
        <el-table-column prop="serviceId" label="服务中心ID" min-width="100">
        </el-table-column>
        <el-table-column prop="serviceNickName" label="服务中心昵称" min-width="100">
        </el-table-column>
        <el-table-column prop="date" label="凭证" min-width="150">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image :src="scope.row.lotImg" fit="fill"></el-image>
              </div>
              <div class="span_right">
                <div>{{ scope.row.lotName }}</div>
                <div>【{{ scope.row.lotCode }}】</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="lotCount" label="总凭证数量" min-width="100">
        </el-table-column>
        <el-table-column prop="lotAmount" label="订单总金额" min-width="100">
        </el-table-column>
        <el-table-column prop="withUpbeatCount" label="商家带凭证总数量" min-width="140">
        </el-table-column>
        <el-table-column prop="withUpbeatAmount" label="带凭证总金额" min-width="100">
        </el-table-column>
        <el-table-column prop="overUpbeatCount" label="超带票数量" min-width="100">
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="form.current"
        :limit.sync="form.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>
<script>
import { getTotalSelfPage } from "@/api/basis/basis.js";
import GetProjectList from "@/layout/mixin/GetProjectList";

export default {
  name: "ThreePage",
  mixins: [GetProjectList],
  data() {
    return {
      form: {
        current: 1,
        pageSize: 10,
        statisticsEndTime: "",
        statisticsStartTime: "",
        projectKeyword: "",
        serviceId: "",
        lotId: "",
      },
      time: "",
      resetForm: {},
      tableData: [],
      total: 0,
      loading: false,
      rules: {},
    };
  },
  created() {
    this.initData()
  },

  mounted() {
    this.resetForm = {
      ...this.form,
    };
  },
  methods: {
    initData() {
      this.getData();
      this.getProjectData()
    },
    // 导出
    handleExport() {
      const params = {
        projectKeyword: this.form.projectKeyword,
        statisticsStartTime: this.form.statisticsStartTime,
        statisticsEndTime: this.form.statisticsEndTime,
        serviceId: this.form.serviceId,
        lotId: this.form.lotId,
      };
      this.download("/statistics-upbeat-center/total-self-export", {...params}, `自主销售.xlsx`);
    },
    // 获取数据
    getData() {
      this.form.current = 1;
      this.form.pageSize = 10;
      this.getList();
    },

    // 重置
    reset() {
      this.form = {
        ...this.resetForm,
      };
      this.time = "";
      this.getData();
    },

    // 改变时间
    changeTime(time) {
      if(time){
        this.form.statisticsStartTime = this.$dayjs(time[0]).format(
          "YYYY-MM-DD 00:00:00"
        );
        this.form.statisticsEndTime = this.$dayjs(time[1]).format(
          "YYYY-MM-DD 23:59:59"
        );
      }else {
        this.form.statisticsStartTime = "";
        this.form.statisticsEndTime = "";
      }
    },

    // 分页 直接按queryForm 开始查询
    getList() {
      this.loading = true;
      getTotalSelfPage({
        ...this.form,
      }).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
      }).finally(() => {
        this.loading = false;
      });
    },
  },
};
</script>
<style scoped lang="scss">
.addRight {
  float: right;
  margin: 10px 0;
}

.toClear {
  clear: both;
}

.numberStyle {
  background: #e7f8ff;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  border: 1px solid #e3f4ff;
  margin-bottom: 10px;
}
</style>
