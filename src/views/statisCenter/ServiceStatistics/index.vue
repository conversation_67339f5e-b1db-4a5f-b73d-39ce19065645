<template>
  <div class="merchant-statistics">
    <div class="default-card" v-loading="loading1">
      <div class="search-content">
        <div class="title-text">服务中心数据统计</div>
        <statistic-search
          :search="tableProps"
          @onSearch="handleSearch"
          @onReset="handleReset"
        >
        </statistic-search>

        <el-row>
          <el-col :span="6" v-for="item in totalData.slice(0,4)" :key="item.title">
            <div class="item-content">
              <div style="border-right: 1px solid #EBEEF5">
                <div class="des-text">
                  {{ item.title }}
                  <span v-if="item.moneyVoid || item.moneyVoid === 0"> / 退款金额</span>
                  <el-tooltip class="item" effect="dark"  placement="top">
                    <i  style="font-size: 16px" class="el-icon-question"></i>
                    <template #content>
                      <div v-html="item.desTitle"></div>
                    </template>
                  </el-tooltip>
                </div>
                <div class="money-text">
                  {{ item.money }}
                  <span v-if="item.moneyVoid || item.moneyVoid === 0"> / {{ item.moneyVoid }}</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-divider></el-divider>
      <div class="search-content">
        <el-row>
          <el-col :span="6" v-for="item in totalData.slice(4,8)" :key="item.title">
            <div class="item-content">
              <div style="border-right: 1px solid #EBEEF5">
                <div class="des-text">
                  {{ item.title }}
                  <el-tooltip class="item" effect="dark"  placement="top">
                    <i  style="font-size: 16px" class="el-icon-question"></i>
                    <template #content>
                      <div v-html="item.desTitle"></div>
                    </template>
                  </el-tooltip>
                </div>
                <div class="money-text">
                  {{ item.money }}
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <el-divider></el-divider>
      <div class="search-content">
        <el-row>
          <el-col :span="6" v-for="item in totalData.slice(8,12)" :key="item.title">
            <div class="item-content">
              <div style="border-right: 1px solid #EBEEF5">
                <div class="des-text">
                  {{ item.title }}
                  <el-tooltip class="item" effect="dark"  placement="top">
                    <i  style="font-size: 16px" class="el-icon-question"></i>
                    <template #content>
                      <div v-html="item.desTitle"></div>
                    </template>
                  </el-tooltip>
                </div>
                <div class="money-text">
                  {{ item.money }}
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

    </div>

    <div class="default-card" style="margin-top: 16px" v-loading="loading2">
      <div class="search-content">
        <div class="title-text flex justify-between mb20">
          <div></div>
          <el-button @click="exportFile">导出</el-button>
        </div>
        <el-table :data="topTenList" style="width: 100%;">
          <el-table-column prop="statisticsDate" label="日期">
          </el-table-column>
          <el-table-column prop="serviceId" label="ID">
          </el-table-column>
          <el-table-column prop="serviceNickName" label="服务中心昵称" >
          </el-table-column>
          <el-table-column prop="preSaleAmount" label="预约销售总额">
          </el-table-column>
          <el-table-column prop="selfSaleAmount" label="自主销售总额">
          </el-table-column>
          <el-table-column prop="lotBuyCount" label="凭证收购总数量">
          </el-table-column>
          <el-table-column prop="lotBuyAmount" label="凭证收购总额">
          </el-table-column>
          <el-table-column prop="dealFee" label="凭证收购手续费">
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="form.current"
          :limit.sync="form.pageSize"
          @pagination="getTotalServicePage"
        />
      </div>
    </div>

  </div>
</template>

<script>

import { getTotalServiceTotal, getTotalSortPage } from "@/api/basis/basis";
import GetProjectList from "@/layout/mixin/GetProjectList";

export default {
  name: "ServiceStatistics",
  mixins: [GetProjectList],
  data() {
    return {
      formModel: {
        projectKeyword: '',
        times: '',
        lotId: '',
        serviceId: '',
      },
      totalData: [
        {
          title: '预约销售总额',
          desTitle: '服务中心下属会员进行预约销售总金额',
          money: '0',
        },
        {
          title: '自主销售总额',
          desTitle: '服务中心进行自主销售总金额',
          money: '0',
        },
        {
          title: '凭证收购总额',
          desTitle: '服务中心收购凭证总金额（包含自主销售的订单以及委托销售的订单）',
          money: '0',
        },
        {
          title: '凭证收购数量',
          desTitle: '服务中心收购凭证总数量（包含自主销售的订单以及委托销售的订单）',
          money: '0',
        },

        {
          title: '凭证收购交易手续费',
          desTitle: '服务中心收购时支付的“买家交易手续费”',
          money: '0',
        },
        {
          title: '预约销售会员带服务中心凭证数量',
          desTitle: '服务中心下属会员预约销售订单中，服务中心被带售的',
          money: '0',
        },
        {
          title: '自主销售服务中心带商家凭证数量',
          desTitle: '服务中心自主销售订单中，商家被带售的',
          money: '0',
        },
        {
          title: '会员委托挂单人数',
          desTitle: '当前已经建立委托挂单关系的会员数',
          money: '0',
        },

        {
          title: '可用凭证销售数量',
          desTitle: '服务中心进行自主销售可用凭证总数量',
          money: '0',
        },
        {
          title: '可用凭证销售总额',
          desTitle: '服务中心进行自主销售可用凭证总金额',
          money: '0',
        },
        {
          title: '收购凭证销售数量',
          desTitle: '服务中心进行自主销售收购凭证总数量',
          money: '0',
        },
        {
          title: '收购凭证销售总额',
          desTitle: '服务中心进行自主销售收购凭证总金额',
          money: '0',
        }
      ],
      topTenList: [],
      total: 0,
      form: {
        current: 1,
        pageSize: 10,
      },
      loading1: false,
      loading2: false,
    }
  },
  created() {
    this.getTotalServiceTotal()
    this.getTotalServicePage()
    this.getProjectData()
  },
  computed: {
    tableProps() {
      return {
        model: this.formModel,
        columns: [
          {
            title: '所属项目：',
            dataIndex: 'projectKeyword',
            customEnum: true,
            valueEnum: this.projectList.map(item => {
              return {
                value: item.code,
                text: item.name
              }
            }),
            allowClear: true
          },
          {
            title: '时间：',
            dataIndex: 'times',
            dateTime: true,
            dateNoAllowClear: false,
          },
          {
            title: '凭证ID：',
            dataIndex: 'lotId',
          },
          {
            title: '服务中心：',
            dataIndex: 'serviceId',
          }
        ],
      }
    },
  },
  methods: {
    getTotalServicePage(){
      let params = {
        ...this.formModel,
        ...this.form,
        statisticsStartTime: this.formModel.times? this.$dayjs(this.formModel.times?.[0]).format('YYYY-MM-DD 00:00:00') : '',
        statisticsEndTime: this.formModel.times? this.$dayjs(this.formModel.times?.[1]).format('YYYY-MM-DD 23:59:59') : '',
      }
      delete params.times
      this.loading2 = true
      getTotalSortPage(params).then(res => {
        this.topTenList = res.records;
        this.total = res.total;
      }).finally(() => {
        this.loading2 = false
      })
    },
    getTotalServiceTotal() {
      this.loading1 = true
      getTotalServiceTotal(
        {
          ...this.formModel,
          statisticsStartTime: this.formModel.times? this.$dayjs(this.formModel.times?.[0]).format('YYYY-MM-DD 00:00:00') : '',
          statisticsEndTime: this.formModel.times? this.$dayjs(this.formModel.times?.[1]).format('YYYY-MM-DD 23:59:59') : '',
          times:undefined
        }
      ).then(res => {
        console.log('data', res.data)
        this.totalData[0].money = res?.data?.preSaleAmount
        this.totalData[0].moneyVoid = res?.data?.preSaleVoidAmount
        this.totalData[1].money = res?.data?.selfSaleAmount
        this.totalData[1].moneyVoid = res?.data?.selfSaleVoidAmount
        this.totalData[2].money = res?.data?.lotBuyAmount
        this.totalData[3].money = res?.data?.lotBuyCount
        this.totalData[4].money = res?.data?.lotBuyFee
        this.totalData[5].money = res?.data?.preMemberWithServiceCount
        this.totalData[6].money = res?.data?.selfServiceWithUpbeatCount
        this.totalData[7].money = res?.data?.memberEntrustCount
        this.totalData[8].money = res?.data?.selfCanSaleLotCount
        this.totalData[9].money = res?.data?.selfCanSaleLotAmount
        this.totalData[10].money = res?.data?.selfWholeLotCount
        this.totalData[11].money = res?.data?.selfWholeLotAmount
      }).finally(() => {
        this.loading1 = false
      })
    },
    handleSearch(model,value) {
      console.log(model,value);
      if(value){
        this.form = {
          current: 1,
          pageSize: 10,
        }
      }
      this.getTotalServiceTotal()
      this.getTotalServicePage()
      // console.log('search')
    },
    handleReset() {
      this.formModel = this.$options.data().formModel
      console.log('reset')
    },
    exportFile() {
      const params = {
        ...this.formModel,
        statisticsStartTime: this.formModel.times? this.$dayjs(this.formModel.times?.[0]).format('YYYY-MM-DD 00:00:00') : '',
        statisticsEndTime: this.formModel.times? this.$dayjs(this.formModel.times?.[1]).format('YYYY-MM-DD 23:59:59') : '',
        times:undefined
      };
      this.download(
        "/statistics-service-center/total-sort-export",
        { ...params },
        `服务中心销售统计.xlsx`
      );
    },
  },
}
</script>

<style scoped lang="scss">
.merchant-statistics {
  background-color: #F0F2F5;
  padding: 24px;
  .default-card {
    padding: 20px 0 24px 0;
    background: #FFFFFF;
    border-radius: 2px;
    .title-text {
      font-size: 16px;
      color: #000000;
      line-height: 24px;
      margin-bottom: 16px;
    }
    .des-text{
      font-size: 14px;
      color: rgba(0,0,0,0.45);
      line-height: 22px;
      margin-bottom: 2px;
    }
    .money-text {
      font-size: 16px;
      color: rgba(0,0,0,0.85);
      line-height: 32px;
    }
    .search-content {
      padding: 0 32px;
    }
    .item-content {
      padding: 24px 0  0 24px;
    }
    .el-row {
      .el-col:nth-child(1),
      .el-col:nth-child(5) {
        .item-content {
          padding-left: 0;
        }
      }
      .el-col:nth-child(4),
      .el-col:nth-child(8) {
        .item-content {
          div {
            border-right: none!important;
          }
        }
      }
    }
  }
}
</style>

