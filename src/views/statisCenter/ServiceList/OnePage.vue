<template>
  <div class="whole">
    <el-form :model="form" ref="form" :rules="rules">
      <el-row>
        <el-col :span="6">
          <el-form-item label="所属项目" label-width="90px" prop="projectKeyword">
<!--            <el-input v-model="form.projectKeyword" style="width: 100%" placeholder="请输入"></el-input>-->
            <el-select v-model="form.projectKeyword" style="width: 100%" placeholder="请选择">
              <el-option
                v-for="item in projectList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="日期"
            label-width="90px"
            prop="userName"
          >
            <el-date-picker
              style="width: 100%;"
              v-model="time"
              type="daterange"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="changeTime"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="服务中心" label-width="90px" prop="">
            <el-input v-model="form.serviceId" style="width: 100%" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="" label-width="43px">
            <el-button @click="reset"> 重置 </el-button>
            <el-button type="primary" @click="getData">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div>
      <el-row  class="mb-8">
        <div class="flex items-center justify-between">
          <div class="title-statistics">
            <span class="statistics-style" style="margin-right:32px">数量：{{ (countData.lotCount || countData.lotCount == 0) ? countData.lotCount : '-' }}</span>
            <span class="statistics-style">金额：{{ (countData.lotAmount || countData.lotAmount == 0) ? countData.lotAmount :'-' }} </span>
          </div>

          <el-col :span="1.5">
            <el-button
              @click="handleExport"
            >导出</el-button>
          </el-col>
        </div>
      </el-row>
      <el-table :data="tableData" style="width: 100%;" border v-loading="loading">
        <el-table-column prop="createDate" label="创建时间" width="180">
        </el-table-column>
        <el-table-column prop="serviceNickName" label="服务中心昵称" min-width="100">
        </el-table-column>
        <el-table-column prop="orderNo" label="订单编号" min-width="150">
        </el-table-column>
        <el-table-column prop="buyUserId" label="买家id" min-width="100">
        </el-table-column>
        <el-table-column prop="buyUserNickName" label="买家昵称" min-width="100">
        </el-table-column>
        <el-table-column prop="goodsName" label="产品名称" min-width="100">
        </el-table-column>
        <el-table-column prop="goodsCount" label="数量" min-width="100">
        </el-table-column>
        <el-table-column prop="goodsAmount" label="订单金额" min-width="100">
        </el-table-column>
        <el-table-column prop="lotCount" label="凭证数量" min-width="100">
        </el-table-column>
        <el-table-column prop="lotAmount" label="金额" min-width="100">
        </el-table-column>
        <el-table-column prop="sellUserNickName" label="卖家昵称" min-width="100">
        </el-table-column>
        <el-table-column prop="sellUserAccountNumber" label="卖家账号" min-width="100">
        </el-table-column>
        <el-table-column prop="withMemberCount" label="卖家凭证数量" min-width="100">
        </el-table-column>
        <el-table-column prop="withMemberAmount" label="金额" min-width="100">
        </el-table-column>

        <el-table-column prop="withServiceCount" label="服务中心凭证数量" min-width="140">
        </el-table-column>
        <el-table-column prop="withServiceAmount" label="金额" min-width="100">
        </el-table-column>

        <el-table-column prop="withUpbeatCount" label="商家凭证数量" min-width="100">
        </el-table-column>
        <el-table-column prop="withUpbeatAmount" label="金额" min-width="100">
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="form.current"
        :limit.sync="form.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>
<script>
import {getServicePrePage, getServicePreTotal} from "@/api/basis/basis.js";
import GetProjectList from "@/layout/mixin/GetProjectList";

export default {
  name: "OnePage",
  mixins: [GetProjectList],
  data() {
    return {
      form: {
        current: 1,
        pageSize: 10,
        statisticsEndTime: "",
        statisticsStartTime: "",
        projectKeyword: "",
        serviceId: "",
      },
      time: "",
      resetForm: {},
      tableData: [],
      total: 0,
      loading: false,
      rules: {},
      countData: {
        lotCount: 0,
        lotAmount: 0
      },
    };
  },
  created() {
    this.initData()
  },

  mounted() {
    this.resetForm = {
      ...this.form,
    };
  },
  methods: {
    getTotalData(){
      getServicePreTotal(this.form).then((res) => {
        if (res.code === 200) {
          this.countData = res.data || {
            lotCount: 0,
            lotAmount: 0
          }
        }else {
          this.$message.warning(res.msg)
        }
      })
    },
    initData() {
      this.getData();
      this.getProjectData()
    },
    // 导出
    handleExport() {
      const params = {
        projectKeyword: this.form.projectKeyword,
        statisticsStartTime: this.form.statisticsStartTime,
        statisticsEndTime: this.form.statisticsEndTime,
        serviceId: this.form.serviceId,
      };
      this.download("/statistics-service-center/total-pre-export", {...params}, `预约销售.xlsx`);
    },
    // 获取数据
    getData() {
      this.form.current = 1;
      this.form.pageSize = 10;
      this.getList();
      this.getTotalData()
    },

    // 重置
    reset() {
      this.form = {
        ...this.resetForm,
      };
      this.time = "";
      this.getData();
    },

    // 改变时间
    changeTime(time) {
      if(time){
        this.form.statisticsStartTime = this.$dayjs(time[0]).format(
          "YYYY-MM-DD 00:00:00"
        );
        this.form.statisticsEndTime = this.$dayjs(time[1]).format(
          "YYYY-MM-DD 23:59:59"
        );
      }else {
        this.form.statisticsStartTime = "";
        this.form.statisticsEndTime = "";
      }

    },

    // 分页 直接按queryForm 开始查询
    getList() {
      this.loading = true;
      getServicePrePage({
        ...this.form,
      }).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
      }).finally(() => {
        this.loading = false;
      });
    },
  },
};
</script>
<style scoped lang="scss">
.addRight {
  float: right;
  margin: 10px 0;
}

.toClear {
  clear: both;
}

.numberStyle {
  background: #e7f8ff;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  border: 1px solid #e3f4ff;
  margin-bottom: 10px;
}
</style>
