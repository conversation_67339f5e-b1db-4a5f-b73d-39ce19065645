<template>
  <div class="merchant-statistics">
    <div class="default-card" v-loading="loading1">
      <div class="search-content">
        <div class="title-text">商家数据统计</div>
        <div class="des-text">可提现总额</div>
        <div class="money-text" style="margin-bottom: 24px">¥ {{ canPayoutsMoney }}</div>
        <statistic-search
          :search="tableProps"
          @onSearch="handleSearch"
        >
        </statistic-search>

        <el-row>
          <el-col :span="6" v-for="item in totalData.slice(0,4)" :key="item.title">
            <div class="item-content">
              <div style="border-right: 1px solid #EBEEF5">
                <div class="des-text">
                  {{ item.title }}
                  <span v-if="item.moneyVoid || item.moneyVoid === 0"> / 退款金额</span>
                  <el-tooltip class="item" effect="dark"  placement="top">
                    <i  style="font-size: 16px" class="el-icon-question"></i>
                    <template #content>
                      <div v-html="item.desTitle"></div>
                    </template>
                  </el-tooltip>
                </div>
                <div class="money-text">
                  {{ item.money }}
                  <span v-if="item.moneyVoid || item.moneyVoid === 0"> / {{ item.moneyVoid }}</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-divider></el-divider>
      <div class="search-content">
        <el-row>
          <el-col :span="6" v-for="item in totalData.slice(4,8)" :key="item.title">
            <div class="item-content">
              <div style="border-right: 1px solid #EBEEF5">
                <div class="des-text">
                  {{ item.title }}
                  <span v-if="item.moneyVoid || item.moneyVoid === 0"> / 退款金额</span>
                  <el-tooltip class="item" effect="dark"  placement="top">
                    <i  style="font-size: 16px" class="el-icon-question"></i>
                    <template #content>
                      <div v-html="item.desTitle"></div>
                    </template>
                  </el-tooltip>
                </div>
                <div class="money-text">
                  {{ item.money }}
                  <span v-if="item.moneyVoid || item.moneyVoid === 0"> / {{ item.moneyVoid }}</span>
                </div>
              </div>

            </div>
          </el-col>
        </el-row>
      </div>

      <el-divider></el-divider>
      <div class="search-content">
        <el-row>
          <el-col :span="6" v-for="( item,index ) in totalData.slice(8,12)" :key="item.title">
            <div class="item-content">
              <div style="border-right: 1px solid #EBEEF5">
                <div class="des-text">
                  {{ item.title }}
                  <span v-if="(item.moneyVoid || item.moneyVoid === 0) && [1,3].includes(index)"> / {{ chineseNames[index] }}</span>
                  <el-tooltip class="item" effect="dark"  placement="top">
                    <i  style="font-size: 16px" class="el-icon-question"></i>
                    <template #content>
                      <div v-html="item.desTitle"></div>
                    </template>
                  </el-tooltip>
                </div>
                <div class="money-text">
                  {{ item.money }}
                  <span v-if="item.moneyVoid || item.moneyVoid === 0"> / {{ item.moneyVoid }}</span>
                </div>
              </div>

            </div>
          </el-col>
        </el-row>
      </div>

    </div>

    <div class="default-card" style="margin-top: 16px" v-loading="loading2">
      <div class="search-content">
        <div class="title-text" style="margin-bottom: 24px">交易市场数据</div>
        <statistic-search
          :search="tablePropsTwo"
          :rules="rules"
          @onSearch="handleSearchTwo"
        >
        </statistic-search>

        <el-row>
          <el-col :span="6" v-for="item in totalTwoData.slice(0,4)" :key="item.title">
            <div class="item-content">
              <div style="border-right: 1px solid #EBEEF5">
                <div class="des-text">
                  {{ item.title }}
                </div>
                <div class="money-text">
                  {{ item.money }}
                </div>
              </div>

            </div>
          </el-col>
        </el-row>
      </div>
      <div class="search-content">
        <el-row>
          <el-col :span="6" v-for="item in totalTwoData.slice(4,8)" :key="item.title">
            <div class="item-content">
              <div style="border-right: 1px solid #EBEEF5">
                <div class="des-text">
                  {{ item.title }}
                </div>
                <div class="money-text">
                  {{ item.money }}
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

    </div>


    <div class="default-card" style="margin-top: 16px" v-loading="loading3">
      <div class="search-content">
        <div class="title-text flex justify-between mb20">
          <div>服务中心销售统计</div>
          <el-button @click="exportFile">导出</el-button>
        </div>
        <el-table :data="topTenList" style="width: 100%;">
          <el-table-column prop="statisticsDate" label="日期" min-width="160">
          </el-table-column>
          <el-table-column prop="serviceId" label="ID" min-width="160">
          </el-table-column>
          <el-table-column prop="serviceNickName" label="服务中心昵称" min-width="120">
          </el-table-column>
          <el-table-column prop="preSaleCount" label="预约销售凭证总数" min-width="160">
          </el-table-column>
          <el-table-column prop="preSaleAmount" label="预约销售总额"  min-width="120">
          </el-table-column>
          <el-table-column prop="selfSaleCount" label="自主销售凭证总数"  min-width="160">
          </el-table-column>
          <el-table-column prop="selfSaleAmount" label="自主销售总额"  min-width="120">
          </el-table-column>
          <el-table-column prop="entrustSaleCount" label="委托收购凭证总数"  min-width="160">
          </el-table-column>
          <el-table-column prop="entrustSaleAmount" label="委托收购凭证总额"  min-width="160">
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="form.current"
          :limit.sync="form.pageSize"
          @pagination="getTotalSalePage"
        />
      </div>
    </div>

  </div>
</template>

<script>

import {getTotalSalePage, getTotalUpbeatDeal, getTotalUpbeatTotal, getTotalUpbeatWithdrawal} from "@/api/basis/basis";
import { list } from "@/api/transferAssets/lotList";
import GetProjectList from "@/layout/mixin/GetProjectList";

export default {
  name: "MerchantStatistics",
  mixins: [GetProjectList],
  data() {
    return {
      form:{
        current: 1,
        pageSize: 10,
      },
      formModel: {
        times: undefined,
        lotId: "",
        projectKeyword: "",
      },
      formModelTwo: {
        rounds: undefined,
        price: undefined,
      },
      totalData: [
        {
          title: '订单交易总额',
          desTitle: '累计订单交易总额：<br/>' + '平台产品订单总额',
          money: '0',
        },
        {
          title: '商家收益总额',
          desTitle: '累计商家收益总额：<br/>' + '产品收益总额+ <br/>' +'委托订单收益即订单推荐服务费/<br/>'  +'凭证挂单收益即订单委托服务费',
          money: '0',
        },
        {
          title: '平台经营保证金',
          desTitle: '平台经营保证金',
          money: '0',
        },
        {
          title: '预约销售总额',
          desTitle: '预约销售总额：<br/>' +'预约销售订单总额',
          money: '0',
        },

        {
          title: '推荐服务费(预约销售)',
          desTitle: '推荐服务费：<br/>'+'预约销售订单中，商家收取推荐服务费总金额',
          money: '0',
        },
        {
          title: '自主销售总额',
          desTitle: '自主销售总额：<br/>'+'自主销售订单总额',
          money: '0',
        },
        {
          title: '推荐服务费(自主销售)',
          desTitle: '推荐服务费：<br/>' +'自主销售订单中，商家收取推荐服务费总金额',
          money: '0',
        },
        {
          title: '委托销售总额',
          desTitle: '委托销售总额：<br/>' +'委托销售订单总额',
          money: '0',
        },

        {
          title: '委托服务费',
          desTitle: '委托服务费：<br/>'+'委托销售订单中，商家收取推荐服务费总金额',
          money: '0',
        },
        {
          title: '产品收益总额',
          desTitle: '累计产品收益总额：<br/>'+'产品销售额-平台技术服务费-推荐服务费-经营保证金',
          money: '0',
        },
        {
          title: '平台技术服务费',
          desTitle: '平台技术服务费：<br/>' +'所有订单总商家支付的平台技术服务费总额',
          money: '0',
        },
        {
          title: '已发凭证总数',
          desTitle: '已发凭证总额：<br/>' +'除商家外的所有用户的持仓资产总和',
          money: '0',
        }
      ],
      totalTwoData: [
        {
          title: '当轮自主转存总量',
          money: '0',
        },
        {
          title: '剩余可交易总量',
          money: '0',
        },
        {
          title: '当轮释放总量',
          money: '0',
        },
        {
          title: '当前轮次凭证价',
          money: '0',
        },

        {
          title: '当前价销售总量',
          money: '0',
        },
        {
          title: '当前价释放总量',
          money: '0',
        },
        {
          title: '当前个人凭证转让总量',
          money: '0',
        },
        {
          title: '当前个人凭证转让总额',
          money: '0',
        },
      ],
      topTenList: [],
      rules: {
        rounds: [
          { required: true, message: "请输入轮次", trigger: "blur" },
          //校验 1.只能输入数字和'-' 2.必须有且只有一个'-'分隔符 3.且不能在首尾
          {
            validator: (rule, value, callback) => {
              if (value) {
                if (!/^\d+-\d+$/.test(value)) {
                  callback(new Error("请输入正确的轮次"));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
      lotsList: [],
      total: 0,
      canPayoutsMoney: 0,
      loading1: false,
      loading2: false,
      loading3: false,
      chineseNames: {
        1: '退款总额',
        3: '已消耗凭证数量'
      }
    }
  },
  created() {
    this.getAllLotsList()
    this.getTotalSalePage()
    this.getTotalUpbeatTotal()
    this.getTotalUpbeatWithdrawal()
    this.getProjectData()
  },
  computed: {
    tableProps() {
      return {
        model: this.formModel,
        columns: [
          {
            title: '凭证ID：',
            dataIndex: 'lotId',
          },
          {
            title: '所属项目：',
            dataIndex: 'projectKeyword',
            customEnum: true,
            valueEnum: this.projectList.map(item => {
              return {
                value: item.code,
                text: item.name
              }
            }),
            allowClear: true,
          },
          {
            title: '时间：',
            dataIndex: 'times',
            dateTime: true,
          }
        ],
      }
    },
    tablePropsTwo() {
      return {
        model: this.formModelTwo,
        columns: [
          {
            title: '凭证：',
            dataIndex: 'lotId',
            customEnum: true,
            valueEnum:this.lotsList,

          },
          {
            title: '轮次：',
            dataIndex: 'rounds',
            placeholderText: '请输入(例如1-1)',
          }
        ],
      }
    }
  },
  methods: {
    getTotalUpbeatWithdrawal(){
      getTotalUpbeatWithdrawal(
        {
          ...this.formModel,
          statisticsStartTime: this.formModel.times? this.$dayjs(this.formModel.times?.[0]).format('YYYY-MM-DD 00:00:00') : '',
          statisticsEndTime: this.formModel.times? this.$dayjs(this.formModel.times?.[1]).format('YYYY-MM-DD 23:59:59') : '',
          times:undefined
        }
      ).then(res => {
        this.canPayoutsMoney = res?.data || 0
      })
    },
    getTotalUpbeatTotal(){
      this.loading1 = true
      getTotalUpbeatTotal({
        ...this.formModel,
        statisticsStartTime: this.formModel.times? this.$dayjs(this.formModel.times?.[0]).format('YYYY-MM-DD 00:00:00') : '',
        statisticsEndTime: this.formModel.times? this.$dayjs(this.formModel.times?.[1]).format('YYYY-MM-DD 23:59:59') : '',
        times:undefined
      }).then(res => {
        console.log('data', res.data)
        this.totalData[0].money = res?.data?.orderAmount
        this.totalData[0].moneyVoid = res?.data?.orderVoidAmount
        this.totalData[1].money = res?.data?.upbeatIncome
        this.totalData[1].moneyVoid = res?.data?.upbeatVoidIncome
        this.totalData[2].money = res?.data?.retainUp
        this.totalData[3].money = res?.data?.preSaleAmount
        this.totalData[3].moneyVoid = res?.data?.preSaleVoidAmount

        this.totalData[4].money = res?.data?.preSaleServiceFee
        this.totalData[5].money = res?.data?.selfSaleAmount
        this.totalData[5].moneyVoid = res?.data?.selfSaleVoidAmount
        this.totalData[6].money = res?.data?.selfSaleServiceFee
        this.totalData[7].money = res?.data?.entrustSaleAmount

        this.totalData[8].money = res?.data?.entrustSaleServiceFee
        this.totalData[9].money = res?.data?.goodsIncome
        this.totalData[9].moneyVoid = res?.data?.goodsVoidIncome
        this.totalData[10].money = res?.data?.platformServiceFee
        this.totalData[11].money = res?.data?.hasSendCount
        this.totalData[11].moneyVoid = res?.data?.consumeNum
      }).finally(() => {
        this.loading1 = false
      })
    },
    getTotalSalePage(){
      this.loading3 = true
      let params = {
        ...this.formModel,
        ...this.form,
        statisticsStartTime: this.formModel.times? this.$dayjs(this.formModel.times?.[0]).format('YYYY-MM-DD 00:00:00') : '',
        statisticsEndTime: this.formModel.times? this.$dayjs(this.formModel.times?.[1]).format('YYYY-MM-DD 23:59:59') : '',
        times:undefined
      }
      delete params.times
      getTotalSalePage(params).then(res => {
        this.topTenList = res.records;
        this.total = res.total;
      }).finally(() => {
        this.loading3 = false
      })
    },
    getAllLotsList() {
      list({
        current: 1,
        pageSize: 99999
      }).then(res => {
        this.lotsList = res?.records?.map(item => {
          return {
            value: item.lotId,
            text: item.lotName,
            times: item.times,
            roundTime: item.roundTime
          }
        })
        this.$set(this.formModelTwo, 'lotId', this.lotsList?.[0]?.value)
        this.formModelTwo.rounds = res?.records?.[0]?.times + '-' + res?.records?.[0]?.roundTime
        this.getTotalUpbeatDeal()
      })
    },
    getTotalUpbeatDeal() {
      this.loading2 = true
      getTotalUpbeatDeal({
        lotId: this.formModelTwo.lotId,
        times: this.formModelTwo.rounds?.split('-')?.[0],
        roundTimes: this.formModelTwo.rounds?.split('-')?.[1],
      }).then(res => {
        this.totalTwoData[0].money = res?.data?.trandferCount
        this.totalTwoData[1].money = res?.data?.remainingTradableCount
        this.totalTwoData[2].money = res?.data?.releaseCount
        this.totalTwoData[3].money = res?.data?.lotPrice
        this.totalTwoData[4].money = res?.data?.salesCount
        this.totalTwoData[5].money = res?.data?.currentReleaseCount
        this.totalTwoData[6].money = res?.data?.currentTrandferCount
        this.totalTwoData[7].money = res?.data?.currentTrandferAmount
      }).finally(
        () => {
          this.loading2 = false
        }
      )
    },
    handleSearch(model,value) {
      if(value){
        this.form = {
          current: 1,
          pageSize: 10,
        }
      }
      this.getTotalUpbeatTotal()
      this.getTotalSalePage()
      this.getTotalUpbeatWithdrawal()
    },
    handleSearchTwo(model,value) {
      if(value){
        this.formModelTwo.rounds = this.lotsList?.[0]?.times + '-' + this.lotsList?.[0]?.roundTime
        // this.formModelTwo.lotId = this.lotsList?.[0]?.value
        this.$set(this.formModelTwo, 'lotId', this.lotsList?.[0]?.value)
      }
      this.getTotalUpbeatDeal()
    },
    exportFile() {
      const params = {
        ...this.formModel,
        statisticsStartTime: this.formModel.times? this.$dayjs(this.formModel.times?.[0]).format('YYYY-MM-DD 00:00:00') : '',
        statisticsEndTime: this.formModel.times? this.$dayjs(this.formModel.times?.[1]).format('YYYY-MM-DD 23:59:59') : '',
        times:undefined
      };
      this.download(
        "/statistics-upbeat-center/total-sale-export",
        { ...params },
        `服务中心销售统计.xlsx`
      );
    },
  },
}
</script>

<style scoped lang="scss">
.merchant-statistics {
  background-color: #F0F2F5;
  padding: 24px;
  .default-card {
    padding: 20px 0 24px 0;
    background: #FFFFFF;
    border-radius: 2px;
    .title-text {
      font-size: 16px;
      color: #000000;
      line-height: 24px;
      margin-bottom: 16px;
    }
    .des-text{
      font-size: 14px;
      color: rgba(0,0,0,0.45);
      line-height: 22px;
      margin-bottom: 2px;
    }
    .money-text {
      font-size: 16px;
      color: rgba(0,0,0,0.85);
      line-height: 32px;
    }
    .search-content {
      padding: 0 32px;
    }
    .item-content {
      padding: 24px 0  0 24px;
    }
    .el-row {
      .el-col:nth-child(1),
      .el-col:nth-child(5) {
        .item-content {
          padding-left: 0;
        }
      }
      .el-col:nth-child(4),
      .el-col:nth-child(8) {
        .item-content {
          div {
            border-right: none!important;
          }
        }
      }
    }
  }
}
</style>

