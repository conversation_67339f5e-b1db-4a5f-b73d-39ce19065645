<template>
  <div class="whole">
    <el-form :model="form" ref="form">
      <el-row>
        <el-col :span="8">
          <el-form-item label="项目名称" label-width="110px">
            <el-input v-model="form.name" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目代码" label-width="110px">
            <el-input v-model="form.code" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="发布时间" label-width="110px">
            <el-date-picker
              style="width: 100%;"
              v-model="time"
              type="daterange"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="changeTime"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="" label-width="43px">
            <el-button @click="reset"> 重置</el-button>
            <el-button type="primary" @click="getData(true)">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div>
      <div class="mb-8 flex justify-end">
        <el-button type="primary" @click="editProject()">新建</el-button>
      </div>
      <el-table :data="tableData" style="width: 100%;" v-loading="loading" border>
        <el-table-column prop="sorted" label="排序" min-width="60">
        </el-table-column>
        <el-table-column prop="releaseTime" label="发布日期" min-width="120">
          <template slot-scope="scope">
            {{ formatDate(scope.row.releaseTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="type" label="项目类型" min-width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.type === '1'">热门项目</span>
            <span v-else>预挂牌项目</span>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="项目代码" min-width="120">
        </el-table-column>
        <el-table-column prop="name" label="项目名称" min-width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.name || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="项目状态" min-width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.status === '1'">销售中</span>
            <span v-else>已结束</span>
          </template>
        </el-table-column>
        <el-table-column prop="jsonList" label="关联商品数" min-width="130">
          <template slot-scope="scope">
            <span v-if="scope.row.type === '2'">
              -
            </span>
            <span v-else-if="scope.row.jsonList && scope.row.jsonList.length" class="text-main">
              {{ scope.row.jsonList.length }}
            </span>
            <span v-else>0</span>
          </template>
        </el-table-column>
        <el-table-column prop="userVO" label="商家" min-width="120">
          <template slot-scope="scope">{{ scope.row.userVO ? scope.row.userVO.userName : '' }}</template>
        </el-table-column>
        <el-table-column prop="merchantId" label="商家客服企业ID" min-width="120">
        </el-table-column>
        <el-table-column prop="customerServiceLink" label="客服链接" min-width="120">
        </el-table-column>
        <el-table-column prop="introduePic" label="列表项目图" min-width="180">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image
                  v-if="scope.row.introduePic"
                  :src="scope.row.introduePic"
                  fit="fill"
                ></el-image>
                <span v-else>-</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="innerPic" label="内页介绍图" min-width="180">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image
                  v-if="scope.row.innerPic"
                  :src="scope.row.innerPic"
                  fit="fill"
                ></el-image>
                <span v-else>-</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="项目说明" min-width="150">
          <template slot-scope="{ row: { desExt, description } }">
            <span v-if="!desExt">-</span>
            <div class="span_all" v-else-if="acceptImg.includes(desExt)">
              <div class="span_left">
                <el-image
                  v-if="description"
                  :src="description"
                  fit="fill"
                ></el-image>
                <span v-else>-</span>
              </div>
            </div>
            <a v-else :href="description" target="_blank">项目说明.{{ desExt }}</a>
          </template>
        </el-table-column>
        <el-table-column prop="license" label="企业证照" min-width="150">
          <template slot-scope="{ row: { license0Ext, license0 } }">
            <span v-if="!license0Ext">-</span>
            <div class="span_all" v-else-if="acceptImg.includes(license0Ext)">
              <div class="span_left">
                <el-image
                  v-if="license0"
                  :src="license0"
                  fit="fill"
                ></el-image>
                <span v-else>-</span>
              </div>
            </div>
            <a v-else :href="license0" target="_blank">营业执照图.{{ license0Ext }}</a>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="120" fixed="right">
          <template slot-scope="{ row }">
            <template>
              <el-button type="text" @click="editProject(row)">
                编辑
              </el-button>
              <el-button
                type="text"
                @click="delProject(row)"
              >
                删除
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="form.pageNum"
        :limit.sync="form.pageSize"
        @pagination="handlePaginationChange"
      />
    </div>

    <projectEditDrawer ref="projectEditDrawer" @getData="getData"></projectEditDrawer>
  </div>
</template>
<script>
import projectEditDrawer from "./projectEditDrawer.vue";
import {delProject, getProjectPage} from "@/api/message/message";
import {formatDate} from "@/utils/date";
import {getFileExt} from "@/utils/tool";

export default {
  name: "ProjectManage",
  components: {
    projectEditDrawer,
  },
  data() {
    return {
      form: {
        id: undefined,
        pageNum: 1,
        pageSize: 10,
        name: "",
        code: "",
        registEndTime: "",
        registStartTime: "",
        type: undefined
      },

      total: 0,
      time: "",
      roleData: [],
      resetForm: {
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      src:
        "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
      sonTableData: [],
      acceptImg: '.png,.jpg,.jpeg',
      loading: false,
    };
  },
  mounted() {
    this.resetForm = {
      ...this.form,
    };
    this.getData();
  },
  methods: {
    getFileExt,
    formatDate,
    // 新增/编辑
    editProject(row = {}) {
      this.$refs.projectEditDrawer.init(row);
    },
    delProject(row) {
      this.$modal
        .confirm("是否确认删除数据")
        .then(async () => {
          await delProject(row);
          this.$message({
            type: "success",
            message: "删除成功!",
          });
          this.getData();
        })
        .catch(() => {
        });
    },
    // 获取数据
    getData(refresh) {
      if (refresh) {
        this.form.pageNum = 1;
      }
      const params = {
        ...this.form,
      };
      this.loading = true;
      getProjectPage(params).then((res) => {
        res.data.records.forEach((item) => {
          item.desExt = getFileExt(item.description)
          item.license0 = item.license ? item.license.split(',')[0] : ''
          item.license0Ext = getFileExt(item.license0)
        });
        this.tableData = res.data.records;
        this.total = res.data.total;
        if (this.total === 0 && this.form.pageNum > 1) {
          this.getData(true);
        }
      }).finally(() => {
        this.loading = false;
      });
    },

    handlePaginationChange(pagination) {
      const {page, limit} = pagination;
      this.form.pageNum = page;
      this.form.pageSize = limit;
      this.getData();
    },

    // 重置
    reset() {
      this.form = {
        ...this.resetForm,
      };
      this.time = "";
      this.getData();
    },

    // 改变申请日期
    changeTime(time) {
      if (!time) {
        Object.assign(this.form, {
          startTime: "",
          endTime: "",
        });
        return
      }
      this.form.startTime = this.$dayjs(this.time[0]).format(
        "YYYY-MM-DD"
      );
      this.form.endTime = this.$dayjs(this.time[1]).format(
        "YYYY-MM-DD"
      );
    },
  },
};
</script>
<style scoped lang="scss">
</style>
