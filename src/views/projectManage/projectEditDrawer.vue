<template>
  <a-drawer
    :title="title"
    width="960"
    :closable="true"
    @close="cleanForm"
    :visible="dialogVisible"
    :maskClosable="false"
    class="drawer-default auth-dialog"
  >
    <div class="dialogStyle">
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <el-row>
          <el-col :span="24">
            <el-form-item label="项目类型" label-width="100px">
              <el-radio-group v-model="form.type" @input="changeType">
                <el-radio label="1">热门项目</el-radio>
                <el-radio label="2">预挂牌项目</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <template v-if="form.type == 1">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="项目代码" label-width="100px" prop="code">
                <el-input placeholder="请输入" v-model="form.code"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目名称" label-width="100px" prop="name">
                <el-input placeholder="请输入" v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发布时间" label-width="110px" prop="releaseTime">
                <el-date-picker
                  style="width: 100%;"
                  v-model="form.releaseTime"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目状态" label-width="100px" prop="status">
                <el-select
                  v-model="form.status"
                  placeholder="请选择"
                  style="width: 100%;"
                >
                  <el-option
                    v-for="dict in [{label: '销售中', value: '1'}, {label: '已结束', value: '2'},]"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="排序" label-width="100px" prop="sorted">
                <el-input-number placeholder="请输入" v-model="form.sorted" :precision="0"
                                 controls-position="right"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="商家" label-width="100px" prop="upbeatUser">
                <el-select
                  v-model="form.upbeatUser"
                  placeholder="请按照手机/ID/昵称/姓名搜索"
                  style="width: 100%;"
                  filterable
                  @clear="saveMemberList = memberList"
                  :filter-method="filterMethod"
                >
                  <el-option
                    v-for="item in saveMemberList"
                    :key="item.userId"
                    :label="item.userName"
                    :value="item.userId"
                  >
                    <span> [{{ item.userId }}]{{ item.userName }} </span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="商家客服企业ID" label-width="100px" prop="merchantId">
                <el-input placeholder="请输入" v-model="form.merchantId"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客服链接" label-width="100px" prop="customerServiceLink">
                <el-input placeholder="请输入" v-model="form.customerServiceLink"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人" label-width="100px" prop="consignee">
                <el-input placeholder="请输入" v-model="form.consignee"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人手机号" label-width="100px" prop="consigneePhone">
                <el-input placeholder="请输入" v-model="form.consigneePhone"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="退货/自提地址" prop="selectAddress">
                <AreaSelect
                  ref="areaSelect"
                  :value.sync="form.selectAddress"
                  :addressInfo.sync="addressInfo"
                  placeholder="请选择退货/自提地址"
                ></AreaSelect>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="详细地址" label-width="100px" prop="detailedAddress">
                <el-input placeholder="请输入" v-model="form.detailedAddress"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="isDeal()">
              <el-form-item label="版权交易编号" label-width="100px" prop="copyId">
                <el-input placeholder="请输入" v-model="form.copyId"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目说明" label-width="100px" prop="description">
                <FileUpload v-model="form.description" :limit="1" :fileType="acceptFileArr">
                  <a slot="text">上传文件</a>
                </FileUpload>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="列表项目图"
                label-width="110px"
                prop="introduePic"
              >
                <ImageUpload v-model="form.introduePic" :limit="1"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="内页介绍图"
                label-width="110px"
                prop="innerPic"
              >
                <ImageUpload v-model="form.innerPic" :limit="1"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="视频封面"
                label-width="110px"
                prop="contactInfo"
              >
                <ImageUpload v-model="form.contactInfo" :limit="1" tipTitle="视频封面"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="选择视频" label-width="100px" prop="videoUrl">
                <FileUpload v-model="form.videoUrl" :limit="1" :fileType="['mp4']" :fileSize="30">
                  <a slot="text">上传视频</a>
                </FileUpload>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="企业证照"
                label-width="110px"
                prop="license"
              >
                <ImageUpload v-model="form.license" :limit="5" tipTitle="公司营业执照、税务登记证、组织代码证、特殊行业许可证等企业证照"/>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        <template v-if="form.type == 2">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="项目代码" label-width="100px" prop="code">
                <el-input placeholder="请输入" v-model="form.code"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="排序" label-width="100px" prop="sorted">
                <el-input-number placeholder="请输入" v-model="form.sorted" :precision="0"
                                 controls-position="right"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="列表项目图"
                label-width="110px"
                prop="introduePic"
              >
                <ImageUpload v-model="form.introduePic" :limit="1"/>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </el-form>
    </div>

    <div class="drawer-bootom-button">
      <a-button type="primary" style="margin-right: 8px" :loading="saving" @click="saveForm">提 交</a-button>
      <a-button @click="cleanForm">取 消</a-button>
    </div>
  </a-drawer>
</template>
<script>
import {addProject, editProject} from "@/api/message/message";
import {queryList} from "@/api/basis/basis.js";
import {valiPhone} from '@/utils/validate'
import AreaSelect from '@/components/AreaSelect/index.vue'
import {isDeal} from '@/utils/utils'

export default {
  name: "ProjectEditDrawer",
  components: {AreaSelect},
  data() {
    return {
      upLoadUrl: process.env.VUE_APP_BASE_API + "/pic/upload",
      dialogVisible: false,
      title: "新增项目",
      roleName: undefined,
      form: {
        id: undefined,
        type: '1',
        code: undefined,
        name: undefined,
        releaseTime: undefined,
        status: undefined,
        sorted: undefined,
        description: undefined,
        introduePic: undefined,
        innerPic: undefined,
        contactInfo: undefined,
        license: undefined,
        introduction: undefined,
        videoUrl: undefined,
        upbeatUser:undefined,
        merchantId:undefined,
        customerServiceLink:undefined,
        consignee:undefined,
        consigneePhone: undefined,
        selectAddress: undefined,
        detailedAddress: undefined,
        copyId: undefined,
      },
      addressInfo: {
        province: null,
        provinceCode: null,
        city: null,
        cityCode: null,
        area: null,
        areaCode: null,
        street: null,
        streetCode: null,
      },
      rules: {
        code: [
          {required: true, message: "请填写项目代码", trigger: "change"},
        ],
        name: [
          {required: true, message: "请填写项目名称", trigger: "change"},
        ],
        releaseTime: [
          {required: true, message: "请选择发布时间", trigger: "change"},
        ],
        status: [
          {required: true, message: "请选择项目状态", trigger: "change"},
        ],
        introduePic: [
          {required: true, message: "请上传列表项目图", trigger: "change"},
        ],
        upbeatUser: [
          {required: true, message: "请选择商家", trigger: "change"},
        ],
        // consignee: [
        //   {required: true, message: "请填写联系人", trigger: "change"},
        // ],
        // consigneePhone: [
        //   { required: true, message: "请填写联系人手机号", trigger: "change" },
        //   { validator: valiPhone, trigger: "blur" },
        // ],
        // selectAddress: [
        //   {required: true, message: "请选择退货/自提地址", trigger: "change"},
        // ],
        // detailedAddress: [
        //   {required: true, message: "请填写详细地址", trigger: "change"},
        // ],
      },
      acceptImg: '.png,.jpg,.jpeg',
      acceptFile: '.doc,.docx,.pdf,.png,.jpg,.jpeg',
      acceptFileArr: ['pdf', 'png', 'jpg', 'jpeg'],
      saving: false,
      saveMemberList: [],
      memberList: []
    };
  },
  created() {
    this.getMember()
  },
  methods: {
    isDeal,
    changeType() {
      this.$nextTick(() => {
        this.$refs?.form?.clearValidate();
      })

    },
    init(row) {
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs?.form?.clearValidate();
      })
      if (row.id) {
        this.title = "编辑项目";
      }
      const {province, provinceCode, city, cityCode, area, areaCode, street, streetCode} = row;
      this.addressInfo = {
        province,
        provinceCode,
        city,
        cityCode,
        area,
        areaCode,
        street,
        streetCode,
      }
      Object.assign(this.form, row, {selectAddress: [provinceCode, cityCode, areaCode, streetCode]})
    },

    cleanForm() {
      this.dialogVisible = false;
      this.form = this.$options.data().form;
      this.$emit("getData");
    },

    getMember() {
      queryList({roleId: 3}).then((res) => {
        this.memberList = res.data;
        this.saveMemberList = res.data;
      });
    },

    filterMethod(query) {
      if (!query) {
        this.saveMemberList = this.memberList;
      } else {
        this.saveMemberList = this.memberList.filter((item) => {
          return (
            (item.nickName + '').indexOf(query) > -1 ||
            (item.accountNumber + '').indexOf(query) > -1 ||
            (item.userId + '').indexOf(query) > -1 ||
            (item.userName + '').indexOf(query) > -1
          );
        });
      }
    },

    saveForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const {jsonList, ...rest} = this.form;
          const {id, releaseTime} = this.form;
          const params = {
            ...rest,
            ...this.addressInfo,
            releaseTime: releaseTime ? this.$dayjs(releaseTime).format("YYYY-MM-DD") : undefined,
          };
          let promise = !id ? addProject(params) : editProject(params);
          this.saving = true;
          promise.then((res) => {
            this.$message.success("保存成功");
            this.cleanForm();
          }).finally(() => {
            this.saving = false;
          });
        } else {
        }
      });
    },
  },
};
</script>

<style>
.dialogStyle {
  max-height: calc(100vh - 140px);
  overflow: auto;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
</style>

<style lang="less" scoped>
.avatar-uploader-icon {
  width: 110px;
  height: 110px;
  line-height: 80px;
}

.avatar-uploader-text {
  position: absolute;
  left: 49%;
  transform: translate(-50%, 0);
  top: 55px;
  color: rgba(0, 0, 0, 0.88);
}

.avatar {
  width: 110px;
  height: 110px;
}

::v-deep .el-form-item__label {
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
}

.el-row {
  margin-right: 0 !important;
}

.del-btn {
  margin-left: 30px;
  color: #F5222D;
}

.el-divider--horizontal {
  margin: 0 0 24px 0;
}
</style>
