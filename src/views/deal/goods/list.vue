<template>
  <div class="whole">
    <el-table-search
      :search="tableProps"
      @onSearch="handleSearch"
      @onReset="handleReset"
    >
    </el-table-search>
    <el-table
      :data="tableData"
      border
      v-loading="loading"
      style="width: 100%"
      @expand-change="handleExpand"
    >
      <!--      <el-table-column type="expand">
              <template slot-scope="props">
                <div v-for="item in props.row.goodsInfoList" :key="item.id" class="item-container">
                  <img :src="item.goodsInfoImg || defaultImg" class="img-item" />
                  <div>
                    <div class="cell">
                      <label class="label">规格：</label>
                      <span class="specification">{{ item.specValue }}</span>
                    </div>
                    <div class="cell">
                      <label class="label">SKU编码：</label>
                      <span class="specification">{{ item.goodsInfoNo }}</span>
                    </div>
                    <div class="cell">
                      <label class="label">市场价：</label>
                      {{ `￥${item.marketPrice?.toFixed(2) || '0.00'}` }}
                    </div>
                    <div class="cell">
                      <label class="label">上下架：</label>
                      {{ item.addedFlag == 0 ? '下架' : '上架' }}
                    </div>
                    <div class="cell">
                      <label class="label">库存：</label>
                      {{ item.stock }}
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>-->
      <el-table-column prop="goodsName" label="商品" width="200"/>
      <el-table-column prop="goodsType" label="商品类型" width="100">
        <template slot-scope="scope">
          {{ scope.row.goodsType === 0 ? '项目商品' : '提货商品' }}
        </template>
      </el-table-column>
      <el-table-column prop="lotName" label="凭证" width="160">
        <template slot-scope="scope">
          {{ scope.row.lotName || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="projectName" label="所属项目" width="200"/>
      <el-table-column prop="marketPrice" label="零售价"/>
      <el-table-column prop="stock" label="库存"/>
      <el-table-column prop="lotNum" label="凭证数量"/>
      <el-table-column prop="goodsSalesNum" label="实际销量"/>
      <el-table-column prop="shamSalesNum" label="注水销量" width="230">
        <template slot-scope="scope">
          <el-input-number
            v-model="scope.row.shamSalesNum"
            @blur="editGoodsShamSaleNum(scope.row)"
            :min="0"
            controls-position="right"
          />
        </template>
      </el-table-column>
      <el-table-column prop="addedFlag" label="上下架">
        <template slot-scope="scope">
          {{ addedFlagEnum[scope.row.addedFlag] }}
        </template>
      </el-table-column>
      <el-table-column prop="hotGoods" label="是否热门">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.hotGoods"
            :active-value="1"
            :inactive-value="0"
            @change="changeHotGoods(scope.row.id)"
            ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="320" fixed="right">
        <template slot-scope="scope">
          <el-button @click="editGoods(scope.row.id)" type="text">编辑</el-button>
          <el-button @click="onOffSpu(scope.row)" type="text">
            {{ scope.row.addedFlag ? '下架' : '上架' }}
          </el-button>
          <el-button v-if="scope.row.approveStatus === 2" @click="goodsPublish(scope.row)" type="text">
            发行
          </el-button>
          <template v-if="scope.row.goodsType !== 1">
            <el-button v-if="!scope.row.lotId" @click="relatedLots(scope.row)" type="text">关联凭证</el-button>
            <el-button v-else @click="relatedLots(scope.row)" type="text">关联数量</el-button>
          </template>

          <el-button type="text" @click="toTradeSettingPage(scope.row)">交易设置</el-button>

<!--          <el-button @click="confirmDelete(scope.row.id)" type="text">删除</el-button>-->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="pagination.total > 0"
      :total="pagination.total"
      :page.sync="pagination.current"
      :limit.sync="pagination.size"
      @pagination="handlePagination"
    />
    <el-dialog :title="rowId ? '编辑' : '新建'" :visible.sync="addVisible">
      <el-form :model="form" :rules="rules" ref="form" label-width="100px">
        <el-form-item label="最低价" prop="priceMin">
          <el-input-number v-model="form.priceMin" :min="0"/>
        </el-form-item>
        <el-form-item label="最高价" prop="priceMax">
          <el-input-number v-model="form.priceMax" :min="0"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">{{ rowId ? '确定' : '新增' }}</el-button>
      </div>
    </el-dialog>
    <RelatedLots ref="relatedLots" @getData="fetchTableData(formModel)"></RelatedLots>
    <TradeSetting ref="tradeSettingRef" @getData="fetchTableData(formModel)"></TradeSetting>
  </div>
</template>

<script>
import {
  addPriceRange,
  deleteGoodsSpu,
  editGoodsShamSaleNum,
  editPriceRange, getGoodsDetailTradeConfig,
  getGoodsSpuPage, goodsPublish, hotSpu,
  offSpu,
  onSpu,
} from '@/api/GoodsLabelController';
import RelatedLots from "./relatedLots.vue";
import TradeSetting from "./tradeSetting.vue";

export default {
  name: 'DealGoodsList',
  components: {
    RelatedLots,
    TradeSetting
  },
  data() {
    return {
      tableData: [],
      form: {
        priceMin: '',
        priceMax: '',
      },
      addVisible: false,
      rowId: null,
      saleTypeEnum: {
        0: '批发',
        1: '零售',
        2: '议价',
      },
      addedFlagEnum: {
        0: '下架',
        1: '上架',
      },
      rules: {
        priceMin: [{required: true, message: '请输入最低价', trigger: 'blur'}],
        priceMax: [{required: true, message: '请输入最高价', trigger: 'blur'}],
      },
      formModel: {
        goodsName: undefined,
        goodsNo: undefined,
        // saleType: '',
        addedFlag: undefined,
        projectName: undefined,
      },
      pagination: {
        current: 1,
        size: 10,
        total: 0,
      },
      loading: false,
    };
  },
  computed: {
    tableProps() {
      return {
        model: this.formModel,
        columns: [
          {
            title: '商品名称',
            dataIndex: 'goodsName',
          },
          {
            title: '凭证名称',
            dataIndex: 'lotName',
          },
          // {
          //   title: 'SPU编码',
          //   dataIndex: 'goodsNo',
          // },
          // {
          //   title: '销售类型',
          //   dataIndex: 'saleType',
          //   valueEnum: {
          //     0: {text: '批发'},
          //     1: {text: '零售'},
          //     2: {text: '议价'},
          //   },
          // },
          {
            title: '上下架',
            dataIndex: 'addedFlag',
            valueEnum: {
              0: {text: '下架'},
              1: {text: '上架'},
              // 2: { text: '部分上架' },
            },
          },

          {
            title: '商品类型',
            dataIndex: 'goodsType',
            valueEnum: {
              0: {text: '项目商品'},
              1: {text: '提货商品'},
            },
          },
          {
            title: '所属项目',
            dataIndex: 'projectName',
          },
        ],
      }
    }
  },
  methods: {
    changeHotGoods(id) {
      hotSpu(id).then(res => {
        if (res.code === 200) {
          this.$message.success('操作成功!');
        } else {
          this.$message({
            type: 'error',
            message: res.msg,
          });
        }
      });
    },
    // 跳转交易设置页面
    toTradeSettingPage(row) {
      console.log('row....',row);
      getGoodsDetailTradeConfig({
        goodsId: row.id,
      }).then(res => {
        this.$refs.tradeSettingRef.indexChoose({
          tradeConfigEntity: res.data ?{
            goodsId: row.id,
            ...res.data
          }:{
            goodsId: row.id,
          },
        });
      });
    },
    async fetchTableData(data) {
      console.log(data);
      const {current, size} = this.pagination
      const params = {current, size, ...data};
      this.loading = true;
      const res = await getGoodsSpuPage(params).finally(
        ()=>{ this.loading = false }
      )
      this.tableData = res.data.records;
      this.pagination.total = res.data.total;
    },
    handleExpand(row, expanded) {
      if (expanded) {
        this.fetchGoodsSkuList(row);
      }
    },
    handleSearch(params) {
      this.pagination.current = 1;
      this.pagination.size = 10;
      this.fetchTableData(params);
    },
    handleReset() {
      this.formModel = this.$options.data().formModel
      this.fetchTableData();
    },
    handlePagination(pagination) {
      this.pagination.current = pagination.page;
      this.pagination.size = pagination.limit;
      this.fetchTableData(this.formModel);
    },
    async fetchGoodsSkuList(row) {
      const res = await getGoodsSkuList({spuId: row.id});
      this.$set(row, 'goodsInfoList', res.data);
    },
    async editGoodsShamSaleNum(row) {
      const res = await editGoodsShamSaleNum({goodsId: row.id, shamSalesNum: row.shamSalesNum});
      if (res.code === 200) {
        this.$message.success('操作成功!');
      } else {
        this.$message.error('操作失败!');
      }
    },
    async onOffSpu(row) {
      const res = row.addedFlag ? await offSpu(row.id) : await onSpu(row.id);
      if (res.code === 200) {
        this.$message.success('操作成功!');
        this.fetchTableData(this.formModel);
      } else {
        this.$message.error('操作失败!');
      }
    },
    async goodsPublish(row) {
      const res = await goodsPublish(row.id)
      if (res.code === 200) {
        this.$message.success('操作成功!');
        this.fetchTableData(this.formModel);
      } else {
        this.$message.error('操作失败!');
      }
    },
    editGoods(id) {
      this.$router.push(`/deal/goods/publish?goodsId=${id}`);
    },
    confirmDelete(id) {
      this.$confirm('确认删除?', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning',
      }).then(() => {
        this.deleteGoods(id);
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    async deleteGoods(id) {
      const res = await deleteGoodsSpu(id);
      if (res.code === 200) {
        this.$message.success('删除成功！');
        this.fetchTableData();
      } else {
        this.$message.error('删除失败！');
      }
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const action = this.rowId ? editPriceRange : addPriceRange;
          const res = await action(this.form);
          if (res.code === 200) {
            this.$message.success(`${this.rowId ? '修改' : '添加'}成功！`);
            this.fetchTableData();
            this.addVisible = false;
          } else {
            this.$message.error(`${this.rowId ? '修改' : '添加'}失败！`);
          }
        }
      });
    },
    relatedLots(row) {
      if (!row.lotId) { // 关联凭证
        this.$refs.relatedLots.goodsId = row.id;
        this.$refs.relatedLots.dialogVisible = true;
        this.$refs.relatedLots.projectId = row.projectId;
        this.$refs.relatedLots.init()
      } else { // 关联数量
        this.$refs.relatedLots.goodsId = row.id;
        this.$refs.relatedLots.lotId = row.lotId;
        this.$refs.relatedLots.numberVisible = true;
        this.$refs.relatedLots.init()
      }
    }
  },
  watch: {
    '$route.query.form': {
      handler(newVal) {
        if (newVal === 'editPage') {
          // 编辑后返回页面时重新获取数据
          this.fetchTableData(this.formModel);
        }
      },
      immediate: false
    }
  },
  mounted() {
      this.fetchTableData();
  },
};
</script>

<style scoped lang="less">
.item-container {
  display: flex;
  flex-direction: row;
  margin: 10px 0;
  height: 124px;
}

.img-item {
  width: 60px;
  height: 60px;
  margin: 5px;
  border: 1px solid #ddd;
}

.cell {
  color: #999;
  min-width: 200px;
  text-align: left;
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
}

.label {
  color: #999;
  min-width: 80px;
  text-align: right;
  display: inline-block;
}

.specification {
  min-width: 120px;
  max-height: 62px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
</style>
