<template>
  <div class="goods-publish p-24">
    <el-card class="list-card-default" shadow="never" v-loading="loading">
      <div slot="header" class="flex a-center justify-between">
        <span class="fs-16 text-default">基本信息</span>
      </div>
      <el-form ref="form" :model="form" :rules="rules" label-suffix="：">
        <el-row :gutter="32">
          <el-col :span="12">
            <el-form-item label="商品名称" label-width="100px" prop="goodsName">
              <el-input
                :maxlength="40"
                v-model="form.goodsName"
                placeholder="请输入"
                :disabled='hasChain'
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="商品价格"
              label-width="100px"
              prop="marketPrice"
            >
              <el-input-number
                size="small"
                placeholder="请输入"
                v-model="form.marketPrice"
                controls-position="right"
                :min="0"
                :precision="2"
                :disabled='hasChain'
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="副标题" label-width="100px" prop="goodsSubName">
              <el-input
                :maxlength="40"
                v-model="form.goodsSubName"
                placeholder="请输入"
                :disabled='hasChain'
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="库存"
              label-width="100px"
              prop="stock"
            >
              <el-input-number
                size="small"
                placeholder="请输入"
                v-model="form.stock"
                controls-position="right"
                :min="0"
                :precision="0"
                :disabled="hasChain"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="商品类型"
              label-width="100px"
              prop="goodsType"
            >
<!--              0 项目商品 1 提货商品-->
              <el-select
                :clearable="false"
                v-model="form.goodsType"
                @change="changeGoodsType"
                placeholder="请选择"
              >
                <el-option label="项目商品" :value="0" />
                <el-option label="提货商品" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属项目" label-width="100px" prop="goodsSubtitle">
              <el-select v-model="form.goodsSubtitle" placeholder="请选择" filterable>
                <el-option v-for="v in projectList" :key="v.code" :label="v.name" :value="v.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
<!--          <template v-if="form.goodsType == 1" >-->
<!--            <el-col :span="12" >-->
<!--              <el-form-item-->
<!--                label="所属商家"-->
<!--                label-width="100px"-->
<!--                prop="upbeatId"-->
<!--              >-->
<!--                <el-select-->
<!--                  style="width: 100%"-->
<!--                  v-model="form.upbeatId"-->
<!--                  placeholder="请按照手机/ID/昵称/姓名搜索"-->
<!--                  filterable-->
<!--                  @clear="saveMemberList = memberList"-->
<!--                  :filter-method="filterMethod"-->
<!--                >-->
<!--                  <el-option-->
<!--                    v-for="item in saveMemberList"-->
<!--                    :key="item.userId"-->
<!--                    :label="item.userName"-->
<!--                    :value="item.userId"-->
<!--                  >-->
<!--                    <span> [{{ item.userId }}]{{ item.userName }} </span>-->
<!--                  </el-option>-->
<!--                </el-select>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--          </template>-->


          <el-col :span="12">
            <el-form-item label="平台类目" label-width="100px" prop="cateId">
              <a-tree-select
                v-model="form.cateId"
                :tree-data="treeData"
                :replaceFields="{ title: 'cateName', key: 'cateId', value: 'cateId', children: 'children' }"
                placeholder="请选择"
                tree-default-expand-all
                :dropdownStyle="{maxHeight: '400px', overflowY: 'auto'}"
                @change="handleCateChange"
                show-search
                :filter-tree-node="filterTreeNode"
              ></a-tree-select>
            </el-form-item>
          </el-col>
<!--          <el-col :span="12">-->
<!--            <el-form-item label="计量单位" label-width="100px">-->
<!--              <el-input v-model="form.goodsUnit"></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->

          <el-col :span="12">
            <el-form-item label="主图角标" label-width="100px">
              <el-select v-model="form.mainLabelIdStr" multiple :multiple-limit="1" placeholder="请选择" filterable>
                <el-option
                  v-for="item in labelListOne"
                  :key="item.id"
                  :label="item.labelName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="商品标签" label-width="100px">
              <el-select  style="width: 100%" v-model="form.labelIdStr" multiple placeholder="请选择" filterable>
                <el-option
                  v-for="item in labelListTwo"
                  :key="item.id"
                  :label="item.labelName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="运费模板" prop="freightTempId" label-width="100px">
              <el-select v-model="form.freightTempId" multiple :multiple-limit="1"  placeholder="请选择">
                <el-option
                  v-for="item in freightTempList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <template v-if="form.goodsType == 1">
            <el-col :span="9" >
              <el-form-item
                label="限购数量"
                label-width="100px"
                prop="purchaseLimit"
              >
                <el-input-number
                  style="width: 100%;"
                  size="small"
                  placeholder="请输入"
                  v-model="form.purchaseLimit"
                  controls-position="right"
                  :min="1"
                  :max="999999"
                  :disabled="form.noLimit"
                  :precision="0"
                ></el-input-number>
              </el-form-item>
            </el-col>
          </template>

          <template v-if="form.goodsType == 1">
            <el-col :span="3">
              <el-form-item
                label="是否不限"
                label-width="110px"
              >
                <el-checkbox v-model="form.noLimit" label="" @change="changeNoLimit"></el-checkbox>
              </el-form-item>
            </el-col>
          </template>


          <el-col :span="24" v-if="goodsProList && goodsProList.length > 0">
            <div class="fs-16">属性设置</div>
          </el-col>
          <el-col class="my-16" :span="24" v-for="(item, index) in goodsProList" :key="item.propId">
            <el-col :span="3" style="text-align: right">
              {{ item.propName }}:
            </el-col>
            <el-col :span="9">
              <el-select
                v-if="item.propType === 0"
                v-model="goodsProValueList[index].detailIds"
                multiple
                placeholder="请选择"
                style="width: 100%"
                @change="changePropValue($event, index, item.propType)"
              >
                <el-option
                  v-for="i in item.detailList"
                  :key="i.id"
                  :label="i.detailName"
                  :value="i.id"
                ></el-option>
              </el-select>
              <el-input
                v-else
                v-model="goodsProValueList[index].detailName"
                @input="changePropValue($event, index, item.propType)"
              ></el-input>
            </el-col>
          </el-col>

          <el-col :span="24">
            <el-form-item label="商品图片" label-width="100px" prop="goodsImageList">
              <el-upload
                :action="uploadUrl"
                list-type="picture-card"
                :limi="10"
                :on-success="handleImgSuccess"
                :before-upload="beforeUpload"
                :file-list="form.goodsImageList"
                :on-preview="handlePictureCardPreview"
                :on-remove="handleRemove"
                :disabled='hasChain'
              >
                <i class="el-icon-plus"></i>
              </el-upload>
              <el-dialog :visible.sync="dialogVisible">
                <img width="100%" :src="dialogImageUrl" alt="" />
              </el-dialog>
              <div>建议尺寸：800*800px，单张大小不超过2M，最多可上传10张</div>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item
              label="商品详情"
              label-width="100px"
              prop="goodsDetail"
              class="hightedit"
            >
              <editor
                v-model="form.goodsDetail"
                ref="editor"
                :min-height="192"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item
              label="购买须知"
              label-width="100px"
              prop="goodsPurchaseNotes"
              class="hightedit"
            >
              <editor
                v-model="form.goodsPurchaseNotes"
                ref="editor"
                :min-height="192"
              />
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>

      <el-row>
        <el-col :span="24" class="text-center">
          <el-button type="primary" @click="handleSave(undefined)">
            保存
          </el-button>
          <el-button type="primary" v-if="!isEdit" @click="handleSave(1)">
            保存并上架
          </el-button>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>
<script>
import { getToken } from "@/utils/auth";
import {
  addGoods,
  editGoods,
  getGoodsCateList,
  getGoodsDetail,
  getGoodsProProp,
  loadFreightTempList,
  loadGoodsLabelList,
} from "@/api/GoodsLabelController";
import {mapGetters} from "vuex";
import {projectList} from "@/api/message/message";
import {queryList} from "@/api/basis/basis";

export default {
  name: "GoodsPublish",
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + "/pic/upload", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      form: {
        goodsName: "",
        goodsSubtitle: "",
        goodsSubName: '',
        marketPrice: undefined,
        pot: "",
        pug: "",
        capacity: "",
        level: "",
        tea: "",
        craft: "",
        author: "",
        professional: "",
        goodsDetail: "",
        goodsPurchaseNotes: "",
        goodsImageList: [],
        tips: [],

        stock: undefined,
        goodsType: 0,
        purchaseLimit: undefined,
        // upbeatId: null,
        cateId: null,
        goodsUnit: '',
        labelIdStr: [],
        mainLabelIdStr: [],
        freightTempId: [],
        noLimit: false,
        loading: false,
      },
      rules: {
        goodsSubtitle: [
          {
            required: true,
            message: "请选择所属项目",
            trigger: "change",
            whitespace: true,
          },
        ],
        goodsName: [
          {
            required: true,
            message: "请输入商品名称",
            trigger: "blur",
            whitespace: true,
          },
        ],
        marketPrice: [
          { required: true, message: "请输入商品价格", trigger: "blur" },
        ],
        stock: [
          { required: true, message: "请输入库存", trigger: "blur" },
        ],
        goodsType: [
          { required: true, message: "请选择商品类型", trigger: "change" },
        ],
        purchaseLimit: [
          { required: true, message: "请输入限购数量", trigger: "blur" },
        ],
        // upbeatId: [
        //   { required: true, message: "请选择所属商家", trigger: "change" },
        // ],
        goodsImageList: [
          { required: true, message: "请选择商品图片", trigger: "change" },
        ],
        goodsDetail: [
          {
            required: true,
            message: "请输入商品详情",
            trigger: "blur",
            whitespace: true,
          },
        ],
        goodsPurchaseNotes: [
          {
            required: false,
            message: "请输入购买须知",
            trigger: "blur",
            whitespace: true,
          },
        ],
        cateId: [{ required: true, message: '请选择平台类目', trigger: 'change' }],
        freightTempId: [{ required: true, message: '请选择运费模板', trigger: 'change' }],
      },
      dialogImageUrl: "",
      dialogVisible: false,
      inputVisible: false,
      inputValue: "",
      treeData: [],
      labelList: [],
      labelListOne: [],
      labelListTwo: [],
      goodsProList: [],
      goodsProValueList: [],
      freightTempList: [],
      isEdit: false,
      goodsId: null,
      goodsInfo: {},
      indexImgList: [],
      indexImgList2: [],
      indexImgList3: [],
      readOnly: false,
      specList: [],
      projectList: [],
      saveMemberList: [],
      memberList: []
    };
  },
  computed: {
    ...mapGetters(['isAdmin']),
    hasChain() {
      return !!this.goodsInfo.approveStatus && this.goodsInfo.approveStatus !== 0 && !this.isAdmin
    },
  },
  created() {
    this.init()
  },
  mounted() {
  },
  methods: {
    changeGoodsType(){
      this.$nextTick(()=>{
        this.$refs.form.clearValidate()
      })
    },
    changeNoLimit(value) {
      if (value) {
        this.form.purchaseLimit = 999999
      }else {
        this.form.purchaseLimit = undefined
      }
    },
    async getMember() {
      await queryList({roleId: 3}).then((res) => {
        this.memberList = res.data;
        this.saveMemberList = res.data;
      });
    },
    filterMethod(query) {
      if (!query) {
        this.saveMemberList = this.memberList;
      } else {
        this.saveMemberList = this.memberList.filter((item) => {
          return (
            (item.nickName + '').indexOf(query) > -1 ||
            (item.accountNumber + '').indexOf(query) > -1 ||
            (item.userId + '').indexOf(query) > -1 ||
            (item.userName + '').indexOf(query) > -1
          );
        });
      }
    },
    filterTreeNode(input, node) {
      return node && node.componentOptions && node.componentOptions.propsData.title.includes(input);
    },
    processTreeData(data) {
      return data.map((item) => {
        if (item.goodsCateList && item.goodsCateList.length) {
          this.processTreeData(item.goodsCateList)
        } else {
          delete item.goodsCateList;
        }
        return {
          ...item,
        };
      });
    },

    async loadGoodsDetail() {
      this.loading = true;
      const res = await getGoodsDetail(this.goodsId).finally(
        ()=>{
          this.loading = false;
        }
      )
      if (res && res.code === 200 && res.data) {
        let item = res.data;
        this.goodsInfo = item;
        if (item.cateId) {
          const propRes = await getGoodsProProp(item.cateId);
          if (propRes && propRes.data) {
            this.goodsProValueList = item.goodsProRelList.concat(propRes.data.filter(v => !item.goodsProRelList.some(r => r.propId === v.propId)).map(v => {
              const { propId, propName } = v;
              return {
                detailIds: undefined,
                detailName: undefined,
                goodsId: item.id,
                propId,
                propName
              }
            }))
            this.goodsProList = propRes.data;
//            console.log(this.goodsProValueList, this.goodsProList)
          }
        }
        // console.log('6666',item.labelIdStr.split(','),item.labelIdStr,this.labelListOne,this.labelListTwo, this.labelListOne.filter(v => item.labelIdStr.split(',').includes(v.id+'')));
        let labelIdStrOne = this.labelListOne.filter(v => item.labelIdStr.split(',').includes(v.id+'')).map(v => v.id).join(',')
        let labelIdStrTwo = this.labelListTwo.filter(v => item.labelIdStr.split(',').includes(v.id+'')).map(v => v.id).join(',')
        this.form = {
          ...item,
          freightTempId: item.freightTempId ? [item.freightTempId] : [],
          goodsSubtitle: item?.projectCode|| '',
          purchaseLimit: item.purchaseLimit ? item.purchaseLimit : undefined,
          noLimit: item.purchaseLimit === 999999,
          labelIdStr: labelIdStrTwo ? labelIdStrTwo.split(',').map(v => parseInt(v)).filter(v => !!v) : undefined,
          mainLabelIdStr : labelIdStrOne ? labelIdStrOne.split(',').map(v => parseInt(v)).filter(v => !!v) : undefined,
        }
        console.log('99999,',this.form);
        this.readOnly = true;
        this.readOnly = false;
        if (item.goodsSpecList) {
          this.specList = item.goodsSpecList;
        }
        if (item.goodsImageList) {
          let arr = item.goodsImageList.map((img, index) => ({
            uid: index,
            name: '',
            status: 'done',
            thumbUrl: img.artworkUrl,
            url: img.artworkUrl,
            response: { code: 0, result: { url: img.artworkUrl } },
            ...img,
          }));
          this.form.goodsImageList = arr;
        }
        if (item.goodsVideo) {
          this.indexImgList2 = [{ uid: '-1', name: '', status: 'done', thumbUrl: item.goodsVideo, url: item.goodsVideo }];
        }
        if (item.goodsExplicationVideo) {
          this.indexImgList3 = [{ uid: '-1', name: '', status: 'done', thumbUrl: item.goodsExplicationVideo, url: item.goodsExplicationVideo }];
        }
        this.$refs.editor.Quill.enable(false);
        this.$nextTick(()=>{
          this.$refs.editor.Quill.enable(true);
          this.$refs.editor.Quill.blur();
        });
      }
    },
    changePropValue(value, index, type) {
      const ary = JSON.parse(JSON.stringify([...this.goodsProValueList]));
      if (type === 0) {
        ary[index].detailIds = value;
      } else {
        ary[index].detailName = value.target.value;
      }
      this.goodsProValueList = ary;
    },
    async handleAddGoods(addedFlag) {
      try {
        await this.$refs.form.validate();
        if (this.form.goodsImageList.length === 0) {
          this.$message.error('请上传商品图片！');
          return;
        }
        let ary = this.form.goodsImageList.map(item => ({ artworkUrl: item.url }));
        this.form.goodsImageList = ary;
        if (this.indexImgList2.length > 0) {
          this.form.goodsVideo = this.indexImgList2[0].url;
        }
        if (this.indexImgList3.length > 0) {
          this.form.goodsExplicationVideo = this.indexImgList3[0].url;
        }
        this.form.goodsInfoList = [
          {
            marketPrice: this.form.marketPrice,
            goodsInfoImage: this.form.goodsImageList[0].artworkUrl,
            stock: this.form.stock
          }
        ];
        // this.form.upbeatId =  this.form.goodsType == 1 ? this.form.upbeatId : null
        this.form.purchaseLimit = this.form.goodsType == 1 ? this.form.purchaseLimit : null

        this.form.goodsSpecList = this.specList;
        this.form.goodsProRelList = this.goodsProValueList;
        if (addedFlag) this.form.addedFlag = addedFlag;
        let checkLabelListOne = this.form.labelIdStr ? this.labelList.filter(v => this.form.labelIdStr.includes(v.id)) : [];
        let checkLabelListTwo = this.form.mainLabelIdStr ? this.labelList.filter(v => this.form.mainLabelIdStr.includes(v.id)) : [];
        const checkLabelList = [...checkLabelListOne, ...checkLabelListTwo];
        const res = await addGoods({
          ...this.form,
          freightTempId: this.form.freightTempId?.[0] || '',
          labelIdStr: checkLabelList.map(v => v.id).join(','),
          labelNameStr: checkLabelList.map(v => v.labelName).join(','),
          projectId: (this.projectList.find(v => v.code === this.form.goodsSubtitle) || {}).id,
          goodsSubtitle: (this.projectList.find(v => v.code === this.form.goodsSubtitle) || {}).name
        });
        if (res && res.code === 200) {
          this.$message.success('添加成功！');
          this.$router.push({ path: '/goods/dealGoodsList?form=editPage' });
        } else {
          this.$message.error('添加失败！');
        }
      } catch (error) {
        console.error(error);
      }
    },
    async handleEditGoods(addedFlag) {
      try {
        await this.$refs.form.validate();
        if (this.form.goodsImageList.length > 0) {
          let ary = this.form.goodsImageList.map(item => (item.id ? { artworkUrl: item.url, id: item.id } : { artworkUrl: item.url }));
          this.form.goodsImageList = ary;
        }
        if (this.indexImgList2.length > 0) {
          this.form.goodsVideo = this.indexImgList2[0].url;
        }
        if (this.indexImgList3.length > 0) {
          this.form.goodsExplicationVideo = this.indexImgList3[0].url;
        }
        this.form.goodsInfoList = [
          {
            goodsInfoId: this.goodsInfo.goodsInfoList[0].id,
            marketPrice: this.form.marketPrice,
            goodsInfoImage: this.form.goodsImageList[0].artworkUrl,
            stock: this.form.stock,
          }
        ];
        // this.form.upbeatId =  this.form.goodsType == 1 ? this.form.upbeatId : null
        this.form.purchaseLimit = this.form.goodsType == 1 ? this.form.purchaseLimit : null
        this.form.id = this.goodsId;
        this.form.goodsSpecList = this.specList;
        if (addedFlag) this.form.addedFlag = addedFlag;
        this.form.goodsProRelList = this.goodsProValueList;
        console.log('-------->',this.form.labelIdStr,this.form.mainLabelIdStr);
        let checkLabelListOne = this.form.labelIdStr ? this.labelList.filter(v => this.form.labelIdStr.includes(v.id)) : [];
        let checkLabelListTwo = this.form.mainLabelIdStr ? this.labelList.filter(v => this.form.mainLabelIdStr.includes(v.id)) : [];
        const checkLabelList = [...checkLabelListOne, ...checkLabelListTwo];
        const res = await editGoods({
          ...this.form,
          freightTempId: this.form.freightTempId?.[0] || '',
          labelIdStr: checkLabelList.map(v => v.id).join(','),
          labelNameStr: checkLabelList.map(v => v.labelName).join(','),
          projectId: (this.projectList.find(v => v.code === this.form.goodsSubtitle) || {}).id,
          goodsSubtitle: (this.projectList.find(v => v.code === this.form.goodsSubtitle) || {}).name
          // projectId: (this.projectList.find(v => v.name === this.form.goodsSubtitle) || {}).id
        });
        if (res && res.code === 200) {
          this.$message.success('操作成功！');
          this.$router.push({ path: "/goods/dealGoodsList?form=editPage" });
        } else {
          this.$message.error('操作失败！');
        }
      } catch (error) {
        console.error(error);
      }
    },
    async getLabelList() {
      await loadGoodsLabelList({labelVisible: 1}).then((res) => {
        if (res.code === 200) {
          this.labelList = res.data;
          this.labelListOne = res.data.filter(v => v.labelType == 1);
          this.labelListTwo = res.data.filter(v => v.labelType == 2);
        }
      });
    },
    handleCateChange(value) {
      this.$nextTick(()=>{
        this.$refs.form.validateField('cateId')
      })
      this.goodsProValueList = [];
      this.goodsProList = [];
      getGoodsProProp(value).then(res => {
        if (res.data) {
          const goodsProList = res.data;
          let ary = [];
          goodsProList.forEach((item) => {
            let data = {
              goodsId: this.goodsId || null,
              propId: item.propId,
              propName: item.propName,
              detailIds: [],
              detailName: '',
            };
            if (this.goodsProValueList && this.goodsProValueList.length > 0) {
              this.goodsProValueList.forEach((i) => {
                if (i.propId === item.propId) {
                  data.detailIds = i.detailIds;
                  data.detailName = i.detailName;
                }
              });
            }
            ary.push(data);
          });
          this.goodsProValueList = ary;
          this.goodsProList = res.data;
        }
      });
    },
    async handleSave(flag) {
      if (this.isEdit) {
        this.handleEditGoods(flag);
      } else {
        this.handleAddGoods(flag);
      }
    },
    async init() {
      await this.getMember()
      await this.getLabelList()
      if (this.$route.query.goodsId) {
        this.isEdit = true;
        this.goodsId = this.$route.query.goodsId;
        this.loadGoodsDetail(this.goodsId);
      }
      try {
        // labelRes
        const [freightRes, cateRes, projectRes] = await Promise.all([
          // loadGoodsLabelList({ labelVisible: 1 }),
          loadFreightTempList({ validFlag: true }),
          getGoodsCateList(),
          projectList()
        ]);
        // if (labelRes.code === 200) {
        //   this.labelList = labelRes.data;
        //   this.labelListOne = labelRes.data.filter(v => v.labelType === 1);
        //   this.labelListTwo = labelRes.data.filter(v => v.labelType === 2);
        // }
        if (freightRes.code === 200) {
          this.freightTempList = freightRes.data;
        }
        if (cateRes.code === 200) {
          this.treeData = this.processTreeData(cateRes.data);
        }
        if (projectRes.code === 200) {
          this.projectList = projectRes.data.filter(v => v.type === '1')
        }
      } catch (error) {
        console.error('初始化数据失败', error);
      }
    },

    handleRemove(file, fileList) {
      this.form.goodsImageList = fileList;
      this.$refs.form.validateField("goodsImageList");
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleImgSuccess(res) {
      if (res.status == "done") {

        this.form.goodsImageList.push({
          name: res.name.substring(res.name.lastIndexOf("/") + 1),
          url: res.name,
        });
        this.$nextTick(()=>{
          this.$refs.form.validateField("goodsImageList");
        })
      }
    },

    beforeUpload(file) {
      const type = ["image/jpeg", "image/jpg", "image/png", "image/svg"];
      const isJPG = type.includes(file.type);
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error(`图片格式错误!`);
        return false;
      }
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过2MB!");
        return false;
      }
      return isJPG && isLt2M;
    },
  },
};
</script>
<style lang="scss" scoped>
.goods-publish {
  min-height: calc(100vh - 84px);
  background-color: #f0f2f5;

  .el-tag {
    height: 32px;
    line-height: 32px;
  }

  .el-tag + .el-tag {
    margin-left: 8px;
  }
}
</style>
