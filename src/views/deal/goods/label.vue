<template>
  <div class="whole">
    <el-card>
      <div slot="header" class="flex align-center justify-between">
        <span>商品标签</span>
        <el-button type="primary" @click="showAddModal">
          新建
        </el-button>
      </div>
      <el-table :data="tableData1" border>
        <el-table-column prop="labelSort" label="序号" />
        <el-table-column prop="labelName" label="标签名称" />
        <el-table-column prop="labelType" label="标签类型">
          <template slot-scope="scope">
            {{ scope.row.labelType === 1 ? '主图角标' : '商品标签' }}
          </template>
        </el-table-column>
        <el-table-column prop="labelVisible" label="标签状态">
          <template slot-scope="scope">
            {{ scope.row.labelVisible ? '启用' : '禁用' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <el-button @click="editLabel(scope.row)" type="text">编辑</el-button>
            <el-button @click="changeVisible(scope.row)" type="text">
              {{ scope.row.labelVisible ? '禁用' : '启用' }}
            </el-button>
            <el-button @click="confirmDelete(scope.row)" type="text">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-dialog :title="rowId ? '编辑标签' : '新建标签'" :visible.sync="addVisible" @close="onCancel">
      <el-form :model="form" ref="form" :rules="rules" label-width="120px">

        <el-form-item label="标签类型" prop="labelType">
          <el-select v-model="form.labelType" placeholder="请选择" style="width: 100%" @change="changeLabelType" >
            <el-option label="主图角标" :value="1" />
            <el-option label="商品标签" :value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label="标签名称" prop="labelName">
          <el-input v-model="form.labelName" placeholder="请输入" :maxlength="form.labelType == 1 ? 4: 12" />
        </el-form-item>

        <el-form-item label="标签样式" prop="labelStyle" v-if="form.labelType === 1">
          <div :class="['tag-style-list']">
            <div :class="['tag-item',
            {
              'tag-style-list-active': form.labelStyle === 1
            }
            ]" @click="selectTag(1)">
              <div class="tag">
                <img width="20px" src="@/assets/images/label-one.png" alt="" />
                {{form.labelName || '标签名称'}}
              </div>
              <img class="select-icon" style="width: 16px" src="@/assets/images/label-select.png" v-if="form.labelStyle == 1" alt=""/>
              <img class="select-icon" style="width: 16px" src="@/assets/images/label-no-select.png" v-else alt=""  />
            </div>
            <div :class="['tag-item',
            {
              'tag-style-list-active': form.labelStyle === 2
            }
            ]" @click="selectTag(2)">
              <div class="tag">
                <img width="20px" src="@/assets/images/label-two.png" alt="" />
                {{form.labelName || '标签名称'}}
              </div>
              <img class="select-icon" style="width: 16px" src="@/assets/images/label-select.png" v-if="form.labelStyle == 2" alt=""/>
              <img class="select-icon" style="width: 16px" src="@/assets/images/label-no-select.png" v-else alt=""  />
            </div>
          </div>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addGoodsLabel,
  deleteGoodsLabel,
  loadGoodsLabelList,
  modifyGoodsLabel,
  modifyGoodsLabelVisible,
} from '@/api/GoodsLabelController';

export default {
  data() {
    return {
      form: {
        labelName: '',
        labelType: undefined,
        labelStyle: undefined
      },
      rules: {
        labelName: [{ required: true, message: '请输入标签名称!', trigger: 'blur' }],
        labelType: [{ required: true, message: '请选择标签类型!', trigger: 'change' }],
        labelStyle: [{ required: true, message: '请选择标签样式!', trigger: 'change' }],
      },
      tableData1: [],
      addVisible: false,
      rowId: null,
    };
  },
  created() {
    this.getTableData();
  },
  methods: {
    changeLabelType() {
      this.$set(this.form, 'labelName', undefined);
    },
    selectTag(tag){
      this.$set(this.form, 'labelStyle', tag);
      this.$refs.form.validateField('labelStyle');
    },
    goTo(path) {
      this.$router.push(path);
    },
    getTableData() {
      loadGoodsLabelList().then((res) => {
        if (res.code === 200) {
          this.tableData1 = res.data;
        }
      });
    },
    showAddModal() {
      this.rowId = null;
      this.form.labelName = '';
      this.form.labelType = undefined;
      this.form.labelStyle = undefined;
      this.addVisible = true;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    onCancel() {
      this.addVisible = false;
      this.rowId = null;
    },
    editLabel(row) {
      this.rowId = row.id;
      this.form.labelName = row.labelName;
      this.form.labelType = row.labelType;
      this.form.labelStyle = row.labelStyle;
      this.addVisible = true;
    },
    changeVisible(row) {
      const newState = row.labelVisible ? 0 : 1;
      modifyGoodsLabelVisible({ goodsLabelId: row.id, labelVisible: !!newState }).then((res) => {
        if (res.code === 200) {
          this.$message.success('操作成功！');
          this.getTableData();
        } else {
          this.$message.error('操作失败！');
        }
      });
    },
    confirmDelete(row) {
      this.$confirm('确认删除?', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning',
      }).then(() => {
        deleteGoodsLabel(row.id).then((res) => {
          if (res.code === 200) {
            this.$message.success('删除成功！');
            this.getTableData();
          } else {
            this.$message.error('删除失败！');
          }
        });
      });
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.rowId) {
            this.editGoodsLabel();
          } else {
            this.handleAddGoodsLabel();
          }
        } else {
          console.log('error submit!');
          return false;
        }
      });
    },
    handleAddGoodsLabel() {
      addGoodsLabel(this.form).then((res) => {
        if (res.code === 200) {
          this.$message.success('操作成功！');
          this.addVisible = false;
          this.getTableData();
        } else {
          this.$message.error('操作失败！');
        }
      });
    },
    editGoodsLabel() {
      modifyGoodsLabel({ goodsLabelId: this.rowId, ...this.form }).then((res) => {
        if (res.code === 200) {
          this.$message.success('操作成功！');
          this.addVisible = false;
          this.getTableData();
        } else {
          this.$message.error('操作失败！');
        }
      });
    },
  },
  components: {
  },
};
</script>

<style scoped lang="less">
.tag-style-list-active {
  background: rgba(69,143,247,0.1) !important;
  border-color: #458FF7!important;
}
.tag-style-list {
  display: flex;
  .tag-item {
    flex: 1;
    padding: 20px;
    border: 1px solid #D9D9D9;
    display: inline-block;
    position: relative;
    .select-icon {
      position: absolute;
      top: 5px;
      right: 5px;
    }
    //display: flex;
    .tag {
      background: linear-gradient( 90deg, #4562AE 0%, #2B4077 100%);
      border-radius: 4px;
      font-weight: 500;
      font-size: 14px;
      color: #E7D8C5;
      line-height: 22px;
      padding: 6px 12px 6px 8px;
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
  .tag-item:nth-child(2) {
    .tag {
      background: linear-gradient( 90deg, #FFE4D6 0%, #FFB583 100%);
      color: #4D2C1D;
    }
  }
  .tag-item:nth-child(1) {
    background: #FFFFFF;
    margin-right: 20px;
  }
}
.dialog-footer {
  text-align: right;
}
::v-deep .el-dialog {
  width: 576px;
}
</style>
