<template>
  <div class="whole">
    <el-table-search
      :search="tableProps"
      @onSearch="handleSearch"
      @onReset="handleReset"
    >
    </el-table-search>
    <el-table
      :data="tableData"
      border
      style="width: 100%"
      @expand-change="handleExpand"
    >
      <el-table-column label="序号" type="index" align="center">
        <template slot-scope="scope">
          <span>{{(pagination.current - 1) * pagination.pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="兑换时间"/>
      <el-table-column  label="会员">
        <template slot-scope="scope">
          <div class="span_all">
            <div class="span_left">
              <el-image
                v-if="scope.row.headPortrait"
                :src="scope.row.headPortrait"
                fit="fill"
              ></el-image>
              <img
                v-else
                src="@/assets/images/member.png"
                alt="donate"
                width="100%"
              />
            </div>
            <div class="span_right">
              <div>{{ scope.row.nickName }}</div>
              <div>
                {{ scope.row.accountNumber }} 【{{ scope.row.userName }}】
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="lotName" label="凭证"/>
      <el-table-column prop="lotTypeValue" label="资产类型"/>
      <el-table-column prop="num" label="兑换数量"/>
      <el-table-column  label="状态" >
        <template slot-scope="scope">
          {{scope.row.statusValue}}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="pagination.total > 0"
      :total="pagination.total"
      :page.sync="pagination.current"
      :limit.sync="pagination.pageSize"
      @pagination="handlePagination"
    />
  </div>
</template>

<script>
import {
  // getGoodsSpuPage,
  queryPickUpVouchersAll,
} from '@/api/GoodsLabelController';

export default {
  name: 'RedemptionHistory',
  components: {
  },
  data() {
    return {
      tableData: [],
      rowId: null,
      addedFlagEnum: {
        0: '下架',
        1: '上架',
      },
      formModel: {
        keyword: undefined,
        goodsNo: undefined,
        addedFlag: undefined,
      },
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  computed: {
    tableProps() {
      return {
        model: this.formModel,
        columns: [
          {
            title: '会员',
            dataIndex: 'keyword',
          },
          {
            title: '凭证ID',
            dataIndex: 'lotCode',
          },
          {
            title: '资产类型',
            dataIndex: 'lotType',
            valueEnum: {
              1: {text: '固定凭证'},
              3: {text: '收购凭证'},
              4: {text: '可用凭证'}
            },
          },
          {
            title: '状态',
            dataIndex: 'status',
            valueEnum: {
              0: {text: '未用'},
              1: {text: '已用'},
            },
          }

        ],
      }
    }
  },
  methods: {
    async fetchTableData(data) {
      const {current, pageSize} = this.pagination
      const params = {current, pageSize, ...data};
      const res = await queryPickUpVouchersAll(params);
      this.tableData = res.records;
      this.pagination.total = res.total;
    },
    handleExpand(row, expanded) {
    },
    handleSearch(params) {
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.fetchTableData(params);
    },
    handleReset() {
      this.formModel = this.$options.data().formModel
      this.fetchTableData();
    },
    handlePagination(pagination) {
      this.pagination.current = pagination.page;
      this.pagination.pageSize = pagination.limit;
      this.fetchTableData(this.formModel);
    },
  },
  activated() {
    if(this.$route.query.form == 'editPage'){
      this.fetchTableData();
    }
  },
  mounted() {
    this.fetchTableData();
  },
};
</script>

<style scoped lang="less">
</style>
