<template>
  <div>
    <el-dialog
      title="关联凭证"
      :visible.sync="dialogVisible"
      width="720"
      :close-on-click-modal="false"
      :before-close="cleanForm"
      :modal-append-to-body="false"
      :modal="false"
      destroy-on-close
    >
      <div class="whole">
        <el-form :model="form" ref="form"  @submit.native.prevent>
          <el-row>
            <el-col :span="12">
              <el-form-item label="凭证代码" label-width="68px" prop="lotCode">
                <el-input @change="lotCodeChange" v-model="form.lotCode" size="small"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="" label-width="43px">
                <el-button @click="reset">重置</el-button>
                <el-button type="primary" @click="getData">查询</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div>
          <el-table :data="tableData" style="width: 100%;" border size="small">
            <el-table-column prop="sorted" label="排序" min-width="100">
            </el-table-column>
            <el-table-column prop="date" label="凭证" min-width="200">
              <template slot-scope="scope">
                <div class="span_all">
                  <div class="span_left">
                    <el-image :src="scope.row.lotImg" fit="fill"></el-image>
                  </div>
                  <div class="span_right">
                    <div>{{ scope.row.lotName }}</div>
                    <div>【{{ scope.row.lotCode }}】</div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="lotPrice" label="价格" min-width="100">
            </el-table-column>
            <el-table-column prop="id" label="价格范围" min-width="100">
              <template slot-scope="scope">
                {{ scope.row.lotPrice1 }}-{{ scope.row.lotPrice2 }}
              </template>
            </el-table-column>
            <el-table-column prop="lotCount" label="发行总量" min-width="100">
            </el-table-column>
            <el-table-column prop="roundTime" label="轮次" min-width="100">
              <template slot-scope="scope">
                {{ scope.row.times }} - {{ scope.row.roundTime }}
              </template>
            </el-table-column>
            <el-table-column prop="publishTime" label="发布时间" min-width="180">
            </el-table-column>
            <el-table-column prop="lotState" label="状态" min-width="100">
              <template slot-scope="scope">
                {{ statusMap[scope.row.lotState] }}
              </template>
            </el-table-column>
            <el-table-column prop="exclusionTime" label="除权日期" min-width="180">
            </el-table-column>
            <el-table-column prop="uploadingDay" label="转存期" min-width="100">
            </el-table-column>
            <el-table-column prop="saleTime" label="起售日期" min-width="180">
            </el-table-column>
            <el-table-column label="操作" min-width="150" fixed="right">
              <template slot-scope="{ row }">
                <el-button type="text"
                           @click="handleRelate(row)"
                >
                  关联凭证
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="form.current"
            :limit.sync="form.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </el-dialog>
    <el-dialog
      title="凭证数量"
      :visible.sync="numberVisible"
      :before-close="numberCleanForm"
      width="30%"
    >
      <el-row style="margin-top: 30px">
        <el-col :span="4">
          <div style="height: 36px;line-height: 36px">凭证数量：</div>
        </el-col>
        <el-col :span="8">
          <el-input-number v-model="lotNum" :min="1"></el-input-number>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
    <el-button @click="numberCleanForm">取 消</el-button>
    <el-button type="primary" @click="save">确 定</el-button>
  </span>
    </el-dialog>
  </div>
</template>

<script>
import {list, relateLot} from "@/api/transferAssets/lotList.js";

export default {
  name: "RelatedLots",
  data() {
    return {
      dialogVisible: false,
      numberVisible: false,
      goodsId: '',
      lotId: '',
      lotNum: 1,
      form: {
        current: 1,
        pageSize: 10,
        lotCode: "",
      },
      total: 0,
      resetForm: {},
      tableData: [],
      statusMap: {
        0: '隐藏',
        1: '开盘',
        2: '停盘',
      },
      projectId: undefined
    };
  },
  mounted() {

  },
  methods: {
    numberCleanForm(){
      this.lotNum = 1
      this.numberVisible = false
    },
    init(){
      this.resetForm = {
        ...this.form,
      };
      this.getData();
    },
    lotCodeChange(e,){
    },
    cleanForm() {
      this.form = {
        ...this.resetForm,
      };
      this.dialogVisible = false;
    },
    // 获取数据
    getData() {
      this.form.current = 1;
      this.form.pageSize = 10;
      const params = {
        ...this.form,
        current: 1,
        pageSize: 10,
        projectId: this.projectId || -1
      };
      list(params).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
        this.form.current = 1;
      });
    },

    // 分页接口
    getList() {
      const params = {
        ...this.form,
      };
      list(params).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
      });
    },

    // 关联
    handleRelate(row) {
      this.numberVisible = true
      this.lotId = row.lotId
      this.lotNum = 1
    },

    save() {
      if (!this.lotNum) {
        this.$message.warning('请填写凭证数量！');
        return
      }
      const params = {
        goodsId: this.goodsId,
        lotId: this.lotId,
        lotNum: this.lotNum
      }
      relateLot(params).then((res) => {
        if (res.code == 200) {
          this.$message.success('关联成功！');
          this.numberVisible = false
          this.dialogVisible = false
          this.$emit("getData");
        }
      });
    },

    // 重置
    reset() {
      this.form = {
        ...this.resetForm,
      };
      this.getList();
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep .el-dialog__body {
  padding-top: 0;
}
</style>
