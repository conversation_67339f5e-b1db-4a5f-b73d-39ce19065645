<template>
  <div>
    <el-drawer
      title="交易设置"
      :visible.sync="dialogVisible"
      :before-close="closeDialog"
      size="80%"
      destroy-on-close
    >
      <div class="demo-drawer__content">
        <el-form :model="form" ref="form" :rules="rules">
        <el-row :gutter="24">
          <el-col :span="2">
            <div class="privateLabel">启用交易所</div>
          </el-col>
          <el-col :span="6">
            <el-form-item label="" label-width="0" prop="isTrade" style="margin-bottom:0">
              <el-switch
                v-model="form.isTrade"
                active-color="#13ce66"
                inactive-color="#ff4949"
                :active-value="1"
                :inactive-value="0"
                @change="isTradeChange"
              >
              </el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8" v-if="form.isTrade === 1">
            <el-form-item label="交易所" label-width="72px" prop="tradeId">
              <el-select
                v-model="form.tradeId"
                placeholder="请选择交易所"
                style="width: 100%;"
                filterable
                clearable
              >
                <el-option
                  v-for="item in memberList"
                  :key="item.userId"
                  :label="item.userId"
                  :value="item.userId"
                >
                  <span> [{{ item.userId }}]{{ item.nickName }} </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="privateLabel"><span class="require">*</span>交易手续费（买家）%</div>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item
              label="平台"
              label-width="62px"
              prop="platformBuyerFee"
              style="margin-bottom: 0"
            >
              <el-input-number
                v-model="form.platformBuyerFee"
                :min="0"
                :max="100"
                placeholder="请输入"
              ></el-input-number>
              <div>平台收取交易手续费（买家）手续费%</div>
            </el-form-item>
          </el-col>
          <el-col :span="8" >
            <el-form-item
              label="商家"
              label-width="122px"
              prop="upbeatBuyerFee"
              style="margin-bottom: 0"
            >
              <el-input-number
                v-model="form.upbeatBuyerFee"
                :min="0"
                :max="100"
                placeholder="请输入"
              ></el-input-number>
              <div>商家收取交易手续费（买家）手续费%</div>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.isTrade === 1">
            <el-form-item
              label="交易所"
              label-width="72px"
              prop="tradPlatformFee"
              style="margin-bottom: 0"
            >
              <el-input-number
                v-model="form.tradPlatformFee"
                :min="0"
                :max="100"
                placeholder="请输入"
              ></el-input-number>
              <div>请设置交易所从平台分得手续费比例:如20%</div>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="privateLabel"><span class="require">*</span>交易手续费（商家）%</div>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="平台" label-width="62px" prop="platformSaleFee" style="margin-bottom: 0"
            >
              <el-input-number
                v-model="form.platformSaleFee"
                placeholder="请输入"
              ></el-input-number>
              <div>平台收取交易手续费（商家）手续费%</div>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.isTrade === 1">
            <el-form-item label="交易所" label-width="72px" prop="tradeSaleFee" style="margin-bottom: 0"
            >
              <el-input-number
                v-model="form.tradeSaleFee"
                :min="0"
                :max="100"
                placeholder="请输入"
              ></el-input-number>
              <div>请设置交易所从平台分得手续费比例:如20%</div>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="privateLabel"><span class="require">*</span>平台技术服务费(商家)%</div>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item
              label="平台"
              label-width="62px"
              prop="platformUpbeatSaleFee"
            >
              <el-input-number
                v-model="form.platformUpbeatSaleFee"
                :min="0"
                :max="100"
                placeholder="请输入"
              ></el-input-number>
              <div>平台收取平台技术服务费（商家）%</div>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.isTrade === 1">
            <el-form-item
              label="交易所"
              label-width="72px"
              prop="tradeUpbeatFee"
            >
              <el-input-number
                v-model="form.tradeUpbeatFee"
                :min="0"
                :max="100"
                placeholder="请输入"
              ></el-input-number>
              <div>请设置交易所从平台分得管理费比例:如20%</div>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="privateLabel"><span class="require">*</span>推荐服务费（会员）%</div>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="平台" label-width="62px" prop="userSaleFee" style="margin-bottom: 0">
              <el-input-number
                v-model="form.userSaleFee"
                placeholder="请输入"
                :min="0"
                :max="100"
              ></el-input-number>
              <div>平台收取推荐服务费（会员）%</div>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.isTrade === 1">
            <el-form-item
              label="交易所"
              label-width="72px"
              prop="tradeUserSaleFee"
              style="margin-bottom: 0"
            >
              <el-input-number
                v-model="form.tradeUserSaleFee"
                placeholder="请输入"
                :min="0"
                :max="100"
              ></el-input-number>
              <div>请设置交易所从平台分得管理费比例:如20%</div>
            </el-form-item>
          </el-col>
        </el-row>

        <template>
          <div class="privateLabel"><span class="require">*</span>商家服务费(会员)%</div>
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="商家" label-width="62px" prop="upbeatUserFee" style="margin-bottom: 0">
                <el-input-number
                  v-model="form.upbeatUserFee"
                  placeholder="请输入"
                  :min="0"
                  :max="100"
                ></el-input-number>
                <div>商家收取服务费（会员）%</div>
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <div class="privateLabel"><span class="require">*</span>服务中心服务费（商家）%</div>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="服务中心" label-width="90px" prop="serviceUserFee" style="margin-bottom: 0">
              <el-input-number
                v-model="form.serviceUserFee"
                placeholder="请输入"
                :min="0"
                :max="100"
              ></el-input-number>
              <div>服务中心收取商家实物订单服务费%</div>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="privateLabel"><span class="require">*</span>合规经营保证金%</div>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item
              label=""
              label-width="0"
              prop="upbeatSaleFreeze"
            >
              <el-input-number
                v-model="form.upbeatSaleFreeze"
                :min="0"
                :max="100"
                placeholder="请输入"
              ></el-input-number>
              <div>合规经营保证金%</div>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      </div>

      <div class="demo-drawer__footer">
        <el-button @click="cancelForm">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>

import {queryList} from "@/api/basis/basis";
import {saveOrUpdateGoodsTradeConfig} from "@/api/GoodsLabelController";

export default {
  name: "tradeSetting",
  components: {
  },
  props: {
  },
  data() {
    return {
      memberList: [],
      form: {
        isTrade: 1,
        tradeId: undefined,
        platformBuyerFee: undefined,
        upbeatBuyerFee: undefined,
        tradPlatformFee: undefined,
        platformSaleFee: undefined,
        tradeSaleFee: undefined,
        platformUpbeatSaleFee: undefined,
        tradeUpbeatFee: undefined,
        userSaleFee: undefined,
        tradeUserSaleFee: undefined,
        upbeatSaleFreeze: undefined,
        upbeatUserFee: undefined,
        serviceUserFee: undefined,
        goodsId:  undefined
      },

      rules: {
        platformBuyerFee: [
          {required: true, message: " ", trigger: "change"},
        ],
        upbeatBuyerFee: [
          {required: true, message: " ", trigger: "change"},
        ],
        tradPlatformFee: [
          {required: true, message: " ", trigger: "change"},
        ],

        platformSaleFee: [
          {required: true, message: " ", trigger: "change"},
        ],
        tradeSaleFee: [
          {required: true, message: " ", trigger: "change"},
        ],

        platformUpbeatSaleFee: [
          {required: true, message: " ", trigger: "change"},
        ],
        tradeUpbeatFee: [
          {required: true, message: " ", trigger: "change"},
        ],
        userSaleFee: [{required: true, message: " ", trigger: "change"}],

        upbeatUserFee: [{required: true, message: " ", trigger: "change"}], // 商家服务费(会员)%
        upbeatSaleFreeze: [
          {required: true, message: " ", trigger: "change"},
        ],
        serviceUserFee: [
          {required: true, message: " ", trigger: "change"},
        ],
        tradeUserSaleFee: [
          {required: true, message: " ", trigger: "change"},
        ],
      },

      dialogVisible: false
    };
  },

  methods: {
    //取消
    cancelForm(){
      this.dialogVisible = false
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          saveOrUpdateGoodsTradeConfig(this.form).then((res) => {
            if (res.code === 200) {
              this.$message({
                message: "保存成功",
                type: "success",
              });
              this.dialogVisible = false;
            } else {
              this.$message({
                message: res.msg,
                type: "error",
              });
            }
          });
        }
      });
    },
    getMember() {
      queryList().then((res) => {
        this.memberList = res.data;
      });
    },
    closeDialog(){
      this.dialogVisible = false
    },

    isTradeChange(val) {
      if (val === 0) {
        this.form.tradeId = "";
        this.form.tradPlatformFee = undefined;
        this.form.tradeSaleFee = undefined;
        this.form.tradeUpbeatFee = undefined;
        this.form.tradeUserSaleFee = undefined;
      }else if(val === 1){
        if(!this.form.tradeId){
          this.form.tradeId = "";
        }
      }
      console.log('val...',this.form.tradeId);
    },

    // formValidate() {
    //   const validateRes = new Promise((resolve, reject) => {
    //     this.$refs["form"].validate((valid) => {
    //       if (valid) {
    //         resolve();
    //       } else {
    //         reject("third");
    //       }
    //     });
    //   });
    //   return validateRes;
    // },

    indexChoose(res) {
      Object.keys(this.form).map((key) => {
        this.form[key] = res.tradeConfigEntity[key];
      });
      this.dialogVisible = true
      this.getMember()
    },
  },
};
</script>
<style lang="less" scoped>
.titleStyle {
  margin: 0 0 10px 0;
  padding-left: 20px;
}

::v-deep .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before, .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:before {
  content: '';
}

::v-deep .el-form-item__label {
  padding-left: 12px;
  height: 36px;
  line-height: 36px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 2px 0px 0px 2px;
  border: 1px solid #D9D9D9;
  text-align: left;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  font-style: normal;
  border-right: none;
}

::v-deep .el-input-number--medium {
  width: 100%;
}

::v-deep .el-input__inner {
  border-radius: 0px 2px 2px 0px;
}

.privateLabel {
  height: 36px;
  line-height: 36px;
  background: rgba(0, 0, 0, 0);
  border-radius: 2px 0px 0px 2px;
  border: 1px solid rgba(0, 0, 0, 0);
  text-align: left;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  font-style: normal;
}

.privateTip {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  line-height: 22px;
  text-align: left;
  font-style: normal;
}

.require {
  color: #FF4D4F;
  font-size: 14px;
}
.el-form {
  padding: 0 24px 24px;
}

.demo-drawer__footer {
  text-align: center;
  margin-bottom: 24px;
}
</style>
