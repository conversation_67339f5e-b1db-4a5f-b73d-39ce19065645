/** 财务提现页面 */
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="会员ID/昵称/手机号/姓名" label-width="180px">
        <el-input v-model="queryParams.keyword" placeholder="请输入" clearable style="width: 240px;"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="发生时间">
        <el-date-picker v-model="dateRange" style="width: 240px;" value-format="yyyy-MM-dd" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" @change="changeTime"></el-date-picker>
      </el-form-item>
      <el-form-item label="汇款退回时间">
        <el-date-picker v-model="backDateRange" style="width: 240px;" value-format="yyyy-MM-dd" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" @change="changeBackTime"></el-date-picker>
      </el-form-item>
      <el-form-item label="单号">
        <el-input v-model="queryParams.ordNumber" placeholder="请输入角色单号" clearable style="width: 240px;"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="状态" clearable style="width: 240px;">
          <el-option v-for="(item, index) of typearr" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button @click="resetQuery">重置</el-button>
        <el-button type="primary" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8 flex justify-end">
      <el-col v-if="!$isProd" :span="1.5">
        <el-button
          type="primary"
          @click="handleBatchTransfer"
          :disabled="selectedList.length === 0">
          批量打款
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button @click="handleExport">导出</el-button>
      </el-col>
    </el-row>
    <div class="amount-box">
      <i class="el-icon-info"></i>
      <p>
        申请总额:
        <span> {{ grossamountobj.countAmount }}</span>
      </p>
      <!-- <p style="padding: 1px;  margin-left: 8px; ">总额: <span>232522.00</span></p> -->
      <p>
        总额(不含驳回):
        <span>
          {{ grossamountobj.countAmountRejectExclusive }}</span>
      </p>
      <p>
        打款中:
        <span> {{ grossamountobj.countPaying || 0 }}</span>
      </p>
      <p>
        已打款:
        <span> {{ grossamountobj.countPayment }}</span>
      </p>
      <p>
        待审核:
        <span> {{ grossamountobj.countAudit }}</span>
      </p>
      <p>
        待打款:
        <span> {{ grossamountobj.countUnpaid }}</span>
      </p>
      <p>
        已驳回:
        <span> {{ grossamountobj.countReject }}</span>
      </p>
      <p>
        汇款退回:
        <span> {{ grossamountobj.countBack }}</span>
      </p>
    </div>

    <!-- <el-table v-loading="loading" :data="roleList" @selection-change="handleSelectionChange"> -->
    <el-table
      ref="table"
      v-loading="loading"
      :data="roleList"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column label="单号" prop="ordNumber" :show-overflow-tooltip="true" width="220" />
<!--      <el-table-column label="会员" prop="memberId" width="220">-->
<!--        <template slot-scope="scope">-->
<!--          <div class="span_all">-->
<!--            <div class="span_left">-->
<!--              <img v-if="scope.row.headPortrait" :src="scope.row.headPortrait"-->
<!--                alt="" />-->
<!--              <img v-else src="@/assets/images/member.png" alt="donate" style="width: 40px; height: 40px;" />-->
<!--            </div>-->
<!--            <div class="span_right">-->
<!--              <div>{{ scope.row.memberId }}【{{ scope.row.name }}】</div>-->
<!--              <div>{{ scope.row.phone }}</div>-->
<!--            </div>-->
<!--          </div>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column prop="date" label="会员" width="280">
        <template slot-scope="scope">
          <div class="span_all">
            <div class="span_left">
              <el-image
                v-if="scope.row.headPortrait"
                :src="scope.row.headPortrait"
                fit="fill"
              ></el-image>
              <img
                v-else
                src="@/assets/images/member.png"
                alt="donate"
                width="100%"
              />
            </div>
            <div class="span_right">
              <div>{{ scope.row.nickname }} <span>({{ scope.row.memberId}})</span></div>
              <div>
                {{ scope.row.phone }} 【{{ scope.row.name }}】
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="金额" prop="ordNumber" class-name="small-padding fixed-width"
        width="200">
        <template slot-scope="scope">
          <div>
            <p style="margin: 0; padding: 0;">
              申请金额 <span> {{ scope.row.cashWithdrawalAmount }}</span>
            </p>
            <p style="margin: 0; padding: 0;">
              -手续费 <span> {{ scope.row.withdrawalCommission || 0 }}</span>
            </p>
            <p style="margin: 0; padding: 0;">
              -提货金 <span> {{ scope.row.deliveryAmount || 0 }}</span>
            </p>
            <p style="margin: 0; padding: 0;">
              实际到账 <span> {{ scope.row.actualArrivalCash }}</span>
            </p>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="时间" prop="id" :show-overflow-tooltip="true" width="240"
        class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <div>
            <p style="margin: 0; padding: 0;">
              申请时间 <span> {{ scope.row.applyTime }}</span>
            </p>
            <p style="margin: 0; padding: 0;">
              审核时间 <span> {{ scope.row.auditTime || '-' }}</span>
            </p>
            <p style="margin: 0; padding: 0;">
              打款时间 <span> {{ scope.row.remitTime || '-' }}</span>
            </p>
            <p style="margin: 0; padding: 0;">
              拒绝时间 <span> {{ scope.row.rejectTime || '-' }}</span>
            </p>
            <p style="margin: 0; padding: 0;">
              汇款退回时间 <span> {{ scope.row.backTime || '-' }}</span>
            </p>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="提现账户" prop="actualArrivalCash" width="280"
        class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <div>
            <p style="margin: 0; padding: 0;">
              提现方式 <span> {{ ['银行卡', '微信', '支付宝'][scope.row.withdrawalMethod - 1] || '-' }}</span>
              <span>{{scope.row.type == '1' ? '(个人账户)':scope.row.type == '2'?'(对公账户)':''}}</span>
            </p>
            <p style="margin: 0; padding: 0;">
              银行卡号 <span>{{ scope.row.account }}</span>
            </p>
            <p style="margin: 0; padding: 0;">
              联行号 <span>{{ scope.row.bankNo || '-' }}</span>
            </p>
            <p style="margin: 0; padding: 0;">
              账户名 <span>{{ scope.row.accountName || '-' }}</span>
            </p>
            <p style="margin: 0; padding: 0;">
              开户行 <span>{{ scope.row.bank }}{{ scope.row.bankBranch }}</span>
            </p>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" class-name="small-padding fixed-width" min-width="120">
        <template slot-scope="scope">
          <span>
            {{
              scope.row.status === 1
                ? "待审核"
                : scope.row.status === 2
                  ? "待打款"
                  : scope.row.status === 3
                    ? ["打款失败", "已打款", "打款中"][scope.row.paymentStatus]
                    : scope.row.status === 4 ? "已驳回"
                      : "处理中"
            }}
          </span>
          <span v-if="scope.row.status === 3 && scope.row.paymentStatus === 1 && scope.row.receiptNo">（{{ scope.row.receiptNo }}）</span>
          <span v-if="scope.row.status === 4 && scope.row.rejectReason">（{{ scope.row.rejectReason }}）</span>
          <span v-if="(scope.row.status === 3 && scope.row.paymentStatus !== 1) && scope.row.repPayment">（{{ scope.row.repPayment }}）</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="100" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
          <el-popconfirm v-if="scope.row.status === 1" class="mr-16" @confirm="auditbth(scope.row)" confirm-button-text="确定"
            cancel-button-text="取消" icon="el-icon-info" icon-color="red" title="审核通过？">
            <el-button type="text" slot="reference">审核</el-button>
          </el-popconfirm>

          <el-button v-if="scope.row.status === 1 || (scope.row.status === 3 && scope.row.paymentStatus === 0)" type="text" @click="rejectbtn(scope.row)">驳回
          </el-button>

          <el-button v-if="scope.row.status === 2" type="text"
            @click="remittancebtn(scope.row)">打款</el-button>

<!--          <p v-if="scope.row.status == 4" style="margin: 0; padding: 0;" type="text" @click="turndown(scope.row)">
            驳回原因
          </p>-->
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import {
  queryAllport,
  withdrawdepositport,
  withdrawrejectport,
  withdrawtransferportV2,
  withdrawUpdatePaymentErrorMsg,
  withdrawDepositqueryTotal,
  withdrawtransferBatchport,
} from "@/api/message/message.js";
// queryAllaccountport
export default {
  name: "Payouts",
  data() {
    return {
      selectedList: [], // 选中的列表
      grossamountobj: {},
      typearr: [
        { value: 1, label: "待审核" },
        { value: 2, label: "待打款" },
        { value: 3, label: "已打款" },
        { value: 6, label: "打款失败" },
        { value: 4, label: "已驳回" },
        // paymentStatus = 2 status=3
        { value: 7, label: "打款中" },
        // { value: 5, label: "处理中" },
      ],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      menuExpand: false,
      menuNodeAll: false,
      deptExpand: true,
      deptNodeAll: false,
      // 日期范围
      dateRange: [],
      backDateRange: [],
      // 数据范围选项
      dataScopeOptions: [
        {
          value: "1",
          label: "全部数据权限",
        },
        {
          value: "2",
          label: "自定数据权限",
        },
        {
          value: "3",
          label: "本部门数据权限",
        },
        {
          value: "4",
          label: "本部门及以下数据权限",
        },
        {
          value: "5",
          label: "仅本人数据权限",
        },
      ],
      // 菜单列表
      menuOptions: [],
      // 部门列表
      deptOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: undefined,
        roleName: undefined,
        roleKey: undefined,
        status: undefined,
        startTime: undefined,
        endTime: undefined,
        backStartTime: undefined,
        backEndTime: undefined,
        paymentStatus: undefined
      },
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label",
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    turndown(value) {
      this.$alert(value.rejectReason, "驳回原因", {
        confirmButtonText: "确定",
        callback: (action) => {
          // this.$message({
          //     type: 'info',
          //     message: `action: ${action}`
          // });
        },
      });
    },

    // 改变时间
    changeTime(time) {
      this.queryParams.startTime = time ? this.$dayjs(time[0]).format(
        "YYYY-MM-DD 00:00:00"
      ) : undefined;
      this.queryParams.endTime = time ? this.$dayjs(time[1]).format(
        "YYYY-MM-DD 23:59:59"
      ) : undefined;
    },

    // 改变汇款退回时间
    changeBackTime(time) {
      this.queryParams.backStartTime = time ? this.$dayjs(time[0]).format(
        "YYYY-MM-DD 00:00:00"
      ) : undefined;
      this.queryParams.backEndTime = time ? this.$dayjs(time[1]).format(
        "YYYY-MM-DD 23:59:59"
      ) : undefined;
    },

    getList() {
      this.loading = true;
      let params = this.queryParams;
      if(this.queryParams.status === 3) {
        params = {
          ...params, paymentStatus : 1
        }
      } else if(this.queryParams.status === 6) {
        params = {
          ...params, status: 3, paymentStatus : 0
        }
      } else if(this.queryParams.status === 7) {
        params = {
          ...params, status: 3, paymentStatus : 2
        }
      }
      queryAllport(
        params
      ).then((response) => {
        this.roleList = response.data.records;
        this.total = response.data.total;
      }).finally(() => {
        this.loading = false;
      });

      withdrawDepositqueryTotal(params).then((res) => {
        console.log(res);
        this.grossamountobj = res.data;
      });
    },

    /**  auditbth  审核按钮  auditbth   */

    auditbth(value) {
      let id = value.id;
      console.log(id);
      this.loading = true
      withdrawdepositport(id).then((res) => {
        this.$message({
          message: "审核成功",
          type: "success",
        });
      }).finally(() => {
        this.getList();
      });
    },
    /**   rejectbtn 驳回按钮 */
    rejectbtn(row) {
      console.log("驳回按钮");
      this.$prompt("驳回理由", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputType: "textarea",
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            const value = instance.inputValue;

            if (!value) {
              this.$message.error('请输入驳回理由');
              return;
            }

            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '提交中...';

            (row.status === 1 ?
              withdrawrejectport({withdrawId: row.id, rejectReason: value}) :
              withdrawUpdatePaymentErrorMsg({withdrawId: row.id, paymentErrorMsg: value})
            ).then(
              (res) => {
                console.log(res);
                instance.confirmButtonLoading = false;
                instance.confirmButtonText = '确定';
                this.$message({
                  type: "success",
                  message: "提交成功",
                });
                done();
                this.getList();
              }
            ).catch(() => {
              instance.confirmButtonLoading = false;
              instance.confirmButtonText = '确定';
            });
          } else {
            done();
          }
        }
      }).catch(() => {
        this.$message({
          type: "info",
          message: "已取消驳回",
        });
      });
    },

    /** 打款按钮 */
    async remittancebtn(row) {
      const payTypeRes = await this.getDicts("pay_type");
      console.log('payTypeRes', payTypeRes)
      const payTypeArr = payTypeRes.data
      const payType = ((payTypeArr || []).find(v => v.dictLabel === "支付渠道") || {}).dictValue;
      const isZh = payType === '0'
      if (!['0', '1'].includes(payType)) {
        this.$message({
          type: "error",
          message: "暂不支持该支付渠道",
        });
        return;
      }
      (isZh ? this.$prompt("确认打款吗？确认请输入回执编号。", "提示", {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputType: "input",
        inputValidator: (value) => {
          if (!value) {
            return "请输入回执编号"
          }
          if (value.indexOf(" ") >= 0) {
            return "回执编号不能有空格"
          }
          if (!/^.{5,50}$/.test(value)) {
            return "回执编号长度必须介于 5 和 50 之间"
          }
          return true;
        },
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '提交中...';

            withdrawtransferportV2(row.id, instance.inputValue || '').then(() => {
              instance.confirmButtonLoading = false;
              instance.confirmButtonText = '确定';
              this.$message({
                type: "success",
                message: "打款执行中，请稍后查看执行结果...",
              });
              done();
              this.getList();
            }).catch(() => {
              instance.confirmButtonLoading = false;
              instance.confirmButtonText = '确定';
            });
          } else {
            done();
          }
        }
      }) :
      this.$confirm("确认打款吗, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '提交中...';

            withdrawtransferportV2(row.id, '').then(() => {
              instance.confirmButtonLoading = false;
              instance.confirmButtonText = '确定';
              this.$message({
                type: "success",
                message: "打款执行中，请稍后查看执行结果...",
              });
              done();
              this.getList();
            }).catch(() => {
              instance.confirmButtonLoading = false;
              instance.confirmButtonText = '确定';
            });
          } else {
            done();
          }
        }
      }))
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消打款",
          });
        });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 10;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.backDateRange = [];
      this.queryParams = this.$options.data().queryParams;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      let params = this.queryParams;
      if(this.queryParams.status === 3) {
        params = {
          ...params, paymentStatus : 1
        }
      } else if(this.queryParams.status === 6) {
        params = {
          ...params, status: 3, paymentStatus : 0
        }
      } else if(this.queryParams.status === 7) {
        params = {
          ...params, status: 3, paymentStatus : 2
        }
      }
      this.download(
        "/withdrawDeposit/exportWithdraw",
        {
          ...params
        },
        `提现管理.xlsx`
      );
    },

    /** 表格多选 */
    handleSelectionChange(selection) {
      // 只允许选择：1.待打款状态 2.个人账户 3.他行且金额超过5万的不允许
      this.selectedList = selection.filter(item => {
        const isValidStatus = item.status === 2;
        const isPersonalAccount = item.type === 1;
        const isValidAmount = !(item.othBankFlag === 1 && item.actualArrivalCash > 50000);

        if (!isValidStatus || !isPersonalAccount || !isValidAmount) {
          let reason = '';
          if (!isValidStatus) reason = '只能选择待打款状态的记录';
          else if (!isPersonalAccount) reason = '只能选择个人账户记录';
          else if (!isValidAmount) reason = '他行打款金额不能超过5万';

          this.$message.error(`订单号[${item.ordNumber}]: ${reason}`);
          // 取消当前行的选中状态
          this.$nextTick(() => {
            this.$refs.table.toggleRowSelection(item, false);
          });
          return false;
        }
        return true;
      });
    },

    /** 批量打款 */
    async handleBatchTransfer() {
      if (this.selectedList.length === 0) {
        this.$message.warning('请选择待打款的记录');
        return;
      }

      this.$confirm(`确认对选中的 ${this.selectedList.length} 条记录执行批量打款操作吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then((resObj) => {
          this.loading = true;
          const ids = this.selectedList.map(item => item.id);
          withdrawtransferBatchport(ids).then(() => {
            this.$message({
              type: "success",
              message: "批量打款执行中，请稍后查看执行结果...",
            });
            this.selectedList = [];
          }).finally(() => {
            this.getList();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消批量打款",
          });
        });
    },
  },
};
</script>
