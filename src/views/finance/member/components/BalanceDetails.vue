/** 余额明细*/
<template>
  <div class="whole">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="会员ID/昵称/手机号/姓名" label-width="180px">
        <el-input v-model="queryParams.keyword" placeholder="请输入" clearable style="width: 240px;"
                  @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="发生时间">
        <el-date-picker v-model="dateRange" style="width: 240px;" value-format="yyyy-MM-dd" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="单号">
        <el-input v-model="queryParams.ordNumber" placeholder="请输入单号" clearable style="width: 240px;"
                  @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="类型">
        <el-select v-model="queryParams.type" placeholder="类型" clearable style="width: 240px;">
          <el-option v-for="(item, index) of typearr" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="loading" @click="handleQuery">查询</el-button>
         <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="action-flex-end">
      <el-col :span="1.5">
        <el-button @click="handleExport">导出
        </el-button>
      </el-col>
    </el-row>
    <div class="amount-box">
      <i class="el-icon-info"></i>
      <p>
        总额: <span>{{ grossamount }}</span>
      </p>
    </div>
    <el-table v-loading="loading" :data="roleList" @selection-change="handleSelectionChange">
      <el-table-column label="会员ID" prop="memberId" :show-overflow-tooltip="true" width="100"/>
      <el-table-column label="单号" prop="ordNumber" :show-overflow-tooltip="true" width="220"/>
      <el-table-column label="会员" prop="roleSort" width="260">
        <template slot-scope="scope">
          <div class="span_all">
            <div class="span_left">
              <img v-if="scope.row.headPortrait" :src="scope.row.headPortrait" alt=""/>
              <img v-else src="@/assets/images/member.png" alt="donate"/>
            </div>
            <div class="span_right">
              <div>{{ scope.row.nickName }}</div>
              <div>{{ scope.row.phone }}【{{ scope.row.name }}】</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="变动金额" prop="alterationAmount" class-name="small-padding fixed-width text-green">
        <!--<template slot-scope="scope">-->
        <!--<div v-if="scope.row.beforeAlterationAmount - scope.row.afterAlterationAmount > 0">-->
        <!-- -{{ scope.row.alterationAmount }}-->
        <!--</div>-->
        <!--<div v-else>{{ scope.row.alterationAmount }}</div>-->
        <!--</template>-->
      </el-table-column>
      <el-table-column label="变动前金额" prop="beforeAlterationAmount" class-name="small-padding fixed-width text-red"/>
      <el-table-column label="变动后金额" prop="afterAlterationAmount" class-name="small-padding fixed-width text-main"/>
      <el-table-column label="类型" prop="typeDesc" class-name="small-padding fixed-width text-black-85">
      </el-table-column>

      <el-table-column label="变动时间" prop="alterationTime" width="170">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.alterationTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="说明" prop="remarks" class-name="small-padding fixed-width"/>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>
  </div>
</template>

<script>
  import {
    memberaccount,
    queryBalanceDetailTotal,
  } from "@/api/message/message.js";

  export default {
    name: "BalanceDetails",
    dicts: ["sys_normal_disable"],
    props: {
      tabName: {
        type: String,
        default: 'first'
      }
    },
    data() {
      return {
        grossamount: "", // 总金额
        typearr: [],
        // 遮罩层
        loading: false,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 角色表格数据
        roleList: [],
        // 弹出层标题
        title: "",
        // 日期范围
        dateRange: [],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          keyword: undefined,
          startTime: undefined,
          endTime: undefined,
          classify: undefined,
        },
        // 表单参数
        form: {},
        defaultProps: {
          children: "children",
          label: "label",
        },
        exportUrl: "/memberAccount/exportBalanceDetail",
        typeValue: "",
      };
    },
    created() {
      // 设置默认近一周的日期范围，但首屏不触发接口调用
      this.setDefaultDateRange();
      this.open(this.tabName);
    },
    methods: {
      /** 设置默认近一周时间范围 */
      setDefaultDateRange() {
        const end = new Date();
        const start = new Date();
        start.setDate(end.getDate() - 7);
        const format = (d) => {
          const y = d.getFullYear();
          const m = (`0${d.getMonth() + 1}`).slice(-2);
          const day = (`0${d.getDate()}`).slice(-2);
          return `${y}-${m}-${day}`;
        };
        this.dateRange = [format(start), format(end)];
      },
      open(value) {
        this.typeValue = value;
        if(value === 'first') {
          this.typearr =[
            {value: 1, label: "线上充值"},
            {value: 2, label: "线下充值"},
            {value: 3, label: "提货释放"},
            {value: 4, label: "买入支出"},
            {value: 5, label: "卖出收入"},
            // {value: 6, label: "手续费"},
            {value: 7, label: "提现申请"},
            {value: 8, label: "配货扣除"},
            {value: 9, label: "后台充值"},
            {value: 10, label: "交易手续费（买家）"},
            {value: 11, label: "交易手续费（卖家）"},
            {value: 12, label: "推荐服务费（会员）"},
            {value: 13, label: "委托服务费（会员）"},
            {value: 14, label: "预约销售推荐服务费（会员）"},
            {value: 15, label: "批发推荐服务费（服务中心）"},
            {value: 16, label: "可销售推荐服务费（服务中心）"},
            {value: 17, label: "平台技术服务费（商家）"},
            // {value: 18, label: "订单金额"},
            {value: 19, label: "提现返回"},
            {value: 20, label: "提现驳回"},
            {value: 21, label: "合规经营保证金"},

            { value: 42, label: "充值合规经营保证金" },
            { value: 43, label: "商家销售订单" },
            { value: 44, label: "提货订单" },
            { value: 45, label: "会员带货订单" },
            { value: 46, label: "服务中心带货订单" },
            { value: 41, label: "商家带货订单" },

            {value: 22, label: "提货"},
            {value: 29, label: "提货手续费"},


            {value: 23, label: "实物-交易手续费（买家）"},
            {value: 24, label: "实物-交易手续费（商家）"},
            {value: 25, label: "实物-平台技术服务费（商家）"},
            {value: 26, label: "实物-推荐服务费（会员）"},
            {value: 27, label: "实物-商家服务费（会员）"},
            {value: 28, label: "实物-服务中心服务会（商家）"},
            {value: 90, label: "商品支付"},
            {value: 91, label: "商品退费"},
          ]
          this.queryParams.classify = 1;
        } else if(value === 'second') {
          this.typearr = [
            { value: 43, label: "商家销售订单" },
            { value: 44, label: "提货订单" },
            { value: 45, label: "会员带货订单" },
            { value: 46, label: "服务中心带货订单" },
            { value: 41, label: "商家带货订单" },
            { value: 42, label: "充值合规经营保证金" },
          ]
          this.queryParams.classify = 4;
        } else if(value === 'fourth') {
          this.typearr =[
            {value: 1, label: "线上充值"},
            {value: 2, label: "线下充值"},
            {value: 3, label: "提货释放"},
            {value: 4, label: "买入支出"},
            {value: 5, label: "卖出收入"},
            // {value: 6, label: "手续费"},
            {value: 7, label: "提现申请"},
            {value: 8, label: "配货扣除"},
            {value: 9, label: "后台充值"},
            {value: 10, label: "交易手续费（买家）"},
            {value: 11, label: "交易手续费（卖家）"},
            {value: 12, label: "推荐服务费（会员）"},
            {value: 13, label: "委托服务费（会员）"},
            {value: 14, label: "预约销售推荐服务费（会员）"},
            {value: 15, label: "批发推荐服务费（服务中心）"},
            {value: 16, label: "可销售推荐服务费（服务中心）"},
            {value: 17, label: "平台技术服务费（商家）"},
            // {value: 18, label: "订单金额"},
            {value: 19, label: "提现返回"},
            {value: 20, label: "提现驳回"},
            {value: 21, label: "合规经营保证金"},

            { value: 42, label: "充值合规经营保证金" },
            { value: 43, label: "商家销售订单" },
            { value: 44, label: "提货订单" },
            { value: 45, label: "会员带货订单" },
            { value: 46, label: "服务中心带货订单" },
            { value: 41, label: "商家带货订单" },

            {value: 22, label: "提货"},
            {value: 29, label: "提货手续费"},


            {value: 23, label: "实物-交易手续费（买家）"},
            {value: 24, label: "实物-交易手续费（商家）"},
            {value: 25, label: "实物-平台技术服务费（商家）"},
            {value: 26, label: "实物-推荐服务费（会员）"},
            {value: 27, label: "实物-商家服务费（会员）"},
            {value: 28, label: "实物-服务中心服务会（商家）"},
            {value: 90, label: "商品支付"},
            {value: 91, label: "商品退费"},
          ]
          this.queryParams.classify = 6;
        }else if(value === 'third') {
          //  7，提现申请 19，提现返回 20，提现驳回 22，提货 29， 提货手续费
          this.typearr = [
            {value: 7, label: "提现申请"},
            {value: 19, label: "提现返回"},
            {value: 20, label: "提现驳回"},
            {value: 22, label: "提货"},
            {value: 29, label: "提货手续费"},
          ]
          this.queryParams.classify = 5
        }

        // 取消 Tab 初次点击时的自动请求，由用户主动查询触发
      },
      getList() {
        this.loading = true;
        if (this.dateRange == null) {
          memberaccount(
            this.addDateRange(
              this.queryParams,
              (this.queryParams.startTime = undefined),
              (this.queryParams.endTime = undefined)
            )
          ).then((response) => {
            this.roleList = response.data.records;
            this.total = response.data.total;
            this.loading = false;
          });
          this.fetchData(queryBalanceDetailTotal);
        } else {
          memberaccount(
            this.addDateRange(
              this.queryParams,
              (this.queryParams.startTime = this.dateRange[0]),
              (this.queryParams.endTime = this.dateRange[1])
            )
          ).then((response) => {
            this.roleList = response.data.records;
            this.total = response.data.total;
            this.loading = false;
          });
          this.fetchData(queryBalanceDetailTotal);
          // queryBalanceDetailTotal(
          //   this.addDateRange(
          //     this.queryParams,
          //     (this.queryParams.startTime = this.dateRange[0]),
          //     (this.queryParams.endTime = this.dateRange[1])
          //   )
          // ).then((res) => {
          //   this.grossamount = res.data;
          // });
        }
      },

      fetchData(queryFunction) {
          let params = {}
          if(this.dateRange == null) {
            params = this.addDateRange(
              this.queryParams,
              (this.queryParams.startTime = undefined),
              (this.queryParams.endTime = undefined)
            )
          } else {
            params = this.addDateRange(
              this.queryParams,
              (this.queryParams.startTime = this.dateRange[0]),
              (this.queryParams.endTime = this.dateRange[1])
            )
          }
        queryFunction(
          this.addDateRange(
            params
          )
        ).then((res) => {
          this.grossamount = res.data;
        });
      },

      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.queryParams.pageSize = 10;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        // 重置后恢复默认近一周范围
        this.setDefaultDateRange();
        this.queryParams = this.$options.data().queryParams;
        this.resetForm("queryForm");
        if(this.typeValue === 'first') {
          this.queryParams.classify = 1;
        } else if(this.typeValue === 'second') {
          this.queryParams.classify = 4;
        } else if(this.typeValue === 'fourth') {
          this.queryParams.classify = 6;
        }
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map((item) => item.roleId);
        this.single = selection.length != 1;
        this.multiple = !selection.length;
      },
      /** 导出按钮操作 */
      handleExport() {
        const fileNameMap = {
          first: '余额明细.xlsx',
          second: '合规经营保证金明细.xlsx',
          fourth: '账户余额明细.xlsx',
          third: '提货金明细.xlsx'
        }
        let fileName = fileNameMap?.[this.typeValue]
        this.download(
          this.exportUrl,
          {
            ...this.queryParams,
          },
          fileName
        );
      },
    },
  };
</script>
