/** 余额明细*/
<template>
  <el-dialog :title="title" :visible.sync="visibleOpen" width="1024px" append-to-body @close="resetQuery">
    <div class="whole">
  <!--    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">-->
  <!--      <el-form-item label="会员ID/昵称/手机号/姓名" label-width="180px">-->
  <!--        <el-input v-model="queryParams.keyword" placeholder="请输入" clearable style="width: 240px;"-->
  <!--                  @keyup.enter.native="handleQuery"/>-->
  <!--      </el-form-item>-->
  <!--      <el-form-item label="发生时间">-->
  <!--        <el-date-picker v-model="dateRange" style="width: 240px;" value-format="yyyy-MM-dd" type="daterange"-->
  <!--                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>-->
  <!--      </el-form-item>-->
  <!--      <el-form-item label="单号">-->
  <!--        <el-input v-model="queryParams.ordNumber" placeholder="请输入单号" clearable style="width: 240px;"-->
  <!--                  @keyup.enter.native="handleQuery"/>-->
  <!--      </el-form-item>-->
  <!--      <el-form-item label="类型">-->
  <!--        <el-select v-model="queryParams.type" placeholder="类型" clearable style="width: 240px;">-->
  <!--          <el-option v-for="(item, index) of typearr" :key="item.value" :label="item.label" :value="item.value"/>-->
  <!--        </el-select>-->
  <!--      </el-form-item>-->
  <!--      <el-form-item>-->
  <!--        <el-button type="primary" @click="handleQuery">查询</el-button>-->
  <!--        <el-button @click="resetQuery">重置</el-button>-->
  <!--      </el-form-item>-->
  <!--    </el-form>-->

  <!--    <el-row :gutter="10" class="action-flex-end">-->
  <!--      <el-col :span="1.5">-->
  <!--        <el-button @click="handleExport">导出-->
  <!--        </el-button>-->
  <!--      </el-col>-->
  <!--    </el-row>-->
  <!--    <div class="amount-box">-->
  <!--      <i class="el-icon-info"></i>-->
  <!--      <p>-->
  <!--        总额: <span>{{ grossamount }}</span>-->
  <!--      </p>-->
  <!--    </div>-->
      <el-table v-loading="loading" :data="roleList" @selection-change="handleSelectionChange">
<!--        <el-table-column label="会员ID" prop="memberId" :show-overflow-tooltip="true" width="100"/>-->
<!--        <el-table-column label="单号" prop="ordNumber" :show-overflow-tooltip="true" width="220"/>-->
<!--        <el-table-column label="会员" prop="roleSort" width="260">-->
<!--          <template slot-scope="scope">-->
<!--            <div class="span_all">-->
<!--              <div class="span_left">-->
<!--                <img v-if="scope.row.headPortrait" :src="scope.row.headPortrait" alt=""/>-->
<!--                <img v-else src="@/assets/images/member.png" alt="donate"/>-->
<!--              </div>-->
<!--              <div class="span_right">-->
<!--                <div>{{ scope.row.nickName }}</div>-->
<!--                <div>{{ scope.row.phone }}【{{ scope.row.name }}】</div>-->
<!--              </div>-->
<!--            </div>-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column label="变动金额" prop="alterationAmount" class-name="small-padding fixed-width text-green">
        </el-table-column>
        <el-table-column label="变动前金额" prop="beforeAlterationAmount" class-name="small-padding fixed-width text-red"/>
        <el-table-column label="变动后金额" prop="afterAlterationAmount" class-name="small-padding fixed-width text-main"/>
        <el-table-column label="类型" prop="typeDesc" class-name="small-padding fixed-width text-black-85">
        </el-table-column>

        <el-table-column label="变动时间" prop="alterationTime" width="170">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.alterationTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="说明" prop="remarks" class-name="small-padding fixed-width"/>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                  @pagination="getList"/>
    </div>
  </el-dialog>
</template>

<script>
import {
  memberaccount,
  queryBalanceDetailTotal,
} from "@/api/message/message.js";
export default {
  name: "DetailDialog",
  data() {
    return {
      visibleOpen: false,
      grossamount: "", // 总金额
      typearr: [],
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "明细",
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: undefined,
        memberId: undefined,
        startTime: undefined,
        endTime: undefined,
        classify: undefined,
      },
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label",
      },
      exportUrl: "",
    };
  },
  created() {
    // this.getList();
  },
  methods: {
    open(type,memberId) {
      this.visibleOpen = true;
      this.queryParams.memberId = memberId;
      this.queryParams.classify = type;
      this.getList();
    },
    getList() {
      if (this.dateRange == null) {
        memberaccount(
          this.addDateRange(
            this.queryParams,
            (this.queryParams.startTime = undefined),
            (this.queryParams.endTime = undefined)
          )
        ).then((response) => {
          this.roleList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        });

        queryBalanceDetailTotal(
          this.addDateRange(
            this.queryParams,
            (this.queryParams.startTime = undefined),
            (this.queryParams.endTime = undefined)
          )
        ).then((res) => {
          this.grossamount = res.data;
        });
      } else {
        memberaccount(
          this.addDateRange(
            this.queryParams,
            (this.queryParams.startTime = this.dateRange[0]),
            (this.queryParams.endTime = this.dateRange[1])
          )
        ).then((response) => {
          this.roleList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        });

        queryBalanceDetailTotal(
          this.addDateRange(
            this.queryParams,
            (this.queryParams.startTime = this.dateRange[0]),
            (this.queryParams.endTime = this.dateRange[1])
          )
        ).then((res) => {
          this.grossamount = res.data;
        });
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 10;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams = this.$options.data().queryParams;
      this.resetForm("queryForm");

      // this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.roleId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        this.exportUrl,
        {
          ...this.queryParams,
        },
        `余额明细.xlsx`
      );
    },
  },
};
</script>
