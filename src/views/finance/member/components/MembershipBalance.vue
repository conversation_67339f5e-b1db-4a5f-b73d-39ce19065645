/** 会员余额 */

<template>
  <div class="whole">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="会员ID/昵称/手机号/姓名" label-width="180px" prop="keyword">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入"
          clearable
          style="width: 240px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="注册时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="所属项目" label-width="82px" prop="projectName">
        <el-input v-model="queryParams.projectName" style="width: 200px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="handleQuery"
          >查询</el-button
        >
         <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="action-flex-end">
      <el-col :span="1.5">
        <el-button
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="roleList">
      <el-table-column
        label="会员ID"
        prop="memberId"
        :show-overflow-tooltip="true"
        width="100"
      />
      <!-- <el-table-column label="单号" prop="roleName" :show-overflow-tooltip="true" width="150" /> -->
      <el-table-column label="会员" prop="roleSort" width="260">
        <template slot-scope="scope">
          <div class="span_all">
            <div class="span_left">
              <img
                v-if="scope.row.headPortrait"
                :src="scope.row.headPortrait"
                alt=""
              />
              <img
                v-else
                src="@/assets/images/member.png"
                alt="donate"
              />
            </div>
            <div class="span_right">
              <div>{{ scope.row.nickName }}</div>
              <div>{{ scope.row.phone }}【{{ scope.row.name }}】</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="projectName" label="所属项目" width="200">
        <template slot-scope="scope">
          {{ scope.row.projectName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        label="账户余额"
        prop="balance"
        class-name="small-padding fixed-width text-green "
      >
        <template slot-scope="scope">
          <span class="cursorPointer" @click="openDetailDialog(6, scope.row.memberId)">{{
            scope.row.balance
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="可用余额"
        prop="availableCredit"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <span class="text-green cursorPointer" @click="openDetailDialog(1, scope.row.memberId)">{{
            scope.row.availableCredit
          }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="冻结金额"
        prop="freezeAmount"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <span class="text-red cursorPointer" @click="openDetailDialog(2, scope.row.memberId)">{{
            scope.row.freezeAmount
          }}</span>
          <!-- <span style="color: greenyellow;">{{ parseTime(scope.row.roleSort) }}</span> -->
        </template>
      </el-table-column>
      <el-table-column
        label="未入账金额"
        prop="receiveAmount"
        class-name="small-padding fixed-width text-main"
      >
        <template slot-scope="scope">
          <span class="cursorPointer" @click="openDetailDialog(3, scope.row.memberId)">{{
            scope.row.receiveAmount
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="合规经营保证金"
        prop="retainage"
        class-name="small-padding fixed-width text-yellow "
      >
        <template slot-scope="scope">
          <span class="cursorPointer" @click="openDetailDialog(4, scope.row.memberId)">{{
            scope.row.retainage
          }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="变动时间" align="center" prop="createTime" width="220">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
            </el-table-column> -->
      <el-table-column
        label="提货金"
        prop="delivery"
        class-name="small-padding fixed-width text-yellow "
      >
        <template slot-scope="scope">
          <span class="cursorPointer" @click="openDetailDialog(5, scope.row.memberId)">{{
            scope.row.delivery
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="免提货金提现额度"
        prop="freeDeliveryLimit"
        width="130"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <span class="" >{{
              (scope.row && scope.row.freeDeliveryLimit  != 0 ) ?  scope.row.freeDeliveryLimit :  '0'
            }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="倍数"
        prop="multiple"
        class-name="small-padding fixed-width"
      />

      <el-table-column
        label="操作"
        width="120"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="toDetailList(scope.row)"
          >
            明细
          </el-button>
          <!-- rechargeAmount  充值金额-->
          <el-button
            type="text"
            @click="rechargeAmount(scope.row)"
          >
            充值
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改岗位对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="620px" append-to-body :close-on-click-modal="false">
      <div class="flex gap-16">
        <p>
          <span>会员昵称：{{ tempfrom.nickName }}</span>
        </p>
        <p>
          <span>会员ID：{{ tempfrom.memberId }} </span>
        </p>
        <p>
          <span>手机号码：{{ tempfrom.phone }}</span>
        </p>
      </div>
      <el-form
        class="fromclass"
        ref="form"
        :model="form"
        :rules="rules"
        label-width="125px"
      >
        <el-form-item label="账户余额" prop="type">
          <template style="display: flex;">
            <el-input
              :disabled="true"
              :precision="2"
              style="margin-right: 5px; width: 50%"
              v-model="tempfrom.balance"
              controls-position="right"
            />
            <el-input-number :precision="2" v-model="form.topUp" controls-position="right" />
          </template>
        </el-form-item>
        <el-form-item label="合规经营保证金额" prop="isDefault">
          <template style="display: flex;">
            <el-input
              :disabled="true"
              :precision="2"
              style="margin-right: 5px; width: 50%"
              v-model="tempfrom.retainage"
              controls-position="right"
            />
            <el-input-number :precision="2" v-model="form.retainUp" controls-position="right" />
          </template>
        </el-form-item>
        <el-form-item label="充值说明" prop="sort">
          <el-input
            type="textarea"
            :rows="2"
            placeholder="请输入内容"
            v-model="form.note"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <DetailDialog ref="DetailDialog" />
  </div>
</template>

<script>
import { changeRoleStatus } from "@/api/system/role";
import {
  queryMemberBalanceport,
  topUpMemberBalance,
} from "@/api/message/message.js";
import {mathFunc} from "@/utils/math";
import DetailDialog from "@/views/finance/member/components/DetailDialog.vue";

export default {
  name: "MembershipBalance",
  dicts: ["sys_normal_disable"],
  components: {
    DetailDialog,
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      menuExpand: false,
      menuNodeAll: false,
      deptExpand: true,
      deptNodeAll: false,
      // 日期范围
      dateRange: [],
      // 菜单列表
      menuOptions: [],
      // 部门列表
      deptOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: undefined,
        dateRange: undefined,
        startTime: undefined,
        endTime: undefined,
        projectName: undefined,
      },
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 表单校验
      rules: {
        roleName: [
          { required: true, message: "角色名称不能为空", trigger: "blur" },
        ],
        roleKey: [
          { required: true, message: "权限字符不能为空", trigger: "blur" },
        ],
        roleSort: [
          { required: true, message: "角色顺序不能为空", trigger: "blur" },
        ],
      },
      tempfrom: {},
    };
  },
  mounted() {
    // 设置默认近一周时间范围，首屏不触发接口请求
    this.setDefaultDateRange();
  },
  methods: {
    /** 设置默认近一周时间范围 */
    setDefaultDateRange() {
      const end = new Date();
      const start = new Date();
      start.setDate(end.getDate() - 7);
      const format = (d) => {
        const y = d.getFullYear();
        const m = (`0${d.getMonth() + 1}`).slice(-2);
        const day = (`0${d.getDate()}`).slice(-2);
        return `${y}-${m}-${day}`;
      };
      this.dateRange = [format(start), format(end)];
    },
    toDetailList(row){
      console.log(123456)
      this.$emit('toDetailPage', row)
    },
    openDetailDialog(type, memberId) {
      this.$refs.DetailDialog.open(type, memberId);
    },
    /** 查询角色列表 */
    getList() {
      if (this.dateRange == null) {
        queryMemberBalanceport(
          this.addDateRange(
            this.queryParams,
            (this.queryParams.startTime = undefined),
            (this.queryParams.endTime = undefined)
          )
        ).then((response) => {
          // console.log(response.data.records, 456)
          this.roleList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        });
      } else {
        queryMemberBalanceport(
          this.addDateRange(
            this.queryParams,
            (this.queryParams.startTime = this.dateRange[0]),
            (this.queryParams.endTime = this.dateRange[1])
          )
        ).then((response) => {
          // console.log(response.data.records, 456)
          this.roleList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        });
      }
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 10;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      // 重置后恢复默认近一周范围
      this.setDefaultDateRange();
      this.queryParams = this.$options.data().queryParams;
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 充值按钮操作 */
    rechargeAmount(row) {
      this.form = {};
      this.open = true;
      this.tempfrom = row;
      this.form.memberId = row.memberId;
      this.form.type = 9;
    },

    // 角色状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal
        .confirm('确认要"' + text + '""' + row.roleName + '"角色吗？')
        .then(function () {
          return changeRoleStatus(row.roleId, row.status);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.status = row.status === "0" ? "1" : "0";
        });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      // this.reset();
    },

    /** 新增按钮操作 */
    handleAdd() {
      // this.reset();
      this.getMenuTreeselect();
      this.open = true;
      this.title = "添加角色";
    },

    /** 选择角色权限范围触发 */
    dataScopeSelectChange(value) {
      if (value !== "2") {
        this.$refs.dept.setCheckedKeys([]);
      }
    },

    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const { balance, retainage } = this.tempfrom;
          const { topUp, retainUp } = this.form;
          if (!topUp && !retainUp) {
            this.$message({
              message: "请填写充值金额或合规经营保证金额",
              type: "error",
            });
            return ;
            // return this.$message.error('请填写充值金额或合规经营保证金额')
          }
          if (
            (topUp && (mathFunc('add', topUp, balance) < 0)) ||
            (retainUp && (mathFunc('add', retainUp, retainage) < 0))
          ) {
            //提示 充值后金额不得小于0，请重新输入
            this.$message({
              message: '充值后金额不得小于0，请重新输入',
              type: 'error'
            });
            return
          }
          topUpMemberBalance(this.form).then((res) => {
            this.$modal.msgSuccess("充值成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "/memberAccount/exportMemberBalance",
        {
          ...this.queryParams,
        },
        `余额报表.xlsx`
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.fromclass ::v-deep {
  .el-form-item__content {
    display: flex;
  }
}
//::v-deep .cursorPointer{
//  cursor: pointer;
//}
.cursorPointer{
  cursor: pointer;
}
</style>
