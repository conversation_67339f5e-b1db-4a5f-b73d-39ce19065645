<!-- 会员账户 -->


<template>
    <el-tabs v-model="activeName" @tab-click="handleClick" class="whole">
      <el-tab-pane label="会员余额" name="third" lazy>
        <MembershipBalance  @toDetailPage="toDetailPage"></MembershipBalance>
      </el-tab-pane>
      <el-tab-pane label="账户余额明细" name="fourth" lazy>
        <BalanceDetails key="fourth" ref="accountBalanceRef" tabName="fourth"></BalanceDetails>
      </el-tab-pane>
      <el-tab-pane label="余额明细" name="first" lazy>
        <BalanceDetails key="first" ref="balanceRef" tabName="first"></BalanceDetails>
      </el-tab-pane>
      <el-tab-pane label="合规经营保证金明细" name="second" lazy>
        <BalanceDetails key="second" ref="depositRef" tabName="second"></BalanceDetails>
<!--            <KeepTheGoldDetails></KeepTheGoldDetails>-->
      </el-tab-pane>

      <el-tab-pane label="提货金明细" name="fifth" lazy>
        <BalanceDetails key="third"  tabName="third"></BalanceDetails>
      </el-tab-pane>

    </el-tabs>
</template>

<script>
import BalanceDetails from "./components/BalanceDetails.vue"
// import KeepTheGoldDetails from "./components/KeepTheGoldDetails.vue"
import MembershipBalance from "./components/MembershipBalance.vue"

export default {
    name: "Member",
    components: {
        BalanceDetails,
        // KeepTheGoldDetails,
        MembershipBalance
    },
    data() {
        return {
            activeName: 'third'
        };
    },
    methods: {
        toDetailPage(row){
          this.activeName = 'first'
          this.$refs.balanceRef.queryParams.keyword = row.memberId
          this.$refs.balanceRef.open('first')
          // 明细页面打开后主动触发一次查询
          this.$refs.balanceRef.handleQuery()
        },
        handleClick(tab) {
          this.activeName = tab.name;
        }
    }
};
</script>
