<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="30%"
    center
    :modal-append-to-body="false"
    :before-close="cleanForm"
    :modal="false"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <el-form ref="form" :model="form" :rules="rules">
      <el-row style="margin-top: 10px;">
        <el-col :span="24">
          <el-form-item
            label="可提现余额"
            label-width="120px"
            prop="canPayOutMoney"
          >
            <el-input
              v-model="form.canPayOutMoney"
              placeholder="可提现余额"
              style="width: 100%;"
              disabled
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="本次提现金额" label-width="120px" prop="alterationAmount">
<!--            可提现金额为整数，两位小数-->
            <el-input-number
              class="payOutMoney"
              v-model="form.alterationAmount"
              placeholder="本次提现金额"
              style="width: 100%"
              :min="0"
              :precision="2"
              :controls="false"
            >
            </el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" size="small" :loading="saveFlag" @click="saveForm">确 定</el-button>
      <el-button size="small" @click="cleanForm">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from "moment";
import {accountTopUp, getPlatformDetail, update, updateTradeTime} from "@/api/transferAssets/lotList.js";
export default {
  name: "PayoutsDialog",
  data() {
    return {
      dialogVisible: false,
      title: "提现",
      saveFlag: false,
      form: {
        canPayOutMoney: "",
        alterationAmount: "",
      },
      rules: {
      },
    };
  },

  methods: {
    init() {
      this.dialogVisible = true;
      getPlatformDetail()
        .then((res) => {
          if(res.code === 200) {
            this.form = {
              canPayOutMoney: res.data,
            };
          }
        });
    },

    cleanForm() {
      this.dialogVisible = false;
      this.saveFlag = false;
      this.form = {
      };
    },

    saveForm() {
      if (this.saveFlag) {
        return;
      }
      this.saveFlag = true;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const params = {
            ...this.form,
          }
          accountTopUp(params)
            .then((res) => {
              this.$message.success("保存成功");
              this.cleanForm();
              this.$emit("getData");
            })
            .finally(() => {
              this.saveFlag = false;
            });
        } else {
          this.saveFlag = false;
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.payOutMoney {
  ::v-deep .el-input{
    .el-input__inner {
      text-align: left;
    }
  }
}
</style>
