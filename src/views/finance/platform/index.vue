/**
财务管理 平台账户
*/
<template>
  <div class="app-container">

    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="会员ID/昵称/手机号/姓名" label-width="180px">
        <el-input v-model="queryParams.keyword"></el-input>
      </el-form-item>
      <el-form-item label="发生时间">
        <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
         <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>


    <el-row :gutter="10" class="action-flex-end">
      <el-col :span="1.5">
        <el-button @click="handlePayouts"
                   >提现</el-button>

        <el-button @click="handleExport">导出</el-button>
      </el-col>
    </el-row>


    <div class="amount-box">
      <i class="el-icon-info"></i>
      <p>
        总额: <span>{{ grossamount }}</span>
      </p>
    </div>

    <el-table v-loading="loading" :data="roleList" @selection-change="handleSelectionChange">
      <el-table-column label="发生时间" prop="alterationTime" width="170">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.alterationTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="单号" prop="ordNumber" :show-overflow-tooltip="true" width="220"/>
      <el-table-column label="会员ID" prop="memberId" :show-overflow-tooltip="true" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.memberId || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="会员" prop="roleSort" width="230">
        <template slot-scope="scope">
          <div class="span_all" v-if="scope.row.memberId">
            <div class="span_left">
              <el-image
                v-if="scope.row.headPortrait"
                :src="scope.row.headPortrait"
                fit="fill"
              ></el-image>
              <img
                v-else
                src="@/assets/images/member.png"
                alt="donate"
                width="100%"
              />
            </div>
            <div class="span_right">
              <div>{{ scope.row.nickName }}</div>
              <div>
                {{ scope.row.phone }} 【{{ scope.row.name }}】
              </div>
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column label="变动金额" prop="alterationAmount" class-name="small-padding fixed-width"/>
      <el-table-column label="变动前" prop="beforeAlterationAmount" class-name="small-padding fixed-width"/>
      <el-table-column label="变动后" prop="afterAlterationAmount" class-name="small-padding fixed-width"/>
      <el-table-column label="类型" prop="typeDesc" class-name="small-padding fixed-width"/>
    </el-table>

    <div>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                  @pagination="getList"/>
    </div>
    <PayoutsDialog ref="PayoutsDialog" @getData="getList"></PayoutsDialog>
  </div>
</template>

<script>
import {addRole, dataScope, delRole, getRole, updateRole} from "@/api/system/role";
import {queryAllaccountport, queryTotal} from "@/api/message/message.js"
import PayoutsDialog from "@/views/finance/platform/cmps/PayoutsDialog.vue";

export default {
  name: "Platform",
  dicts: ['sys_normal_disable'],
  components: {
    PayoutsDialog
  },
  data() {
    return {
      grossamount: '',
      // 搜索
      queryForm: {},
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      menuExpand: false,
      menuNodeAll: false,
      deptExpand: true,
      deptNodeAll: false,
      // 日期范围
      dateRange: [],
      // 菜单列表
      menuOptions: [],
      // 部门列表
      deptOptions: [],

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        startTime: undefined,
        endTime: undefined,
        keyword: undefined,
        // roleName: undefined,
        // roleKey: undefined,
        // status: undefined
      },
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 表单校验
      rules: {
        roleName: [
          {required: true, message: "角色名称不能为空", trigger: "blur"}
        ],
        roleKey: [
          {required: true, message: "权限字符不能为空", trigger: "blur"}
        ],
        roleSort: [
          {required: true, message: "角色顺序不能为空", trigger: "blur"}
        ]
      }
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    /** 查询角色列表 */
    // getList() {
    //   if (this.dateRange == null) {
    //     queryAllaccountport(this.addDateRange(this.queryParams,
    //       this.queryParams.startTime = undefined, this.queryParams.endTime = undefined)).then(response => {
    //         // console.log(response.data.records, 456)
    //         this.roleList = response.data.records;
    //         this.total = response.data.total;
    //         this.loading = false;
    //       }).catch((err) => {
    //         console.log(err)
    //       });
    //   } else {

    //     queryAllaccountport(this.addDateRange(this.queryParams,
    //       this.queryParams.startTime = this.dateRange[0], this.queryParams.endTime = this.dateRange[1])).then(response => {
    //         // console.log(response.data.records, 456)
    //         this.roleList = response.data.records;
    //         this.total = response.data.total;
    //         this.loading = false;
    //       }).catch((err) => {
    //         console.log(err)
    //       });
    //   }
    //   queryTotal().then((res) => {
    //     console.log(res, 2414212)
    //     this.grossamount = res.data
    //     this.loading = false;

    //   })
    // },

    // getList() {
    //   if (this.dateRange == null) {
    //     queryAllaccountport(this.addDateRange(this.queryParams,
    //       this.queryParams.startTime = undefined, this.queryParams.endTime = undefined)).then(response => {
    //         // console.log(response.data.records, 456)
    //         this.roleList = response.data.records;
    //         this.total = response.data.total;
    //         this.loading = false;
    //       }).catch((err) => {
    //         console.log(err)
    //       });
    //   } else {
    //     const startDate = new Date(this.dateRange[0]);
    //     startDate.setHours(0, 0, 0, 0); // 设置为当天的 00:00:00
    //     const endDate = new Date(this.dateRange[1]);
    //     endDate.setHours(23, 59, 59, 999); // 设置为当天的 23:59:59
    //     console.log(startDate,);
    //     queryAllaccountport(this.addDateRange(this.queryParams,
    //       this.queryParams.startTime = startDate, this.queryParams.endTime = endDate)).then(response => {
    //         this.roleList = response.data.records;
    //         this.total = response.data.total;
    //         this.loading = false;
    //       }).catch((err) => {
    //         console.log(err)
    //       });
    //   }
    //   queryTotal().then((res) => {
    //     // console.log(res.data, 2414212)
    //     this.grossamount = res.data
    //     this.loading = false;
    //     console.log(this.grossamount);
    //   })
    // },
    getList() {
      if (this.dateRange == null) {
        queryAllaccountport(this.queryParams).then(response => {
          this.roleList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        }).catch((err) => {
          console.log(err)
        });
      } else {
        const startDate = this.dateRange[0] ? this.dateRange[0] + ' 00:00:00' : undefined;
        const endDate = this.dateRange[1] ? this.dateRange[1] + ' 23:59:59' : undefined;

        queryAllaccountport({
          ...this.queryParams,
          startTime: startDate,
          endTime: endDate
        }).then(response => {
          this.roleList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        }).catch((err) => {
          console.log(err)
        });

        queryTotal({
          ...this.queryParams,
          startTime: startDate,
          endTime: endDate
        }).then((res) => {
          this.grossamount = res.data;
        }).finally(() => {
          this.loading = false;
        })
      }

    },


    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮（数据权限）
    cancelDataScope() {
      this.openDataScope = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      this.menuExpand = false,
        this.menuNodeAll = false,
        this.deptExpand = true,
        this.deptNodeAll = false,
        this.form = {
          roleId: undefined,
          roleName: undefined,
          roleKey: undefined,
          roleSort: 0,
          status: "0",
          menuIds: [],
          deptIds: [],
          menuCheckStrictly: true,
          deptCheckStrictly: true,
          remark: undefined
        };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1; // 将pageNum重置为1
      this.queryParams.pageSize = 10;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams = this.$options.data().queryParams;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.roleId)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleDataScope":
          this.handleDataScope(row);
          break;
        case "handleAuthUser":
          this.handleAuthUser(row);
          break;
        default:
          break;
      }
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getMenuTreeselect();
      this.open = true;
      this.title = "添加角色";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const roleId = row.roleId || this.ids
      const roleMenu = this.getRoleMenuTreeselect(roleId);
      getRole(roleId).then(response => {
        this.form = response.data;
        this.open = true;
        this.$nextTick(() => {
          roleMenu.then(res => {
            let checkedKeys = res.checkedKeys
            checkedKeys.forEach((v) => {
              this.$nextTick(() => {
                this.$refs.menu.setChecked(v, true, false);
              })
            })
          });
        });
        this.title = "修改角色";
      });
    },


    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.roleId != undefined) {
            this.form.menuIds = this.getMenuAllCheckedKeys();
            updateRole(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.form.menuIds = this.getMenuAllCheckedKeys();
            addRole(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 提交按钮（数据权限） */
    submitDataScope: function () {
      if (this.form.roleId != undefined) {
        this.form.deptIds = this.getDeptAllCheckedKeys();
        dataScope(this.form).then(response => {
          this.$modal.msgSuccess("修改成功");
          this.openDataScope = false;
          this.getList();
        });
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const roleIds = row.roleId || this.ids;
      this.$modal.confirm('是否确认删除角色编号为"' + roleIds + '"的数据项？').then(function () {
        return delRole(roleIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      const startDate = this.dateRange[0] ? this.dateRange[0] + ' 00:00:00' : undefined;
      const endDate = this.dateRange[1] ? this.dateRange[1] + ' 23:59:59' : undefined;
      this.download('/platformAccount/exportPlatformBalance', {
        ...this.queryParams,
        startTime: startDate,
        endTime: endDate
      }, `平台账户.xlsx`)
    },

    handlePayouts(){
      this.$refs.PayoutsDialog.init()
    }
  }
};
</script>
