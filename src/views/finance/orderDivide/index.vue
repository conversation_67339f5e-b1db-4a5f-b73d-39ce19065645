<!-- 会员账户 -->


<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick" class="whole">
      <el-tab-pane v-if="roleId != 8" label="未进入分账周期" name="NOTIN" lazy>
        <NoDoneSubBill key="NOTIN" ref="noDoneSubBillNOTIN" :status="activeName"></NoDoneSubBill>
      </el-tab-pane>
      <el-tab-pane label="未分账" name="INIT" lazy>
        <NoDoneSubBill key="INIT" ref="noDoneSubBillINIT" :status="activeName"></NoDoneSubBill>
      </el-tab-pane>
      <el-tab-pane v-if="roleId != 8" label="分账单" name="WAITING" lazy>
        <DoneSubBill ref="doneSubBillWAITING"></DoneSubBill>
      </el-tab-pane>
      <el-tab-pane v-if="roleId != 8" label="已分账" name="ALL" lazy>
        <NoDoneSubBill key="ALL" ref="noDoneSubBillALL" :status="activeName"></NoDoneSubBill>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import DoneSubBill from "./components/DoneSubBill.vue"
import NoDoneSubBill from "./components/NoDoneSubBill.vue"
import store from "@/store";
export default {
  name: "OrderDivide",
  components: {
    DoneSubBill,
    NoDoneSubBill,
  },
  data() {
    return {
      activeName: 'INIT',
      roleId: store.getters.roleId,
    };
  },

  methods: {
    handleClick(tab) {
      this.activeName = tab.name;
    }
  }
};
</script>
