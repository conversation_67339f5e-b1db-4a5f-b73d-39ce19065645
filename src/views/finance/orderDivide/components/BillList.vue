/** 会员余额 */

<template>
  <div class="whole">
    <el-table v-loading="loading" :data="roleList"  row-key="id" >
      <el-table-column
        label="会员ID"
        prop="memberId"
        :show-overflow-tooltip="true"
        width="130"
      />
      <el-table-column label="会员" prop="roleSort" width="260">
        <template slot-scope="scope">
          <div class="span_all">
            <div class="span_left">
              <img
                v-if="scope.row.headPortrait"
                :src="scope.row.headPortrait"
                alt=""
              />
              <img
                v-else
                src="@/assets/images/member.png"
                alt="donate"
              />
            </div>
            <div class="span_right">
              <div>{{ scope.row.nickName }}</div>
              <div>{{ scope.row.phone }}【{{ scope.row.name }}】</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        color:red
        label="可用金额"
        prop="availableCredit"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <span class="text-green">{{
              scope.row.availableCredit
            }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="冻结金额"
        prop="freezeAmount"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <span class="text-red">{{ scope.row.freezeAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="总额"
        prop="totalAmount"
        class-name="small-padding fixed-width text-main"
      />
      <el-table-column
        label="合规经营保证金"
        prop="retainage"
        class-name="small-padding fixed-width text-yellow"
      />
      <el-table-column
        label="提货金"
        prop="delivery"
        class-name="small-padding fixed-width text-yellow"
      />
      <el-table-column
        label="倍数"
        prop="multiple"
        class-name="small-padding fixed-width"
      />

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="rechargeAmount(scope.row)"
          >
            明细
          </el-button>
          <!-- rechargeAmount  充值金额-->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <OrderDialog ref="orderDialog" />
  </div>
</template>

<script>
import {
  queryMemberBalanceport,
} from "@/api/message/message.js";
import OrderDialog from "./OrderDialog.vue";
export default {
  name: "BillList",
  components: {OrderDialog},
  dicts: ["sys_normal_disable"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: undefined,
        startTime: undefined,
        endTime: undefined,
      },
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label",
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询角色列表 */
    getList() {
      if (this.dateRange == null) {
        queryMemberBalanceport(
          this.addDateRange(
            this.queryParams,
            (this.queryParams.startTime = undefined),
            (this.queryParams.endTime = undefined)
          )
        ).then((response) => {
          this.roleList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        });
      } else {
        queryMemberBalanceport(
          this.addDateRange(
            this.queryParams,
            (this.queryParams.startTime = this.dateRange[0]),
            (this.queryParams.endTime = this.dateRange[1])
          )
        ).then((response) => {
          // console.log(response.data.records, 456)
          this.roleList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        });
      }
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 充值按钮操作 */
    rechargeAmount(row) {
      this.$refs.orderDialog.openDialog(row, true);
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "/memberAccount/exportMemberBalance",
        {
          ...this.queryParams,
        },
        `余额报表.xlsx`
      );
    },
  },
};
</script>
<style lang="scss" scoped>

</style>
