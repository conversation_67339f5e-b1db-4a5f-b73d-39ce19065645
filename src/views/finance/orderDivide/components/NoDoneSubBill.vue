/** 会员余额 */

<template>
  <div class="whole">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-if="showSearch"
    >
      <el-form-item v-if="isInit" :label="'下单时间'">
        <el-date-picker
          v-model="orderDateRange"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="isNotIn ? '下单时间' : isInit ? '结算时间' : '分账时间'">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="订单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单号"
          clearable
          style="width: 240px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item v-if="isAll" label="分账结果" prop="status">
        <el-select v-model="queryParams.status">
          <el-option
            v-for="v in statusOptions"
            :key="v.value"
            :label="v.label"
            :value="v.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属项目" label-width="82px" prop="projectName">
        <el-input v-model="queryParams.projectName"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button size="medium" @click="resetQuery">重置</el-button>
        <el-button
          type="primary"
          size="medium"
          @click="handleQuery"
          >查询</el-button
        >
      </el-form-item>
    </el-form>

    <el-row v-if="!isNotIn" :gutter="10" class="action-flex-end">
      <el-col v-if="isInit" :span="1.5">
        <el-button
          type="primary"
          @click="createSubBill"
        >生成分账单</el-button>
      </el-col>
      <el-col v-if="!isNotIn" :span="1.5">
        <el-button
          @click="handleExport"
        >导出</el-button>
      </el-col>
    </el-row>

    <el-table ref="tabelRef" v-loading="loading" :data="billList"  row-key="id" @selection-change="handleSelectionChange">
<!--      <el-table-column v-if="isInit" key="selection" type="selection" width="50" align="center" reserve-selection />-->
      <el-table-column
        label="单号"
        key="orderNo"
        prop="orderNo"
        :show-overflow-tooltip="true"
        width="220"
      />
      <el-table-column label="会员" key="memberBox" prop="memberBox" width="260">
        <template slot-scope="scope">
          <div class="span_all">
            <div class="span_left">
              <img
                v-if="scope.row.buyMemberHeadPortrait"
                :src="scope.row.buyMemberHeadPortrait"
                alt=""
              />
              <img
                v-else
                src="@/assets/images/member.png"
                alt="donate"
              />
            </div>
            <div class="span_right">
              <div>{{ scope.row.buyMemberNickName }} <span>({{ scope.row.buyMemberId}})</span></div>
              <div>{{ scope.row.buyMemberAccountNumber }}【{{ scope.row.buyMemberName }}】</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="商品/凭证" key="shoppz" prop="shoppz" width="220">
        <template slot-scope="scope">
          <div class="span_all">
            <div class="span_left">
              <img
                v-if="scope.row.lotImg"
                :src="scope.row.lotImg"
                alt=""
              />
              <img
                v-else
                src="@/assets/images/member.png"
                alt="donate"
              />
            </div>
            <div class="span_right">
              <div>{{ scope.row.lotName }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="projectName" label="所属项目" width="200"/>
      <el-table-column v-if="isInit" key="createTime" prop="createTime" label="结算时间" min-width="110">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="isAll" key="allotSettleTime" prop="allotSettleTime" label="分账时间" min-width="110">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.allotSettleTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column key="orderCreateTime" prop="orderCreateTime" label="下单时间" min-width="110">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.orderCreateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column key="orderFinishTime" prop="orderFinishTime" label="完成时间" min-width="110">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.orderFinishTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column key="type" prop="type" label="订单类型" min-width="120">
        <template slot-scope="scope">
          <span>{{ {'LOT': '凭证订单', 'GOODS': '商城订单', 'DELIVERY': '提货订单'}[scope.row.type] || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column key="count" prop="count" label="数量" min-width="120"></el-table-column>
      <el-table-column
        label="单价"
        key="price"
        prop="price"
        min-width="100"
        class-name="small-padding fixed-width"
      >
      </el-table-column>
      <el-table-column
        label="总价"
        key="allPrice"
        prop="allPrice"
        min-width="100"
        class-name="small-padding fixed-width"
      >
      </el-table-column>
      <el-table-column
        label="实付金额"
        key="allotPrice"
        prop="allotPrice"
        min-width="100"
        class-name="small-padding fixed-width text-main"
      />
      <el-table-column
        label="交易手续费(买家)"
        key="payTradeCommission"
        prop="payTradeCommission"
        min-width="135"
        class-name="small-padding fixed-width text-yellow"
      />
      <el-table-column
        :label="isNotIn ? '交易手续费(商家)' : '交易手续费(卖家)'"
        key="recTradeCommission"
        prop="recTradeCommission"
        min-width="135"
        class-name="small-padding fixed-width text-yellow"
      />
      <el-table-column
        label="合规经营保证金"
        key="complianceMargin"
        prop="complianceMargin"
        min-width="135"
        class-name="small-padding fixed-width"
      />
      <el-table-column
        label="平台技术服务费"
        key="pltServiceFee"
        prop="pltServiceFee"
        min-width="135"
        class-name="small-padding fixed-width"
      />
      <el-table-column
        label="推荐服务费"
        key="referralServiceFee"
        prop="referralServiceFee"
        min-width="125"
        class-name="small-padding fixed-width"
      />
      <el-table-column
        label="商家收款"
        key="recIncome"
        prop="recIncome"
        min-width="125"
        class-name="small-padding fixed-width"
      />
      <el-table-column
        label="服务中心收款"
        key="serviceIncome"
        prop="serviceIncome"
        min-width="130"
        class-name="small-padding fixed-width"
      />
      <el-table-column
        label="会员收款"
        key="memberIncome"
        prop="memberIncome"
        min-width="125"
        class-name="small-padding fixed-width"
      />
      <el-table-column
        label="平台净收益"
        key="pltIncome"
        prop="pltIncome"
        min-width="125"
        class-name="small-padding fixed-width"
      />
      <el-table-column v-if="isAll" key="status" prop="status" label="分账结果" min-width="110" :formatter="row => findItemKey(statusOptions, row.status, 'label')">
      </el-table-column>

      <el-table-column
        v-if="!isNotIn"
        key="actionColumn"
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        min-width="80"
        fixed="right"
      >
        <template class="flex a-center gap-16" slot-scope="scope"><!--
          <el-button
            v-if="isInit"
            type="text"
            @click="rechargeAmount(scope.row)"
          >
            编辑
          </el-button>-->
          <el-button
            type="text"
            @click="rechargeAmount(scope.row, true)"
          >
            明细
          </el-button>
          <!-- rechargeAmount  充值金额-->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-if="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <OrderDialog ref="orderDialog" @ok="getList" />
  </div>
</template>

<script>
import OrderDialog from "./OrderDialog.vue";
import {bathAllotOrder, getAllotOrderPage} from "@/api/orderDivide";
import {findItemKey} from "@/utils/utils";
import {formatMonthDateStart, formatNowDate} from '@/utils/date'
export default {
  name: "NoDoneSubBill",
  components: {OrderDialog},
  dicts: ["sys_normal_disable"],
  props: {
    status: {
      type: String,
      default: "INIT",
    },
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      billList: [],
      // 弹出层标题
      title: "",
      // 下单时间范围
      orderDateRange: [],
      // 日期范围 当月第一天至当前日期
      dateRange: [formatMonthDateStart(), formatNowDate()],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        canAllot: 'Y',
        orderNo: undefined,
        startOrderCreateTime: undefined,
        endOrderCreateTime: undefined,
        status: 'INIT',
        projectName: undefined,
      },
      statusOptions: [
        {
          value: "WAITING",
          label: "未执行",
        },
        {
          value: "FINISH",
          label: "执行成功",
        },
        {
          value: "ERROR",
          label: "执行失败",
        },
      ],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label",
      },
    };
  },
  watch: {
    // status() {
    //   this.handleQuery()
    // }
  },
  computed: {
    // 已分账
    isAll() {
      return this.status === 'ALL';
    },
    // 未分账
    isInit() {
      return this.status === 'INIT';
    },
    // 未进入分账周期
    isNotIn() {
      return this.status === 'NOTIN';
    },
  },
  created() {
    this.handleQuery()
  },
  methods: {
    findItemKey,
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
    },
    handleQueryParams() {
      console.log('queryParams====>',this.queryParams);
      const status = this.isAll ? (['WAITING', 'FINISH', 'ERROR'].includes(this.queryParams.status) ? this.queryParams.status : undefined) : this.isNotIn ? undefined : this.status;
      Object.assign(this.queryParams, {
        status,
        statusList: this.isAll && !status ? ['WAITING', 'FINISH', 'ERROR'] : undefined,
        canAllot: this.isNotIn ? 'N' : 'Y',
      });
      this.addDateRange(
        this.queryParams,
        this.orderDateRange,
        [
          'startOrderCreateTime',
          'endOrderCreateTime'
        ],
        'joinTime'
      )
      this.addDateRange(
        this.queryParams,
        this.dateRange,
        [
          this.isNotIn ? 'startOrderCreateTime' : this.isInit ? 'startCreateTime' : 'startAllotSettleTime',
          this.isNotIn ? 'endOrderCreateTime' : this.isInit ? 'endCreateTime' : 'endAllotSettleTime'
        ],
        'joinTime'
      )
      return this.queryParams;
    },
    /** 查询角色列表 */
    getList() {
      getAllotOrderPage(
        this.handleQueryParams(this.queryParams)
      ).then((response) => {
        // console.log(response.data.records, 456)
        response.data.records.forEach(record => {
          for (let key in record) {
            if (record[key] === null && !['buyMemberHeadPortrait', 'lotImg'].includes(key)) {
              record[key] = '-';
            }
          }
        })
        this.billList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.orderDateRange = [];
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 搜索按钮操作 */
    handleQuery(params = {}) {
      this.queryParams = {
        ...this.queryParams,
        pageNum: 1,
        pageSize: 10,
        ...params
      };
      this.getList();
    },

    handleSaveSearchQuery(params = {}) {
      this.queryParams = {
        ...this.queryParams,
        ...params
      };
      this.getList();
    },

    /** 充值按钮操作 */
    rechargeAmount(row, isDisable) {
      this.$refs.orderDialog.openDialog(row, isDisable);
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getMenuTreeselect();
      this.title = "添加角色";
    },
    // 生成分账单
    async createSubBill(){
      if (!this.dateRange || this.dateRange.length === 0) {
        this.$message.error('请先选择发生时间')
        return
      }
      this.loading = true
      const { pageNum, pageSize, ...rest } = this.queryParams
      const params = {
        ...rest,
      }
      await bathAllotOrder(params)
      this.$message.success('生成分账单成功')
      this.loading = false
      this.$refs.tabelRef.clearSelection()
      this.getList()
    },

    /** 导出按钮操作 */
    handleExport(row) {
      this.download(
        "/allotOrder/query/export",
        this.handleQueryParams(this.queryParams),
        `${this.isInit ? '未分账' : '已分账'}报表.xlsx`,
        {
          headers: { 'Content-Type': 'application/json' },
        }
      );
    },
  },
};
</script>
<style lang="scss" scoped>

</style>
