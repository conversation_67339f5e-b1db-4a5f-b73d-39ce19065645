/** 会员余额 */

<template>
  <div class="whole">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="生成时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="queryParams.status">
          <el-option
            v-for="v in statusOptions"
            :key="v.value"
            :label="v.label"
            :value="v.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button size="medium" @click="resetQuery">重置</el-button>
        <el-button
          type="primary"
          size="medium"
          @click="handleQuery"
        >查询</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="action-flex-end">
      <el-col :span="1.5">
        <el-button
          @click="handleBtnExport"
        >导出</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="billList"  row-key="id" @selection-change="handleSelectionChange">
      <el-table-column
        label="生成日期"
        prop="createTime"
        width="170"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="结算周期"
        prop="createTime"
        width="200"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.startSettleCycle }} - {{ scope.row.endSettleCycle }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="实付金额"
        prop="payPrice"
        min-width="120"
        class-name="small-padding fixed-width text-main"
      />
      <el-table-column
        label="交易手续费(买家)"
        prop="payTradeCommission"
        min-width="135"
        class-name="small-padding fixed-width text-yellow"
      />
      <el-table-column
        label="交易手续费(卖家)"
        prop="recTradeCommission"
        min-width="135"
        class-name="small-padding fixed-width text-yellow"
      />
      <el-table-column
        label="合规经营保证金"
        prop="complianceMargin"
        min-width="135"
        class-name="small-padding fixed-width"
      />
      <el-table-column
        label="平台技术服务费"
        prop="pltServiceFee"
        min-width="135"
        class-name="small-padding fixed-width"
      />
      <el-table-column
        label="推荐服务费"
        prop="referralServiceFee"
        min-width="120"
        class-name="small-padding fixed-width"
      />
      <el-table-column
        label="商家收款"
        prop="recIncome"
        min-width="120"
        class-name="small-padding fixed-width"
      />
      <el-table-column
        label="服务中心收款"
        prop="serviceIncome"
        min-width="135"
        class-name="small-padding fixed-width"
      />
      <el-table-column
        label="会员收款"
        prop="memberIncome"
        min-width="120"
        class-name="small-padding fixed-width"
      />
      <el-table-column
        label="平台净收益"
        prop="pltIncome"
        min-width="135"
        class-name="small-padding fixed-width"
      />
      <el-table-column
        label="状态"
        prop="status"
        :formatter="row => findItemKey(statusOptions, row.status, 'label')"
        width="80"
      />

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleExport(scope.row)"
          >
            导出
          </el-button>

          <el-button
            v-if="scope.row.status === 'INIT'"
            type="text"
            @click="submitControl(scope.row)"
          >
            确认
          </el-button>

          <el-button
            v-if="scope.row.status === 'INIT'"
            type="text"
            @click="cancelControl(scope.row)"
          >
            取消
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <OrderDialog ref="orderDialog" />
  </div>
</template>

<script>
import OrderDialog from "./OrderDialog.vue";
import {cancelAllotBathOrder, confirmAllotBathOrder, getAllotBathOrderPage} from "@/api/orderDivide";
import {findItemKey} from "@/utils/utils";
import {formatMonthDateStart, formatNowDate} from '@/utils/date'
export default {
  name: "DoneSubBill",
  components: {OrderDialog},
  dicts: ["sys_normal_disable"],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      billList: [],
      // 弹出层标题
      title: "",
      // 日期范围
      dateRange: [formatMonthDateStart(), formatNowDate()],
      statusOptions: [
        {
          value: "INIT",
          label: "待确认",
        },
        {
          value: "ALLOT",
          label: "已确认",
        },
        {
          value: "CANCEL",
          label: "已取消",
        },
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        startTime: undefined,
        endTime: undefined,
        status: undefined,
      },
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label",
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    findItemKey,
    cancelControl(row){
      this.$confirm("取消本次分账<br/><span style='color: red'>取消后可重新勾选生成分账单</span>", "提示", {
        confirmButtonText: "继续",
        cancelButtonText: "取消",
        type: "warning",
        customClass:'subBillConfirm',
        dangerouslyUseHTMLString: true,
      })
          .then(async() => {
            await cancelAllotBathOrder({id: row.id})
            this.$message({
              type: "success",
              message: "取消成功!",
            });
            this.getList();
          })
          .catch(() => {
          });
    },
    submitControl(row){
      this.$prompt("请确认分账账户及金额的准确性<br/><span style='color: red'>本操作不可撤回，</span>确认请输入登录密码", "提示", {
        confirmButtonText: "继续",
        cancelButtonText: "取消",
        inputType: "input",
        type: "warning",
        customClass:'subBillConfirm',
        dangerouslyUseHTMLString: true,
        inputValidator: (value) => {
          if (!value) {
            return "请输入用户密码"
          }
          if (value.indexOf(" ") >= 0) {
            return "用户密码不能有空格"
          }
          if (!/^.{5,20}$/.test(value)) {
            return "用户密码长度必须介于 5 和 20 之间"
          }
          return true;
        },
      })
        .then(async ({ value }) => {
          await confirmAllotBathOrder({id: row.id, password: value})
          this.$message({
            type: "success",
            message: "确认成功!",
          });
          this.getList();
        })
        .catch(() => {
        });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
    },
    handleQueryParams() {
      return this.addDateRange(
        this.queryParams,
        this.dateRange,
        ['startCreateTime', 'endCreateTime'],
        'joinTime'
      )
    },
    /** 查询角色列表 */
    getList() {
      getAllotBathOrderPage(
        this.handleQueryParams(),
      ).then((response) => {
        this.billList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams.status = undefined;
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 充值按钮操作 */
    rechargeAmount(row) {
      this.$refs.orderDialog.openDialog(row);
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getMenuTreeselect();
      this.title = "添加角色";
    },

    /** 导出按钮操作 */
    handleExport(row) {
      this.download(
        "/allotBathOrder/export",
        {
          id: row.id,
        },
        `分账报表.xlsx`
      );
    },

    /** 导出按钮操作 */
    handleBtnExport() {
      this.download(
        "/allotBathOrder/query/export",
        this.handleQueryParams(),
        `分账单报表.xlsx`,
        {
          headers: { 'Content-Type': 'application/json' },
        }
      );
    },
  },
};
</script>
<style lang="scss" scoped>


</style>
<style lang="scss" >
.subBillConfirm {
  .el-message-box__container {
    .el-icon-warning{
      transform: translateY(-20px);
    }

  }
}
</style>
