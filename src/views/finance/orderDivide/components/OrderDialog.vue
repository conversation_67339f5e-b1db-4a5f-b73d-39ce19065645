<template>
  <el-dialog :title="title" :visible.sync="open" width="820px" append-to-body :close-on-click-modal="isDisable">
    <div class="flex gap-16">
      <div
        v-for="[k, r] in Object.entries(groupedData)"
        :key="k"
      >
        <p>{{ r[0].memberNickName || r[0].memberName || '-' }}（{{ memberTypeMap[r[0].memberType] }}）：</p>
        <div v-for="v in r" :key="v.id">
          <p v-if="v.memberAccountType === 'AB' && v.memberType === 'B'">初始余额：{{!v.amount ? '' : balanceSymbolMap[buyerBalanceType]}}{{ buyerMoney }}</p>
          <p v-if="v.memberAccountType === 'AB'">余额： {{!v.amount ? '' : balanceSymbolMap[v.balanceType]}}{{ v.amount }}</p>
          <p v-if="v.memberAccountType === 'RE'">合规经营保证金： {{!v.amount ? '' : balanceSymbolMap[v.balanceType]}}{{ v.amount }}</p>
        </div>
      </div>
    </div>
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="80px"
    >
      <el-table :data="itemList" style="width: 100%">
        <el-table-column
          prop="balanceDetailTypeValue"
          label="名目"
        >
        </el-table-column>
        <el-table-column
          prop="payMemberName"
          label="出金方"
        >
          <template slot-scope="scope">
            {{ scope.row.payMemberNickName || scope.row.payMemberName || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="receMemberName"
          label="入金方"
        >
          <template slot-scope="scope">
            {{ scope.row.receMemberNickName || scope.row.receMemberName || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="receMemberAccountType"
          label="入金账户"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.receMemberAccountType === 'AB'">余额</span>
            <span v-if="scope.row.receMemberAccountType === 'RE'">合规经营保证金</span>
          </template>
        </el-table-column>

        <el-form-item label="" prop="topUp">
        <el-table-column
          label="金额"
          width="220"
          fixed="right"
        >
            <template slot-scope="scope,index">
                <el-input-number
                  :min="0.00"
                  :precision="2"
                  :parser="value => parserFloat(value, 2)"
                  :disabled="isDisable" v-model="scope.row.amount" controls-position="right"
                  :controls="false"
                  @blur="handleAmountBlur(scope.row)"
                />
            </template>
        </el-table-column>
        </el-form-item>
      </el-table>
    </el-form>
    <div slot="footer" class="dialog-footer" v-if="!isDisable">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {getAllotOrderDetail, reCalcAllotOrder, setAllotOrder} from "@/api/orderDivide";
import {parserFloat} from "@/utils/validate";

export default {
  name: "OrderDialog",
  data() {
    return {
      title: "明细",
      open: false,
      memberTypeMap: {
        'B': '买家',
        'R': '卖家',
        'P': '平台',
        'S': '服务中心',
        'O': '商家',
        'E': '交易所',
      },
      balanceSymbolMap: {
        'INCOME': '+',
        'PAY': '-'
      },
      order: {},
      itemList: [],
      summaryList: [],
      groupedData: {},
      buyerBalanceType: '',
      buyerMoney: '',
      form: {
        topUp: 0,
        retainUp: 0,
        note: ""
      },
      rules: {
        amount: [
          { required: true, message: "请输入金额", trigger: "blur" }
        ]
        // topUp: [
        //   { required: true, message: "请输入充值金额", trigger: "blur" }
        // ],
        // retainUp: [
        //   { required: true, message: "请输入合规经营保证金额", trigger: "blur" }
        // ]
      },
      isDisable: false
    };
  },
  methods: {
    parserFloat,
    handleAmountChange(row) {
      console.log('row....',row.amount);
      if (row.amount === null || row.amount === undefined) {
        row.amount = 0.00; // 或者其他默认值
      }
    },
    handleGroupedData(summaryList) {
      const groupedData = summaryList.reduce((acc, item) => {
        const key = item.memberId + item.memberType;
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(item);
        return acc;
      }, {});
      this.summaryList = summaryList
      this.groupedData = groupedData
    },
    async handleAmountBlur() {
      const res = await reCalcAllotOrder({
        id: this.order.id,
        items: this.itemList
      })
      this.handleGroupedData(res.data)
    },
    findBuyerMember(summaryList) {
      return summaryList.find(item => item.memberAccountType === 'AB' && item.memberType === 'B')
    },
    async openDialog(row,isDisable = false) {
      this.open = true;
      this.isDisable = isDisable;
      const res = await getAllotOrderDetail({id: row.id})
      const { order, itemList, summaryList } = res.data
      this.order = order
      this.itemList = itemList
      const { balanceType, amount } = this.findBuyerMember(summaryList)
      this.buyerBalanceType = balanceType
      this.buyerMoney = amount
      this.handleGroupedData(summaryList)
      // console.log(this.groupedData)
      // this.rules = {
      //   amount: [
      //     { required: true, message: "请输入金额", trigger: "blur" }
      //   ]
      // }
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const { balanceType, amount } = this.findBuyerMember(this.summaryList)
          if (this.buyerBalanceType !== balanceType || this.buyerMoney !== amount) {
            this.$message.error('买家余额与初始余额不一致，请重新设置分账金额')
            return
          }
          setAllotOrder({
            id: this.order.id,
            ids: [this.order.id],
            items: this.itemList
          }).then(res => {
            this.$message({
              message: '重新设置订单分账金额成功',
              type: 'success'
            })
            this.cancel()
            this.$emit("ok");
          })
        }
      });
    },
    cancel() {
      this.open = false;
      this.groupedData = {}
      this.itemList = []
      // this.$emit("cancel");
    }
  },
}
</script>

<style scoped lang="scss">

</style>
