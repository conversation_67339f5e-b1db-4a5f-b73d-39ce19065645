<template>
  <el-dialog
    title="上传发票"
    :visible.sync="visible"
    width="60%"
    center
    :modal-append-to-body="false"
    :before-close="cleanForm"
    :modal="false"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <el-row>
      <el-col :span="24" style="margin-bottom: 12px">
        <span>发票明细：</span>
        <a v-if="this.flag != 2" @click="handleAdd">新增一行</a>
      </el-col>
      <el-col :span="24">
        <el-table :data="tableData" border style="width: 100%">
          <el-table-column prop="invoiceNo" label="发票号码" min-width="150px">
            <template slot-scope="scope">
              <el-input
                style="width: 100%;"
                v-model="scope.row.invoiceNo"
                @change="val => dataOnChange('invoiceNo', val, scope)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="invoiceAmount" label="发票金额" width="200px">
            <template slot-scope="scope, index">
              <el-input-number
                style="width: 100%;"
                :precision="2"
                :min="0"
                :controls="false"
                v-model="scope.row.invoiceAmount"
                @change="val => dataOnChange('invoiceNo', val, scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="invoiceTime" label="开票日期" width="200px">
            <template slot-scope="scope">
              <el-date-picker
                style="width: 100%;"
                v-model="scope.row.invoiceTime"
                type="date"
                value-format="yyyy-MM-dd"
                @change="val => dataOnChange('invoiceTime', val, scope.row)"
              >
              </el-date-picker>
            </template>
          </el-table-column>
          <el-table-column v-if="this.flag != 2" prop="invoiceNo" label="附件" width="200px">
            <template slot-scope="scope">
              <el-upload
                :action="uploadFileUrl"
                :file-list="scope.row.fileUrl ? [{name: scope.row.fileName, url: scope.row.fileUrl}] : []"
                :on-error="handleUploadError"
                :on-success="(res, file, fileList)=>handleUploadSuccess(res, file, fileList, scope)"
                :on-remove="(file, fileList)=>handleRemove(file, fileList, scope)"
                :on-preview="onPreview"
                :show-file-list="true"
                :headers="headers"
                class="upload-file-uploader"
                ref="fileUpload"
              >
                <el-button :disabled="!!scope.row.fileUrl" size="small" type="default">点击上传</el-button>
              </el-upload>
            </template>
          </el-table-column>
          <el-table-column v-else prop="invoiceNo" label="附件" width="200px">
            <template slot-scope="scope">
              <a :href="scope.row.fileUrl" target="_blank">{{ scope.row.fileName }}</a>
            </template>
          </el-table-column>
          <el-table-column v-if="this.flag != 2" prop="id" label="操作" width="100px">
            <template slot-scope="scope">
              <a @click="()=>handleDel(scope)">删除</a>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <span slot="footer" class="dialog-footer">
      <el-button v-if="this.flag != 2" type="primary" size="small" :loading="saveFlag" @click="saveForm">保存</el-button>
      <el-button size="small" @click="cleanForm">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {v4 as uuidv4} from 'uuid';
import { getToken } from "@/utils/auth";
import {queryInvoiceDetail} from "@/api/finance/invoice"
export default {
  name: "addDialog",
  data() {
    return {
      uploadFileUrl: `${process.env.VUE_APP_BASE_API}/file/upload`, // 上传文件服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      visible: false,
      saveFlag: false,
      tableData: [],
      invoiceId: '',
      flag: '',
    }
  },
  methods: {
    onPreview(file) {
      window.open(file.url)
    },
    saveForm() {
      if (this.saveFlag) {
        return;
      }
      this.saveFlag = true;
      if (this.tableData.length > 0) {
        const flag = this.tableData.every(item => item.invoiceNo && item.invoiceAmount && item.invoiceTime && item.fileUrl)
        if (flag) {
          // console.log('??????????',this.tableData)
          this.$emit('uploadInvoice', {
            invoiceId: this.invoiceId,
            invoiceAttachments: this.tableData
          })
        } else {
          this.$message({
            type: "error",
            message: "请完善发票信息",
          });
          this.saveFlag = false;
          return;
        }
      } else {
        this.$message({
          type: "error",
          message: "请完善发票信息",
        });
        this.saveFlag = false;
        return;
      }
    },
    handleRemove(file, fileList, scope) {
      const index = scope.$index;
      this.tableData = this.tableData.map((item, index) => {
        if (index == scope.$index) {
          return {
            ...item,
            fileUrl: '',
            fileName: '',
          }
        } else {
          return item
        }
      })
    },
    // 上传成功回调
    handleUploadSuccess(res, file, fileList, scope) {
      // console.log('??????????',scope)
      if (res.status === 'done') {
        this.tableData = this.tableData.map((item, index) => {
          if (index == scope.$index) {
            // console.log('xxxxx',item)
            return {
              ...item,
              fileUrl: res.name,
              fileName: file.name,
            }
          } else {
            return item
          }
        })
        // this.uploadList.push({ name: file.name, url: res.name });
        // this.uploadedSuccessfully();
      } else {
        // this.number--;
        // this.$modal.closeLoading();
        // this.$modal.msgError(res.msg);
        // this.$refs.fileUpload.handleRemove(file);
        // this.uploadedSuccessfully();
      }
    },
    // 上传失败
    handleUploadError(err) {
      this.$modal.msgError("上传文件失败，请重试");
      this.$modal.closeLoading();
    },
    init(id, flag) {
      this.invoiceId = id;
      this.visible = true;
      this.flag = flag;
      if (flag != 0) {
        queryInvoiceDetail({id}).then(r => {
          if (r.code === 200) {
            const {attachments} = r.data;
            this.tableData = attachments
          }
        })
      }
    },
    cleanForm() {
      this.visible = false;
      this.saveFlag = false;
      this.tableData = [];
    },
    dataOnChange(key, val, row) {
      // console.log('XXXXXX', val, row)
      this.tableData = this.tableData.map((item, index) => {
        if (index == row.$index) {
          return {
            ...item,
            [key]: val,
          }
        } else {
          return item
        }
      })
      // console.log('>>>>>>>>>', this.tableData)
    },
    handleDel(row){
      this.tableData = this.tableData.filter((item, index) => {
        return index != row.$index
      })
    },
    handleAdd(){
      this.tableData.push({})
    },
  }
}
</script>

<style scoped lang="scss">
.upload-file-uploader {
  //::v-deep .el-upload {
  //  display: none;
  //}
}
</style>
