<template>
  <div class="app-container">

    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="订单编号">
        <el-input v-model="queryParams.orderNo"></el-input>
      </el-form-item>
      <el-form-item label="开票时间">
        <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="发票类型">
        <el-select v-model="queryParams.invoiceType" placeholder="发票类型" clearable style="width: 240px;">
          <el-option v-for="(item, index) of typearr" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="queryParams.invoiceStatus" placeholder="状态" clearable style="width: 240px;">
          <el-option v-for="(item, index) of typearr2" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
         <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
      <el-form-item class="fr">
        <el-button @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="roleList" @selection-change="handleSelectionChange">
      <el-table-column label="开票时间" prop="createTime" width="170">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单号" prop="orderNo" :show-overflow-tooltip="true" width="220"/>
      <el-table-column label="会员" prop="invoiceUserId" width="230">
        <template slot-scope="scope">
          <div class="span_all" v-if="scope.row.invoiceUserId">
            <div class="span_left">
              <el-image
                v-if="scope.row.invoiceHeadPortrait"
                :src="scope.row.invoiceHeadPortrait"
                fit="fill"
              ></el-image>
              <img
                v-else
                src="@/assets/images/member.png"
                alt="donate"
                width="100%"
              />
            </div>
            <div class="span_right">
              <div>{{ scope.row.invoiceNickName }}</div>
              <div>
                {{ scope.row.invoiceAccountNumber }} 【{{ scope.row.invoiceUserName }}】
              </div>
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="实付金额" prop="allAmount" class-name="small-padding fixed-width"/>
      <el-table-column label="发票类型" prop="invoiceTypeName" class-name="small-padding fixed-width" width="200"/>
      <el-table-column label="发票抬头" prop="companyName" width="170">
<!--        <template slot-scope="scope">
          <span>{{ scope.row.riseType == 1 ? '个人' : scope.row.companyName }}</span>
        </template>-->
      </el-table-column>
      <el-table-column label="纳税人识别号" prop="taxNum" width="170">
        <template slot-scope="scope">
          <span>{{ scope.row.taxNum || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开票状态" prop="statusName" class-name="small-padding fixed-width"/>
      <el-table-column prop="name" label="操作" width="120" fixed="right">
        <template slot-scope="{ row }">
          <el-button
            type="text"
            v-if="row.status == 1"
            @click="handleAddDialog(row.id, 0)"
          >
            上传发票
          </el-button>

          <el-button
            type="text"
            v-if="row.status != 1"
            @click="handleAddDialog(row.id, 1)"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            v-if="row.status != 1"
            @click="handleAddDialog(row.id, 2)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.current" :limit.sync="queryParams.pageSize"
                  @pagination="getList"/>
    </div>
    <AddDialog ref="addDialog" @uploadInvoice="uploadInvoice"></AddDialog>
  </div>
</template>

<script>
import {queryInvoicePage, uploadInvoice} from "@/api/finance/invoice";
import AddDialog from "./components/addDialog.vue";

export default {
  name: "Invoice",
  dicts: ['sys_normal_disable'],
  components: {
    AddDialog
  },
  data() {
    return {
      typearr: [
        { value: 1, label: "增值税专用发票" },
        { value: 2, label: "增值税普通发票" },
      ],
      typearr2: [
        // { value: 0, label: "待审核" },
        { value: 1, label: "待开票" },
        { value: 2, label: "已开票" },
        // { value: 3, label: "已驳回" },
        // { value: 4, label: "已取消" },
      ],
      grossamount: '',
      // 搜索
      queryForm: {},
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      menuExpand: false,
      menuNodeAll: false,
      deptExpand: true,
      deptNodeAll: false,
      // 日期范围
      dateRange: [],
      // 菜单列表
      menuOptions: [],
      // 部门列表
      deptOptions: [],

      // 查询参数
      queryParams: {
        current: 1,
        pageSize: 10,
        createStartTime: undefined,
        createEndTime: undefined,
        orderNo: undefined,
        invoiceStatus: undefined,
        invoiceType: undefined,
        // roleName: undefined,
        // roleKey: undefined,
        // status: undefined
      },
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 表单校验
      rules: {
        roleName: [
          {required: true, message: "角色名称不能为空", trigger: "blur"}
        ],
        roleKey: [
          {required: true, message: "权限字符不能为空", trigger: "blur"}
        ],
        roleSort: [
          {required: true, message: "角色顺序不能为空", trigger: "blur"}
        ]
      }
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    uploadInvoice(tableData) {
      uploadInvoice(tableData).then(response => {
        this.$modal.msgSuccess("上传成功");
        this.$refs.addDialog.cleanForm()
        this.getList();
      }).finally(() => {
        this.$refs.addDialog.saveFlag = false
      });
    },
    handleAddDialog(id, flag) {
      this.$refs.addDialog.init(id, flag)
    },
    getList() {
      if (this.dateRange == null) {
        queryInvoicePage(this.queryParams).then(response => {
          this.roleList = response.records;
          this.total = response.total;
          this.loading = false;
        }).catch((err) => {
          console.log(err)
        });
      } else {
        const startDate = this.dateRange[0] ? this.dateRange[0] + ' 00:00:00' : undefined;
        const endDate = this.dateRange[1] ? this.dateRange[1] + ' 23:59:59' : undefined;

        queryInvoicePage({
          ...this.queryParams,
          createStartTime: startDate,
          createEndTime: endDate
        }).then(response => {
          this.roleList = response.records;
          this.total = response.total;
          this.loading = false;
        }).catch((err) => {
          console.log(err)
        });
      }
    },


    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮（数据权限）
    cancelDataScope() {
      this.openDataScope = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      this.menuExpand = false,
        this.menuNodeAll = false,
        this.deptExpand = true,
        this.deptNodeAll = false,
        this.form = {
          roleId: undefined,
          roleName: undefined,
          roleKey: undefined,
          roleSort: 0,
          status: "0",
          menuIds: [],
          deptIds: [],
          menuCheckStrictly: true,
          deptCheckStrictly: true,
          remark: undefined
        };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1; // 将current重置为1
      this.queryParams.pageSize = 10;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams = this.$options.data().queryParams;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.roleId)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleDataScope":
          this.handleDataScope(row);
          break;
        case "handleAuthUser":
          this.handleAuthUser(row);
          break;
        default:
          break;
      }
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getMenuTreeselect();
      this.open = true;
      this.title = "添加角色";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const roleId = row.roleId || this.ids
      const roleMenu = this.getRoleMenuTreeselect(roleId);
      getRole(roleId).then(response => {
        this.form = response.data;
        this.open = true;
        this.$nextTick(() => {
          roleMenu.then(res => {
            let checkedKeys = res.checkedKeys
            checkedKeys.forEach((v) => {
              this.$nextTick(() => {
                this.$refs.menu.setChecked(v, true, false);
              })
            })
          });
        });
        this.title = "修改角色";
      });
    },


    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.roleId != undefined) {
            this.form.menuIds = this.getMenuAllCheckedKeys();
            updateRole(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.form.menuIds = this.getMenuAllCheckedKeys();
            addRole(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 提交按钮（数据权限） */
    submitDataScope: function () {
      if (this.form.roleId != undefined) {
        this.form.deptIds = this.getDeptAllCheckedKeys();
        dataScope(this.form).then(response => {
          this.$modal.msgSuccess("修改成功");
          this.openDataScope = false;
          this.getList();
        });
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const roleIds = row.roleId || this.ids;
      this.$modal.confirm('是否确认删除角色编号为"' + roleIds + '"的数据项？').then(function () {
        return delRole(roleIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      const startDate = this.dateRange[0] ? this.dateRange[0] + ' 00:00:00' : undefined;
      const endDate = this.dateRange[1] ? this.dateRange[1] + ' 23:59:59' : undefined;
      this.download('/invoice/export', {
        ...this.queryParams,
        createStartTime: startDate,
        createEndTime: endDate
      }, `发票.xlsx`)
    },

    handlePayouts(){
      this.$refs.PayoutsDialog.init()
    }
  }
};
</script>
