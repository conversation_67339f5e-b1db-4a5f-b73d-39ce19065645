<!--
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-03-22 13:13:22
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-18 15:15:58
-->
<template>
  <div class="">
    <el-form :model="form" ref="form">
      <el-row>
        <el-col :span="8">
          <el-form-item label="下单日期" label-width="110px">
            <el-date-picker
              style="width: 100%;"
              v-model="time"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="changeTime"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="凭证代码" label-width="80px" prop="lotCode">
            <el-input v-model="form.lotCode"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单号" label-width="60px" prop="orderNo">
            <el-input v-model="form.orderNo"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item
            label="买家ID/昵称/手机号/姓名"
            label-width="180px"
            prop="buyerUserName"
          >
            <el-input v-model="form.buyerUserName"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="卖家ID/昵称/手机号/姓名"
            label-width="180px"
            prop="userName"
          >
            <el-input v-model="form.userName"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="卖家所属机构ID/昵称/手机号/姓名"
            label-width="220px"
            prop="sellReferrerKeyword"
          >
            <el-input v-model="form.sellReferrerKeyword"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="类型" label-width="110px" prop="type">
            <el-select
              v-model="form.tradeTypeList"
              multiple
              clearable
              placeholder="请选择类型"
              style="width: 100%;"
            >
              <el-option
                v-for="dict in typearr"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="状态" label-width="80px" prop="tradeStatusList">
            <el-select
              multiple
              v-model="form.tradeStatusList"
              placeholder="请选择状态"
              style="width: 100%;"
            >
              <el-option
                v-for="dict in dict.type.order_trade_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="所属项目" label-width="82px" prop="projectName">
            <el-input v-model="form.projectName"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="" label-width="15px">
            <el-button @click="reset"> 重置</el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div>
      <div class="addRight" v-if="roleId != 8">
        <el-button @click="exportFile">导出</el-button>
      </div>
      <div class="toClear"></div>
      <div class="numberStyle">
        总数量:
        <span style="color: red;">{{ orderParams.orderCount }}</span> 总额:
        <span style="color: red;">¥ {{ orderParams.orderAmount }} </span>
      </div>
      <el-table :data="tableData" style="width: 100%;" border v-loading="loading">
        <el-table-column prop="orderNo" label="单号" min-width="130">
        </el-table-column>
        <el-table-column prop="goodOrderId" label="实物订单号" min-width="130">
          <template slot-scope="scope">
            {{ scope.row.goodOrderId || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="buyerName" label="买家" width="280">
          <template slot-scope="scope">
            <div v-if="scope.row.buyerUserId" class="span_all cursor-pointer" @click="jumpMemberCenter(scope.row.buyerUserId)">
              <div class="span_left">
                <el-image
                  v-if="scope.row.buyerUserImg"
                  :src="scope.row.buyerUserImg"
                  fit="fill"
                ></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.buyerNickName }} <span>({{ scope.row.buyerUserId}})</span></div>
                <div>{{ scope.row.buyerUserName }} 【{{ scope.row.buyerRealName }}】</div>
              </div>
            </div>
            <div>-</div>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="卖家" width="280">
          <template slot-scope="scope">
            <div class="span_all cursor-pointer" @click="jumpMemberCenter(scope.row.userId)">
              <div class="span_left">
                <el-image
                  v-if="scope.row.userImg"
                  :src="scope.row.userImg"
                  fit="fill"
                ></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.nickName }} <span>({{ scope.row.userId}})</span></div>
                <div>{{ scope.row.userName }} 【{{ scope.row.realName }}】</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="凭证" min-width="200">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image :src="scope.row.lotImg" fit="fill"></el-image>
              </div>
              <div class="span_right">
                <div>{{ scope.row.lotName }}</div>
                <div>【{{ scope.row.lotCode }}】</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="projectName" label="所属项目" width="200"/>
        <el-table-column prop="lotCount" label="数量" width="110">
        </el-table-column>
        <el-table-column prop="price" label="价格" min-width="80">
        </el-table-column>
        <el-table-column prop="tradeTypeDesc" label="类型" width="110">
        </el-table-column>
        <el-table-column prop="orderTypeDesc" label="资产" width="110">
          <!--          <template slot-scope="scope">
                      <span v-if="scope.row.lotType === 1"> 竞拍凭证</span>
                      <span v-if="scope.row.lotType === 2"> 赠送凭证</span>
                      <span v-if="scope.row.lotType === 3"> 收购凭证</span>
                      <span v-if="scope.row.lotType === 4"> 可用凭证</span>
                      <span v-if="scope.row.lotType === 5"> 预约销售</span>
                    </template>-->
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="105">
        </el-table-column>
        <el-table-column prop="roundTimeDesc" label="轮次" width="70">
        </el-table-column>
        <el-table-column prop="tradeTime" label="交易时间" width="105">
        </el-table-column>
        <el-table-column prop="tradeStatus" label="状态" width="80">
          <template slot-scope="{ row }">
            <span>{{ ((dict.type.order_trade_status || []).find(v => v.value == row.tradeStatus) || {}).label }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80" fixed="right" v-if="noLimit">
          <template slot-scope="{ row }">
            <el-button v-if="![2, 3, 5].includes(row.tradeStatus)" type="text" @click="handleCancel(row)">
              取消
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="form.current"
        :limit.sync="form.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>
<script>
  import {
    pageList,
    orderCountParams, lotOrderCancel,
  } from '@/api/transferAssets/tradingMarket.js'
  import store from "@/store";
  import JumpMemberCenter from "@/layout/mixin/JumpMemberCenter";
  import {mapGetters} from 'vuex'
  import dayjs from 'dayjs'

  export default {
    name: "LotOrder",
    dicts: ["order_trade_status", "eazy_lot_type"],
    mixins: [JumpMemberCenter],
    data() {
      // 计算近一周的开始、结束时间
      const end = dayjs();
      const start = dayjs().subtract(6, 'day');
      return {
        form: {
          current: 1,
          pageSize: 10,
          lotCode: "",
          userName: "",
          orderNo: "",
          tradeStatusList: [],
          tradeTypeList: [],
          // 默认近一周
          createDateStart: start.format("YYYY-MM-DD 00:00:00"),
          createDateEnd: end.format("YYYY-MM-DD 23:59:59"),
          buyerUserName: '',
          sellReferrerKeyword: '',
          projectName: '',
        },
        time: [start.toDate(), end.toDate()],
        resetForm: {},
        tableData: [],
        orderParams: {},
        total: 0,
        typearr: [
          {value: 1, label: "自主销售"},
          {value: 2, label: "服务中心"},
          {value: 3, label: "商家"},
          {value: 5, label: "配货增资"},
        ],
        roleId: store.getters.roleId,
        loading: false,
      };
    },
    props: {
      autoLoad: {
        type: Boolean,
        default: true,
      },
    },
    computed: {
      ...mapGetters(['noLimit']),
    },
    created() {
      this.resetForm = {
        ...this.form,
      };
      // 当 autoLoad === true 时才默认加载数据
      if (this.autoLoad) {
        this.getData();
        this.getOrder();
      }
    },
    methods: {
      onSearch() {
        this.getData();
        this.getOrder();
      },
      // 获取数据
      getData() {
        const params = {
          ...this.form,
          current: 1,
          pageSize: 10,
        };
        this.loading = true;
        pageList(params).then((res) => {
          this.tableData = res.records;
          this.total = res.total;
          this.form.current = 1;
          this.form.pageSize = 10;
        }).finally(() => {
          this.loading = false;
        });
      },

      // 获取订单
      getOrder() {
        const params = {
          ...this.form,
        };
        orderCountParams(params).then((res) => {
          console.log(res, 3333);
          this.orderParams = res.data;
        });
      },

      // 重置
      reset() {
        this.form = {
          ...this.resetForm,
        };
        const end = dayjs();
        const start = dayjs().subtract(6, 'day');
        this.time = [start.toDate(), end.toDate()];
        this.form.createDateStart = start.format("YYYY-MM-DD 00:00:00");
        this.form.createDateEnd = end.format("YYYY-MM-DD 23:59:59");
        this.getData();
        this.getOrder();
      },

      // 分页 直接按queryForm 开始查询
      getList() {
        this.loading = true;
        const params = {
          ...this.form,
        };
        pageList(params).then((res) => {
          this.tableData = res.records;
          this.total = res.total;
        }).finally(() => {
          this.loading = false;
        });
      },

      // 导出
      exportFile() {
        const params = {
          ...this.form,
        };
        this.download(
          "/statistical/export-order-list",
          {...params},
          `交易市场.xlsx`
        );
      },

      // 改变时间
      changeTime(time) {
        this.form.createDateStart = !time ? '' : this.$dayjs(time[0]).format(
          "YYYY-MM-DD 00:00:00"
        );
        this.form.createDateEnd = !time ? '' : this.$dayjs(time[1]).format(
          "YYYY-MM-DD 23:59:59"
        );
      },

      handleCancel(row) {
        this.$confirm("确定取消吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          await lotOrderCancel({id: row.id})
          this.getList();
          this.getOrder();
          this.$message({
            type: "success",
            message: "取消成功!",
          });
        });
      },
    },
  };
</script>
<style scoped lang="scss">
  .addRight {
    float: right;
    margin: 10px 0;
  }

  .toClear {
    clear: both;
  }

  .numberStyle {
    background: #e7f8ff;
    height: 40px;
    line-height: 40px;
    padding-left: 10px;
    border: 1px solid #e3f4ff;
    margin-bottom: 10px;
  }
</style>
