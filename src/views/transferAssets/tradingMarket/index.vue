<!-- 会员账户 -->


<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick" class="whole">
      <el-tab-pane label="实物订单" name="goods" lazy>
        <GoodsOrder ref="goods" readOnly :autoLoad="false" />
      </el-tab-pane>
      <el-tab-pane label="凭证订单" name="lot" lazy>
        <LotOrder ref="lot" :autoLoad="false" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>

import LotOrder from "@/views/transferAssets/tradingMarket/LotOrder.vue";
import GoodsOrder from "@/views/order/list.vue";

export default {
  name: "TradingMarket",
  components: {
    GoodsOrder,
    LotOrder
  },
  data() {
    return {
      activeName: 'goods'
    };
  },
  methods: {
    handleClick(tab, event) {
    }
  }
};
</script>
