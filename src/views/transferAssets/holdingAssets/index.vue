<template>
  <div class="whole">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="持仓资产" name="first" lazy>
        <one-page></one-page>
      </el-tab-pane>
      <el-tab-pane label="固定凭证" name="second" lazy>
        <common-asset-page :lotType="1"></common-asset-page>
      </el-tab-pane>
      <el-tab-pane label="赠送凭证" name="third" lazy>
        <common-asset-page :lotType="2"></common-asset-page>
      </el-tab-pane>
      <el-tab-pane label="活动凭证" name="seven" lazy>
        <common-asset-page :lotType="6"></common-asset-page>
      </el-tab-pane>
      <el-tab-pane label="收购凭证" name="fourth" lazy>
        <common-asset-page :lotType="3"></common-asset-page>
      </el-tab-pane>
      <el-tab-pane label="可用凭证" name="fifth" lazy>
        <common-asset-page :lotType="4"></common-asset-page>
      </el-tab-pane>
      <el-tab-pane label="预约销售" name="sixth" lazy>
        <common-asset-page :lotType="5"></common-asset-page>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import OnePage from "./onePage.vue";
import CommonAssetPage from "./components/CommonAssetPage.vue";

export default {
  name: "HoldingAssets",
  components: {
    OnePage,
    CommonAssetPage
  },
  data() {
    return {
      form: {
        pageNum: 1,
        pageSize: 10,
        id: "",
      },
      activeName: "first",
      total: 0,
      time: "",
      resetForm: {},
      tableData: [],
    };
  },

  methods: {
    handleClick(tab, event) {
      this.activeName = tab.name
    },
  },
};
</script>
<style scoped lang="scss">
</style>
