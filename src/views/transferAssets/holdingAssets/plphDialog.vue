<!--
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-03-25 08:34:09
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-18 13:26:24
-->
<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="30%"
    center
    :modal-append-to-body="false"
    :before-close="cleanForm"
    :modal="false"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div style="margin: 10px;">
      配货明细 <el-button type="text" @click="exportFile">模版下载</el-button>
    </div>

    <el-upload
      ref="upload"
      :action="upLoadUrl"
      class="upload-demo"
      :on-preview="handlePreview"
      :limit="1"
      :on-exceed="handleExceed"
      :file-list="fileList"
      :auto-upload="false"
      :on-success="handleSuccess"
    >
      <el-button size="small" type="primary">点击上传</el-button>
      <div slot="tip" class="el-upload__tip">
        请上传正确格式的文件
      </div>
    </el-upload>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" size="small" :loading="saveFlag" @click="saveForm">确 定</el-button>
      <el-button size="small" @click="cleanForm">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "PlphDialog",
  data() {
    return {
      upLoadUrl: process.env.VUE_APP_BASE_API + "/lot/importData",
      dialogVisible: false,
      title: "批量配货",
      fileList: [],
      saveFlag: false,
    };
  },

  methods: {
    init() {
      this.dialogVisible = true;
    },

    // exportFile() {
    //   this.download(
    //     "https://cdn-newteapai.eazytec-cloud.com/peihuo.xlsx",
    //     "",
    //     `配货明细表.xlsx`
    //   );
    // },

    exportFile() {
      window.open(
        "https://cdn-newteapai.eazytec-cloud.com/peihuo.xlsx"
      );
    },

    handlePreview(file) {
      console.log(file);
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          files.length + fileList.length
        } 个文件`
      );
    },

    cleanForm() {
      this.dialogVisible = false;
    },
    handleSuccess(response, file, fileList) {
      console.log(response);
      if( response.code != 200 ){
        this.$message.error(response.msg);
      } else {
        this.$emit("getData");
        this.cleanForm();
      }
      this.saveFlag = false
    },
    saveForm: async function () {
      if (this.saveFlag) {
        return;
      }
      this.saveFlag = true;
      await this.$refs?.upload?.submit() // 手动触发文件上传
      this.saveFlag = false;
    },
  },
};
</script>

<style></style>
