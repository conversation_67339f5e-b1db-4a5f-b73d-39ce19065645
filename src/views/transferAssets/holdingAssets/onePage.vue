<!--
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-03-22 13:13:22
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-14 19:58:51
-->
<template>
  <div class="whole">
    <el-form :model="form" ref="form">
      <el-row>
        <el-col :span="8">
          <el-form-item label="凭证代码" label-width="110px" prop="lotCode">
            <el-input v-model="form.lotCode"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="会员ID/昵称/手机号/姓名"
            label-width="180px"
            prop="userName"
          >
            <el-input v-model="form.userName"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="服务中心" label-width="110px" prop="serviceUser">
            <el-select
              v-model="form.serviceUser"
              placeholder="请选择服务中心"
              filterable
              style="width: 100%;"
            >
              <el-option
                v-for="item in userList.filter(v => v.roleId === 2)"
                :key="item.userId"
                :label="item.userName"
                :value="item.userId"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <!--<el-col :span="8">-->
          <!--<el-form-item label="发生时间" label-width="110px">-->
            <!--<el-date-picker-->
              <!--style="width: 100%;"-->
              <!--v-model="time"-->
              <!--type="daterange"-->
              <!--start-placeholder="开始日期"-->
              <!--end-placeholder="结束日期"-->
              <!--@change="changeTime"-->
            <!--&gt;-->
            <!--</el-date-picker>-->
          <!--</el-form-item>-->
        <!--</el-col>-->

        <el-col :span="8">
          <el-form-item label="" label-width="43px">
            <el-button @click="reset"> 重置 </el-button>
            <el-button type="primary" @click="getData">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div>
      <div class="addRight" v-if="roleId != 8">
        <el-button @click="exportFile">导出</el-button>
        <el-button type="primary" @click="openPh">配货</el-button>
        <el-button type="primary" @click="openPlph">批量配货</el-button>
      </div>

      <el-table :data="tableData" style="width: 100%;" border v-loading="loading">
        <el-table-column prop="date" label="会员" width="280">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image v-if="scope.row.avatar" :src="scope.row.avatar" fit="fill"></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.nickName }} <span>({{ scope.row.userId}})</span></div>
                <div>{{ scope.row.userName }} 【{{ scope.row.realName }}】</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="凭证" min-width="150">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image :src="scope.row.lotImg" fit="fill"></el-image>
              </div>
              <div class="span_right">
                <div>{{ scope.row.lotName }}</div>
                <div>【{{ scope.row.lotCode }}】</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="auctionCount" label="固定凭证" min-width="100">
        </el-table-column>
        <el-table-column prop="giveSaleCount" label="赠送凭证" min-width="100">
        </el-table-column>
        <el-table-column prop="giveActivityCount" label="活动凭证" min-width="100">
        </el-table-column>
        <el-table-column prop="wholesaleCount" label="收购凭证" min-width="100">
        </el-table-column>
        <el-table-column prop="saleCount" label="可用凭证" min-width="100">
        </el-table-column>
        <el-table-column prop="bookSaleCount" label="预约销售" min-width="100">
        </el-table-column>
        <el-table-column prop="worth" label="总资产" min-width="100">
        </el-table-column>
        <el-table-column prop="buyAmount" label="总买入额" min-width="100">
        </el-table-column>
        <el-table-column prop="saleAmount" label="总卖出额" min-width="100">
        </el-table-column>
        <el-table-column prop="multiple" label="收益倍数" min-width="100">
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="form.current"
        :limit.sync="form.pageSize"
        @pagination="getList"
      />
    </div>
    <ph-dialog ref="ph" @getData="getList" :userList="userList"></ph-dialog>
    <plph-dialog ref="plph" @getData="getList"></plph-dialog>
  </div>
</template>
<script>
import store from "@/store";
import { queryList } from "@/api/basis/basis.js";
import {logPageListAll, logPageListAllV2} from '@/api/transferAssets/holdingAssets.js'
import PhDialog from "./phDialog.vue";
import PlphDialog from "./plphDialog.vue";
export default {
  name: "OnePage",
  components: {
    PhDialog,
    PlphDialog,
  },
  data() {
    return {
      form: {
        current: 1,
        pageSize: 10,
        createDateEnd: "",
        createDateStart: "",
        lotCode: "",
        userName: "",
        serviceUser: "",
      },
      time: "",
      resetForm: {},
      tableData: [],
      userList: [],
      total: 0,
      src: "",
      roleId: store.getters.roleId,
      loading: false,
    };
  },
  created() {
    this.resetForm = {
      ...this.form,
    };
    this.getUser();
  },

  mounted() {
  },
  methods: {
    // 导出
    exportFile() {
      const params = {
        lotCode: this.form.lotCode,
        userName: this.form.userName,
        serviceUser: this.form.serviceUser,
      };
      this.download("/lot/export-log-list-all", { ...params }, `持仓资产.xlsx`);
    },
    getUser() {
      queryList().then((res) => {
        this.userList = res.data;
      });
    },

    // 获取数据
    getData() {
      this.form.current = 1;
      this.form.pageSize = 10;
      this.getList();
    },

    // 重置
    reset() {
      this.form = {
        ...this.resetForm,
      };
      this.time = "";
      this.getData();
    },

    // 改变时间
    changeTime(time) {
      this.form.createDateStart = this.$dayjs(time[0]).format(
        "YYYY-MM-DD 00:00:00"
      );
      this.form.createDateEnd = this.$dayjs(time[1]).format(
        "YYYY-MM-DD 23:59:59"
      );
    },

    // 分页 直接按queryForm 开始查询
    getList() {
      this.loading = true;
      (this.$isProd ? logPageListAll : logPageListAllV2)(this.form).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
      }).finally(() => {
        this.loading = false;
      });
    },

    openPh() {
      this.$refs.ph.init();
    },

    openPlph() {
      this.$refs.plph.init();
    },
  },
};
</script>
<style scoped lang="scss">
.addRight {
  float: right;
  margin: 10px 0;
}
.toClear {
  clear: both;
}
.numberStyle {
  background: #e7f8ff;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  border: 1px solid #e3f4ff;
  margin-bottom: 10px;
}
</style>
