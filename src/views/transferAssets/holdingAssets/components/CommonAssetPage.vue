<template>
  <div class="whole">
    <el-form :model="form" ref="form">
      <el-row>
        <el-col :span="8">
          <el-form-item label="发生时间" label-width="110px">
            <el-date-picker
              style="width: 100%;"
              v-model="time"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="changeTime"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="凭证代码" label-width="110px" prop="lotCode">
            <el-input v-model="form.lotCode"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="会员ID/昵称/手机号/姓名"
            label-width="180px"
            prop="userName"
          >
            <el-input v-model="form.userName"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="单号" label-width="110px" prop="serialNo">
            <el-input v-model="form.serialNo"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="showTypeSelect">
          <el-form-item label="类型" label-width="110px" prop="operateType">
            <el-select
              v-model="form.operateType"
              placeholder="请选择类型"
              style="width: 100%;"
            >
              <el-option
                v-for="dict in dict.type[typeDict]"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="" label-width="15px">
            <el-button @click="reset"> 重置 </el-button>
            <el-button type="primary" @click="getData">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div>
      <div class="addRight" v-if="roleId != 8">
        <el-button @click="exportFile">导出</el-button>
      </div>
      <div class="toClear"></div>
      <div class="numberStyle">
        持仓数量:
        <span style="color: red;">{{ number }}</span>
      </div>
      <el-table :data="tableData" style="width: 100%;" border v-loading="loading">
        <el-table-column prop="createTime" label="发生时间" min-width="100">
        </el-table-column>
        <el-table-column prop="orderNo" label="单号" min-width="100">
        </el-table-column>
        <el-table-column prop="date" label="会员" min-width="280">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image
                  v-if="scope.row.userInfo.avatar"
                  :src="scope.row.userInfo.avatar"
                  fit="fill"
                ></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.userInfo.nickName }} <span>({{ scope.row.userInfo.userId}})</span></div>
                <div>
                  {{ scope.row.userInfo.userName }} 【{{
                    scope.row.userInfo.realName
                  }}】
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="凭证" min-width="150">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image :src="scope.row.lotInfo.lotImg" fit="fill"></el-image>
              </div>
              <div class="span_right">
                <div>{{ scope.row.lotInfo.lotName }}</div>
                <div>【{{ scope.row.lotInfo.lotCode }}】</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="lotCount" label="数量" min-width="100">
          <template #default="scope">
            {{ scope.row.type === 2 && scope.row.lotCount !== 0 ? renderNewLotCount(scope.row.lotCount) : scope.row.lotCount }}
          </template>
        </el-table-column>
        <el-table-column prop="lotPrice" label="价格" min-width="100">
        </el-table-column>
        <el-table-column prop="operateTypeDesc" label="类型" min-width="100">
        </el-table-column>
        <el-table-column prop="roundTime" label="轮次" min-width="100">
        </el-table-column>
        <el-table-column prop="orderDesc" label="说明" min-width="100">
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="form.current"
        :limit.sync="form.pageSize"
        @pagination="getData(false)"
      />
    </div>
  </div>
</template>
<script>
import {logPageCount, logPageList, logPageListV2} from '@/api/transferAssets/holdingAssets.js'
import store from "@/store";
import { multiply } from "lodash";

const TYPE_DICT_MAP = {
  1: 'auction_item_type',
  2: 'gift_item_type',
  3: 'wholesale_item_type',
  4: 'can_sale_item_type',
  5: 'appointment_item_type',
  6: 'gift_item_type'
}

const EXPORT_FILE_NAME_MAP = {
  1: '固定凭证',
  2: '赠送凭证',
  3: '收购凭证',
  4: '可用凭证',
  5: '预约销售',
  6: '活动凭证'
}

export default {
  name: "CommonAssetPage",
  dicts: {
    type: function() {
      return [TYPE_DICT_MAP[this.lotType]]
    }
  },
  props: {
    lotType: {
      type: Number,
      required: true,
      validator: function(value) {
        return [1, 2, 3, 4, 5, 6].indexOf(value) !== -1
      }
    }
  },
  data() {
    return {
      form: {
        current: 1,
        pageSize: 10,
        lotCode: "",
        userName: "",
        serialNo: "",
        operateType: "",
        createDateStart: "",
        createDateEnd: "",
        lotType: this.lotType,
      },
      resetForm: {},
      time: "",
      tableData: [],
      number: "",
      total: 0,
      roleId: store.getters.roleId,
      loading: false,
    };
  },
  computed: {
    typeDict() {
      return TYPE_DICT_MAP[this.lotType]
    },
    showTypeSelect() {
      return this.lotType !== 6
    }
  },
  created() {
    // 设置默认的时间范围为近一周
    this.setDefaultDateRange();
    // 记录初始表单，用于后续重置
    this.resetForm = {
      ...this.form,
    };
    // 手动初始化字典
    this.dict.init([TYPE_DICT_MAP[this.lotType]]);
    // 移除页面初始化时的接口调用，改为用户主动查询
  },
  mounted() {
  },
  methods: {
    // 新增: 设置默认时间范围为近一周（含今天）
    setDefaultDateRange() {
      const end = this.$dayjs();
      const start = this.$dayjs().subtract(6, 'day');
      this.time = [start.toDate(), end.toDate()];
      this.form.createDateStart = start.format('YYYY-MM-DD 00:00:00');
      this.form.createDateEnd = end.format('YYYY-MM-DD 23:59:59');
    },
    renderNewLotCount(lotCount) {
      return multiply(-1, lotCount)
    },
    async getData(isReset = false) {
      try {
        if(isReset) {
          this.form.current = 1;
          this.form.pageSize = 10;
        }
        this.loading = true;
        const params = {
          ...this.form,
        };

        const listRes = await logPageListV2(params);
        this.tableData = listRes.data?.records;
        this.total = listRes.data?.total;

        if(isReset) {
          const countRes = await logPageCount(params);
          this.number = countRes.data;
        }
      } catch(error) {
        console.error('获取数据失败:', error);
      } finally {
        this.loading = false;
      }
    },

    reset() {
      this.form = {
        ...this.resetForm,
      };
      // 恢复默认时间范围为近一周
      this.setDefaultDateRange();
      this.getData(true);
    },

    exportFile() {
      const params = {
        lotCode: this.form.lotCode,
        userName: this.form.userName,
        serialNo: this.form.serialNo,
        operateType: this.form.operateType,
        createDateStart: this.form.createDateStart,
        createDateEnd: this.form.createDateEnd,
        lotType: this.lotType,
      };
      this.download("/lot/export-log", { ...params }, `${EXPORT_FILE_NAME_MAP[this.lotType]}.xlsx`);
    },

    changeTime(time) {
      if (time && time.length === 2) {
        this.form.createDateStart = this.$dayjs(time[0]).format(
          "YYYY-MM-DD 00:00:00"
        );
        this.form.createDateEnd = this.$dayjs(time[1]).format(
          "YYYY-MM-DD 23:59:59"
        );
      } else {
        // 当日期选择器被清空时，清空相应的表单字段
        this.form.createDateStart = "";
        this.form.createDateEnd = "";
      }
    },
  },
};
</script>
<style scoped lang="scss">
.addRight {
  float: right;
  margin: 10px 0;
}
.toClear {
  clear: both;
}
.numberStyle {
  background: #e7f8ff;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  border: 1px solid #e3f4ff;
  margin-bottom: 10px;
}
</style>
