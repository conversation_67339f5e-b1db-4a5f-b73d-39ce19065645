<!--
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-03-25 08:34:09
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-17 15:51:47
-->
<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="700px"
    center
    :modal-append-to-body="false"
    :before-close="cleanForm"
    :modal="false"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <el-form ref="form" :model="form" :rules="rules">
      <el-row style="margin-top: 10px;">
        <el-col :span="24">
          <el-form-item label="市场" label-width="90px" prop="type">
            <el-radio-group v-model="form.type">
              <el-radio :label="1">固定凭证</el-radio>
              <el-radio :label="4">可用凭证</el-radio>
              <el-radio :label="3">收购凭证</el-radio>
              <el-radio :label="6">活动凭证</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="用户" label-width="90px" prop="userId">
            <el-select
              v-model="form.userId"
              placeholder="请按照手机/ID/昵称/姓名搜索"
              style="width: 100%;"
              filterable
              @clear="saveUserList = userList"
              :filter-method="filterMethod"
            >
              <el-option
                v-for="item in saveUserList"
                :key="item.userId"
                :label="item.userName"
                :value="item.userId"
              >
                <span> [{{ item.userId }}]{{ item.userName }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="凭证Code" label-width="90px" prop="lotCode">
            <el-input v-model="form.lotCode"> </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="数量" label-width="90px" prop="count">
            <el-input v-model="form.count"> </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="价格" label-width="90px" prop="price">
            <el-input v-model="form.price"> </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="价值" label-width="90px" prop="worth">
            <el-input v-model="form.worth" disabled> </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="配货说明" label-width="90px" prop="lotDesc">
            <el-input v-model="form.lotDesc" type="textarea"> </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" size="small" :loading="saveFlag" @click="saveForm">确 定</el-button>
      <el-button size="small" @click="cleanForm">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { distribution } from "@/api/transferAssets/holdingAssets.js";
export default {
  name: "PhDialog",
  props: {
    userList: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      dialogVisible: false,
      title: "配货",
      saveFlag: false,
      form: {
        type: undefined,
        userId: "",
        lotCode: "",
        count: "",
        price: "",
        worth: "",
        lotDesc: "",
      },
      rules: {
        type: [{ required: true, message: "请选择市场", trigger: "change" }],
        userId: [{ required: true, message: "请选择用户", trigger: "change" }],
        price: [
          { required: true, message: "请填写价格", trigger: "change" },
          {
            pattern: /^[0-9]\d*(\.\d+)?$|^0\.\d*[1-9]\d*$/,
            message: "请填写正确价格",
            trigger: "blur",
          },
        ],
        count: [
          { required: true, message: "请填写数量", trigger: "change" },
          { pattern: /^-?\d+$/, message: "请输入整数", trigger: "blur" },
        ],
        lotCode: [
          { required: true, message: "请填写凭证Code", trigger: "change" },
        ],
        worth: [{ required: true, message: "请填写价值", trigger: "change" }],
      },
      saveUserList: [],
    };
  },

  watch: {
    "form.count": {
      handler() {
        if (this.form.price) {
          this.form.worth = (
            Number(this.form.count) * Number(this.form.price)
          ).toFixed(2);
        }
      },
      immediate: true,
    },
    "form.price": {
      handler() {
        if (this.form.count) {
          this.form.worth = (
            Number(this.form.count) * Number(this.form.price)
          ).toFixed(2);
        }
      },
      immediate: true,
    },
  },

  methods: {
    filterMethod(query) {
      console.log(query,this.userList);
      if(!query){
        this.saveUserList = this.userList;
      }else {
        this.saveUserList = this.userList.filter((item) => {
          return (
            (item.nickName+'').indexOf(query) > -1 ||
            (item.accountNumber+'').indexOf(query) > -1 ||
            (item.userId+'').indexOf(query) > -1 ||
            (item.userName+'').indexOf(query) > -1
          );
        });
      }
    },
    init() {
      this.dialogVisible = true;
      this.saveUserList = this.userList
    },

    cleanForm() {
      this.dialogVisible = false;
      this.saveFlag = false;
      this.form = {
        type: undefined,
        userId: "",
        lotCode: "",
        count: "",
        price: "",
        worth: "",
        lotDesc: "",
      };
    },

    saveForm() {
      if (this.saveFlag) {
        return;
      }
      this.saveFlag = true;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const params = {
            ...this.form,
          };
          distribution(params)
            .then((res) => {
              this.$message.success("配货成功");
              this.cleanForm();
              this.$emit("getData");
            })
            .finally(() => {
              this.saveFlag = false;
            });
        } else {
          this.saveFlag = false;
        }
      });
    },
  },
};
</script>

<style></style>
