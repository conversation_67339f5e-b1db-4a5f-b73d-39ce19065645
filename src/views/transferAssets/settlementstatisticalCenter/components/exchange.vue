<template>
  <div class="whole">
    <el-form :model="form" ref="form">
      <el-row>
        <el-col :span="7">
          <el-form-item label="发生时间" label-width="110px">
            <el-date-picker
              style="width: 100%;"
              v-model="time"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="changeTime"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="凭证ID" label-width="110px" prop="lotId">
            <el-input v-model="form.lotId" placeholder="请输入凭证ID"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="所属项目名称/code" label-width="150px" prop="projectKeyword">
            <el-input v-model="form.projectKeyword" placeholder="请输入所属项目名称/code"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="" label-width="15px">
            <el-button @click="reset"> 重置</el-button>
            <el-button type="primary" @click="queryData">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row></el-row>
    </el-form>
    <el-row>
      <el-col :span="5">
        <el-card shadow="never">
          <div>收益总额</div>
          <div>¥{{ statisticData && statisticData.tradeIncomeTotal || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="5" :offset="1">
        <el-card shadow="never">
          <div>交易手续费（买家）</div>
          <div>¥{{ statisticData && statisticData.tradeBuyFeeTotal || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="5" :offset="1">
        <el-card shadow="never">
          <div>交易手续费（卖家）</div>
          <div>¥ {{ statisticData && statisticData.tradeSellFeeTotal || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="5" :offset="1">
        <el-card shadow="never">
          <div>平台技术服务费（商家）</div>
          <div>¥ {{ statisticData && statisticData.tradeUpbeatFeeTotal || 0 }}</div>
        </el-card>
      </el-col>
    </el-row>
    <el-row style="margin-top: 16px">
      <el-col :span="5">
        <el-card shadow="never">
          <div>服务中心（销售可用凭证）服务费</div>
          <div>¥ {{ statisticData && statisticData.tradeServiceSaleFeeTotal || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="5" :offset="1">
        <el-card shadow="never">
          <div>服务中心（销售收购凭证）服务费</div>
          <div>¥{{ statisticData && statisticData.tradeServiceWhoFeeTotal || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="5" :offset="1">
        <el-card shadow="never">
          <div>会员（自主销售）服务费</div>
          <div>¥{{ statisticData && statisticData.tradeMemberSelfFeeTotal || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="5" :offset="1">
        <el-card shadow="never">
          <div>会员（委托销售）服务费</div>
          <div>¥ {{ statisticData && statisticData.tradeMemberEntrustFeeTotal || 0 }}</div>
        </el-card>
      </el-col>
    </el-row>
    <div>
      <div class="addRight" v-if="roleId != 8">
        <el-button @click="exportFile">导出</el-button>
      </div>

      <el-table :data="tableData" style="width: 100%;" border>
        <el-table-column prop="statisticsDate" label="日期" min-width="100">
        </el-table-column>

        <el-table-column prop="date" label="凭证" min-width="150">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image v-if="scope.row.lotImg" :src="scope.row.lotImg" fit="fill"></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.lotName || '-' }}</div>
                <div>【{{ scope.row.lotCode || '-' }}】</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="tradeIncome" label="收益总额" min-width="100">

        </el-table-column>
        <el-table-column
          prop="tradeBuyFee"
          label="交易手续费（买家）"
          min-width="100"
        >
        </el-table-column>
        <el-table-column
          prop="tradeSellFee"
          label="交易手续费（卖家）"
          min-width="100"
        >
        </el-table-column>
        <el-table-column
          prop="tradeUpbeatFee"
          label="平台技术服务费（商家）"
          min-width="130"
        >
          <template slot-scope="scope">
            <div>{{ scope.row.tradeUpbeatFee || 0 }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="tradeServiceSaleFee"
          label="服务中心（销售可用凭证）服务费"
          min-width="120"
        >
        </el-table-column>
        <el-table-column
          prop="tradeServiceWhoFee"
          label="服务中心（销售收购凭证）服务费"
          min-width="100"
        >
        </el-table-column>
        <el-table-column
          prop="tradeMemberSelfFee"
          label="会员（自主销售）服务费"
          min-width="100"
        >
        </el-table-column>
        <el-table-column
          prop="tradeMemberEntrustFee"
          label="会员（委托销售）服务费"
          min-width="100"
        >
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="form.current"
        :limit.sync="form.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>
<script>
import {
  statistic, totalTradePage, totalTradeTotal,
} from "@/api/transferAssets/settlementstatisticalCenter.js";
import store from "@/store";
export default {
  name: "Exchange",
  data() {
    return {
      form: {
        current: 1,
        pageSize: 10,
        statisticsStartTime: "",
        statisticsEndTime: "",
        lotId: "",
      },
      statisticData: {},
      resetForm: {},
      tableData: [],
      time: "",
      total: 0,
      roleId: store.getters.roleId,
    };
  },
  mounted() {
    this.resetForm = {
      ...this.form,
    };
    this.queryData();
  },
  methods: {
    queryData() {
      this.getData();
      this.getStatistic();
    },
    // 获取数据
    getData() {
      this.form.current = 1;
      this.form.pageSize = 10;
      const params = {
        ...this.form,
      };
      totalTradePage(params).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
      });
    },

    getStatistic() {
      totalTradeTotal(this.form).then((res) => {
        this.statisticData = res.data;
      });
    },

    // 重置
    reset() {
      this.form = {
        ...this.resetForm,
      };
      this.time = "";
      this.queryData();
    },

    // 改变申请日期
    changeTime(time) {
      if(!time) {
        this.form.statisticsStartTime = "";
        this.form.statisticsEndTime = "";
        return;
      }
      this.form.statisticsStartTime = this.$dayjs(time[0]).format(
        "YYYY-MM-DD 00:00:00"
      );
      this.form.statisticsEndTime = this.$dayjs(time[1]).format(
        "YYYY-MM-DD 23:59:59"
      );
    },

    // 分页 直接按queryForm 开始查询
    getList() {
      const params = {
        ...this.form,
      };
      totalTradePage(params).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
      });
    },

    // 导出
    exportFile() {
      const params = {
        ...this.form,
      };
      this.download(
        "/statistics-settle/total-trade-export",
        {...params},
        `结算统计中心-交易所收益.xlsx`
      );
    },
  },
};
</script>
<style scoped lang="scss">
.addRight {
  float: right;
  margin: 10px 0;
}
</style>
