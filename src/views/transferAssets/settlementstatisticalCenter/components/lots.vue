<template>
  <div class="whole">
    <el-form :model="form" ref="form">
      <el-row>
        <el-col :span="8">
          <el-form-item label="发生时间" label-width="110px">
            <el-date-picker
              style="width: 100%;"
              v-model="time"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="changeTime"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="凭证ID" label-width="110px" prop="lotId">
            <el-input v-model="form.lotId" placeholder="请输入凭证ID"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="商家" label-width="110px" prop="upbeatId">
            <el-input v-model="form.upbeatId" placeholder="请输入账号/昵称/ID/手机号搜索"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="服务中心" label-width="110px" prop="serviceId">
            <el-input v-model="form.serviceId" placeholder="请输入账号/昵称/ID/手机号搜索"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="所属项目名称/code" label-width="150px" prop="projectKeyword">
            <el-input v-model="form.projectKeyword" placeholder="请输入所属项目名称/code"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="" label-width="15px">
            <el-button @click="reset"> 重置</el-button>
            <el-button type="primary" @click="queryData">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row></el-row>
    </el-form>
    <el-row>
      <el-col :span="5">
        <el-card shadow="never">
          <div>订单交易总额</div>
          <div>¥{{ statisticData && statisticData.tradeAmountTotal || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="5" :offset="1">
        <el-card shadow="never">
          <div>平台收益总额</div>
          <div>¥{{ statisticData && statisticData.platformIncomeTotal || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="5" :offset="1">
        <el-card shadow="never">
          <div>商家收益</div>
          <div>¥ {{ statisticData && statisticData.upbeatIncomeTotal || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="5" :offset="1">
        <el-card shadow="never">
          <div>交易所收益</div>
          <div>¥ {{ statisticData && statisticData.tradeIncomeTotal || 0 }}</div>
        </el-card>
      </el-col>
    </el-row>
    <el-row style="margin-top: 16px">
      <el-col :span="5">
        <el-card shadow="never">
          <div>服务中心（销售可用凭证）销售总额</div>
          <div>¥ {{ statisticData && statisticData.lotServiceSaleAmountTotal || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="5" :offset="1">
        <el-card shadow="never">
          <div>服务中心（销售收购凭证）销售总额</div>
          <div>¥{{ statisticData && statisticData.lotServiceWhoAmountTotal || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="5" :offset="1">
        <el-card shadow="never">
          <div>会员（自主销售）销售总额</div>
          <div>¥{{ statisticData && statisticData.lotMemberSelfAmountTotal || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="5" :offset="1">
        <el-card shadow="never">
          <div>会员（委托销售）销售总额</div>
          <div>¥ {{ statisticData && statisticData.lotMemberEntrustAmountTotal || 0 }}</div>
        </el-card>
      </el-col>
    </el-row>    <div>
      <div class="addRight" v-if="roleId != 8">
        <el-button @click="exportFile">导出</el-button>
      </div>

      <el-table :data="tableData" style="width: 100%;" border>
        <el-table-column prop="statisticsDate" label="日期" min-width="100">
        </el-table-column>

        <el-table-column prop="date" label="凭证" min-width="150">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image v-if="scope.row.lotImg" :src="scope.row.lotImg" fit="fill"></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.lotName || '-' }}</div>
                <div>【{{ scope.row.lotCode || '-' }}】</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="tradeAmount" label="交易总额" min-width="100">
        </el-table-column>
        <el-table-column
          prop="platformIncome"
          label="平台收益"
          min-width="100"
        >
        </el-table-column>
        <el-table-column prop="upbeatIncome" label="商家收益" min-width="100">
        </el-table-column>
        <el-table-column
          prop="tradeIncome"
          label="交易所收益"
          min-width="100"
        >
        </el-table-column>
        <el-table-column
          prop="lotServiceSaleAmount"
          label="服务中心（销售可用凭证）"
          min-width="130"
        >
        </el-table-column>
        <el-table-column
          prop="lotServiceWhoAmount"
          label="服务中心（销售收购凭证）"
          min-width="100"
        >
        </el-table-column>
        <el-table-column
          prop="lotMemberSelfAmount"
          label="会员（自主销售）"
          min-width="100"
        >
        </el-table-column>
        <el-table-column
          prop="lotMemberEntrustAmount"
          label="会员（委托销售）"
          min-width="120"
        >
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="form.current"
        :limit.sync="form.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>
<script>
import {
  statisticalList, totalLotPage, totalLotTotal, totalUpbeatPage,
} from "@/api/transferAssets/settlementstatisticalCenter.js";
import store from "@/store";
export default {
  name: "Lots",
  data() {
    return {
      form: {
        current: 1,
        pageSize: 10,
        statisticsStartTime: "",
        statisticsEndTime: "",
        lotId: "",
        upbeatId: "",
        serviceId: "",
        projectKeyword: "",
      },
      statisticData: {},
      resetForm: {},
      tableData: [],
      time: "",
      total: 0,
      roleId: store.getters.roleId,
    };
  },
  mounted() {
    this.resetForm = {
      ...this.form,
    };
    this.queryData();
  },
  methods: {
    queryData(){
      this.getData();
      this.getStatistic();
    },
    // 获取数据
    getData() {
      this.form.current = 1;
      this.form.pageSize = 10;
      const params = {
        ...this.form,
      };
      totalLotPage(params).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
      });
    },

    // 分页 直接按queryForm 开始查询
    getList() {
      const params = {
        ...this.form,
      };
      totalLotPage(params).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
      });
    },


    getStatistic() {
      totalLotTotal(this.form).then((res) => {
        this.statisticData = res.data;
      });
    },

    // 重置
    reset() {
      this.form = {
        ...this.resetForm,
      };
      this.time = "";
      this.queryData()
    },

    // 改变申请日期
    changeTime(time) {
      if(!time) {
        this.form.statisticsStartTime = "";
        this.form.statisticsEndTime = "";
        return;
      }
      this.form.statisticsStartTime = this.$dayjs(time[0]).format(
        "YYYY-MM-DD 00:00:00"
      );
      this.form.statisticsEndTime = this.$dayjs(time[1]).format(
        "YYYY-MM-DD 23:59:59"
      );
    },

    // 分页 直接按queryForm 开始查询

    // 导出
    exportFile() {
      const params = {
        ...this.form,
      };
      this.download(
        "/statistics-settle/total-lot-export",
        {...params},
        `结算统计中心-凭证总览.xlsx`
      );
    },
  },
};
</script>
<style scoped lang="scss">
.addRight {
  float: right;
  margin: 10px 0;
}
</style>
