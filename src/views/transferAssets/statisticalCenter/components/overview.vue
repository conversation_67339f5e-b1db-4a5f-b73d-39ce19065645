<template>
  <div class="whole">
    <el-form :model="form" ref="form">
      <el-row>
        <el-col :span="8">
          <el-form-item label="发生时间" label-width="110px">
            <el-date-picker
              style="width: 100%;"
              v-model="time"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="changeTime"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="凭证ID" label-width="110px" prop="lotId">
            <el-input v-model="form.lotId" placeholder="请输入凭证ID"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="商家" label-width="110px" prop="upbeatId">
            <el-input v-model="form.upbeatId" placeholder="请输入账号/昵称/ID/手机号搜索"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="服务中心" label-width="110px" prop="serviceId">
            <el-input v-model="form.serviceId" placeholder="请输入账号/昵称/ID/手机号搜索"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="所属项目名称/code" label-width="150px" prop="projectKeyword">
            <el-input v-model="form.projectKeyword" placeholder="请输入所属项目名称/code"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="" label-width="110px">
            <el-button @click="reset"> 重置</el-button>
            <el-button type="primary" @click="getStatistic">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row></el-row>
    </el-form>
    <div class="content">
      <div class="field">
        <div class="field-label">凭证总览</div>
        <div class="field-items">
          <el-row>
            <el-col :span="5">
              <div>交易总额</div>
              <div>¥{{ statisticData  && statisticData.tradeAmount || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>平台收益/平台净收益</div>
              <span>¥{{ statisticData  && statisticData.platformIncome || 0 }}</span>
              <span>/¥{{ statisticData  && statisticData.platformIncomeOutRetain || 0 }}</span>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>商家收益</div>
              <div>¥ {{ statisticData  && statisticData.upbeatIncome || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>交易所收益</div>
              <div>¥ {{ statisticData  && statisticData.tradeIncome || 0 }}</div>
            </el-col>
          </el-row>
          <el-row style="margin-top: 16px">
            <el-col :span="5">
              <div>服务中心（销售可用凭证）</div>
              <div>¥{{ statisticData  && statisticData.lotServiceSaleAmount || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>服务中心（销售收购凭证）</div>
              <div>¥{{ statisticData  && statisticData.lotServiceWhoAmount || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>会员（自主销售）</div>
              <div>¥ {{ statisticData  && statisticData.lotMemberSelfAmount || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>会员（委托销售）</div>
              <div>¥ {{ statisticData  && statisticData.lotMemberEntrustAmount || 0 }}</div>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="field">
        <div class="field-label">平台收益总览</div>
        <div class="field-items">
          <el-row>
            <el-col :span="5">
              <div>收益总额</div>
              <div>¥{{ statisticData  && statisticData.platformIncome || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>交易手续费（买家）</div>
              <div>¥{{ statisticData  && statisticData.platformBuyFee || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>交易手续费（卖家）</div>
              <div>¥ {{ statisticData  && statisticData.platformSellFee || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>平台技术服务费（商家）</div>
              <div>¥ {{ statisticData  && statisticData.platformUpbeatFee || 0 }}</div>
            </el-col>
          </el-row>
          <el-row style="margin-top: 16px">
            <el-col :span="5">
              <div>服务中心（销售可用凭证）服务费</div>
              <div>¥{{ statisticData  && statisticData.platformServiceSaleFee || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>服务中心（销售收购凭证）服务费</div>
              <div>¥{{ statisticData  && statisticData.platformServiceWhoFee || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>会员（自主销售）服务费</div>
              <div>¥ {{ statisticData  && statisticData.platformMemberSelfFee || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>会员（委托销售）服务费</div>
              <div>¥ {{ statisticData  && statisticData.platformMemberEntrustFee ||0 }}</div>
            </el-col>
          </el-row  >
        </div>
      </div>
      <div class="field">
        <div class="field-label">商家收益总览</div>
        <div class="field-items">
          <el-row>
            <el-col :span="5">
              <div>收益总额</div>
              <div>¥{{ statisticData  && statisticData.upbeatIncome || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>商家自主销售</div>
              <div>¥{{ statisticData  && statisticData.upbeatUpbeatSelfAmount || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>交易手续费（买家）</div>
              <div>¥ {{ statisticData  && statisticData.upbeatBuyFee || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>交易手续费（卖家）</div>
              <div>¥ {{ statisticData  && statisticData.upbeatSellFee || 0 }}</div>
            </el-col>
          </el-row>
          <el-row style="margin-top: 16px">
            <el-col :span="5">
              <div>服务中心（销售可用凭证）服务费</div>
              <div>¥{{ statisticData  && statisticData.upbeatServiceSaleFee || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>服务中心（销售收购凭证）服务费</div>
              <div>¥{{ statisticData  && statisticData.upbeatServiceWhoFee || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>会员（自主销售）服务费</div>
              <div>¥ {{ statisticData  && statisticData.upbeatMemberSelfFee || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>会员（委托销售）服务费</div>
              <div>¥ {{ statisticData  && statisticData.upbeatMemberEntrustFee || 0 }}</div>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="field">
        <div class="field-label">交易所收益总览</div>
        <div class="field-items">
          <el-row>
            <el-col :span="5">
              <div>收益总额</div>
              <div>¥{{ statisticData  && statisticData.tradeIncome || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>交易手续费（买家）</div>
              <div>¥{{ statisticData  && statisticData.tradeBuyFee || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>交易手续费（卖家）</div>
              <div>¥ {{ statisticData  && statisticData.tradeSellFee || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>平台技术服务费（商家）</div>
              <div>¥ {{ statisticData  && statisticData.tradeUpbeatFee || 0 }}</div>
            </el-col>
          </el-row>
          <el-row style="margin-top: 16px">
            <el-col :span="5">
              <div>服务中心（销售可用凭证）服务费</div>
              <div>¥{{ statisticData  && statisticData.tradeServiceSaleFee || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>服务中心（销售收购凭证）服务费</div>
              <div>¥{{ statisticData  && statisticData.tradeServiceWhoFee || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>会员（自主销售）服务费</div>
              <div>¥ {{ statisticData  && statisticData.tradeMemberSelfFee || 0 }}</div>
            </el-col>
            <el-col :span="5" :offset="1">
              <div>会员（委托销售）服务费</div>
              <div>¥ {{ statisticData  && statisticData.tradeMemberEntrustFee || 0 }}</div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  logStatistic,
  statisticalList,
} from "@/api/transferAssets/statisticalCenter.js";
import store from "@/store";

export default {
  name: "Overview",
  data() {
    return {
      form: {
        statisticsStartTime: "",
        statisticsEndTime: "",
        lotId: "",
        upbeatId: "",
        serviceId: "", //服务中心ID
        projectKeyword: "", //所属项目名称/code
      },
      statisticData: {},
      resetForm: {},
      time: "",
      roleId: store.getters.roleId,
    };
  },
  mounted() {
    this.resetForm = {
      ...this.form,
    };
    this.getStatistic();
  },
  methods: {
    getStatistic() {
      logStatistic(this.form).then((res) => {
        this.statisticData = res.data;
      });
    },

    // 重置
    reset() {
      this.form = {
        ...this.resetForm,
      };
      this.time = "";
      this.getStatistic();
    },

    // 改变申请日期
    changeTime(time) {
      if(!time) {
        this.form.statisticsStartTime = "";
        this.form.statisticsEndTime = "";
        return;
      }
      this.form.statisticsStartTime = this.$dayjs(time[0]).format(
        "YYYY-MM-DD 00:00:00"
      );
      this.form.statisticsEndTime = this.$dayjs(time[1]).format(
        "YYYY-MM-DD 23:59:59"
      );
    },

    // 导出
    exportFile() {
      const params = {
        ...this.form,
      };
      this.download(
        "/statistical/export-statistic",
        {...params},
        `统计中心.xlsx`
      );
    },
  },
};
</script>
<style scoped lang="scss">
.content {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  .field {
    flex-basis: calc(50% - 16px); /* 减去16px的外边距 */
    margin-right: 16px; /* 添加16px的外边距 */
    border: 1px solid #e6ebf5;
    margin-bottom: 16px;

    .field-label {
      padding: 16px;
      border-bottom: 1px solid #e6ebf5;
    }

    .field-items{
      padding: 16px;
    }
  }
  .field:nth-child(2n) {
    margin-right: 0; /* 每行的第二个子元素不需要右边距 */
  }
}
</style>
