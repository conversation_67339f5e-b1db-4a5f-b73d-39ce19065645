<template>
  <div class="whole">
    <el-form :model="form" ref="form">
      <el-row>
        <el-col :span="8">
          <el-form-item label="发生时间" label-width="80px">
            <el-date-picker
              style="width: 100%;"
              v-model="time"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="changeTime"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="商品名称" label-width="80px" prop="goodsName">
            <el-input v-model="form.goodsName" placeholder="请输入商品名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="所属机构" label-width="70px" prop="referrerId">
            <el-input v-model="form.referrerId" placeholder="请输入账号/昵称/ID/手机号搜索"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="所属项目名称/code" label-width="150px" prop="projectKeyword">
            <el-input v-model="form.projectKeyword" placeholder="请输入所属项目名称/code"></el-input>
          </el-form-item>
        </el-col>
<!--        <el-col :span="6">-->
<!--          <el-form-item label="实际所属机构" label-width="100px" prop="realReferrerId">-->
<!--            <el-input v-model="form.realReferrerId" placeholder="请输入账号/昵称/ID/手机号搜索"></el-input>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
        <el-col :span="8">
          <el-form-item label="" label-width="15px">
            <el-button @click="reset"> 重置</el-button>
            <el-button type="primary" @click="queryData">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row></el-row>
    </el-form>
    <el-row>
      <el-col :span="4">
        <el-card shadow="never">
          <div>订单总额</div>
          <div>¥{{ statisticData && statisticData.tradeAmountTotal || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="4" :offset="1">
        <el-card shadow="never">
          <div>交易商品数</div>
          <div>¥{{ statisticData && statisticData.tradeNumTotal || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="4" :offset="1">
        <el-card shadow="never">
          <div>交易手续费（买家）</div>
          <div>¥ {{ statisticData && statisticData.buyerFeeTotal || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="4" :offset="1">
        <el-card shadow="never">
          <div>交易手续费（商家）</div>
          <div>¥ {{ statisticData && statisticData.sellFeeTotal || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="4" :offset="1">
        <el-card shadow="never">
          <div>平台技术服务费（商家）</div>
          <div>¥ {{ statisticData && statisticData.platformUpbeatFeeTotal || 0 }}</div>
        </el-card>
      </el-col>


    </el-row>
    <div style="height: 16px"></div>
    <el-row>
      <el-col :span="4">
        <el-card shadow="never">
          <div>推荐服务费（会员）</div>
          <div>¥ {{ statisticData && statisticData.platformUserFeeTotal || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="4" :offset="1">
        <el-card shadow="never">
          <div>商家服务费（会员）</div>
          <div>¥ {{ statisticData && statisticData.userFeeTotal || 0 }}</div>
        </el-card>
      </el-col>

      <el-col :span="4" :offset="1">
        <el-card shadow="never">
          <div>服务中心服务费（商家）</div>
          <div>¥ {{ statisticData && statisticData.serviceFeeTotal || 0 }}</div>
        </el-card>
      </el-col>

      <el-col :span="4" :offset="1">
        <el-card shadow="never">
          <div>合规经营保证金</div>
          <div>¥ {{ statisticData && statisticData.retainUpTotal || 0 }}</div>
        </el-card>
      </el-col>
    </el-row>
    <div>
      <div class="addRight" v-if="roleId != 8">
        <el-button @click="exportFile">导出</el-button>
      </div>

      <el-table :data="tableData" style="width: 100%;" border>
        <el-table-column prop="statisticsDate" label="日期" min-width="100">
        </el-table-column>
        <el-table-column prop="date" label="商品" min-width="150">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image v-if="scope.row.goodsImg" :src="scope.row.goodsImg" fit="fill"></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.goodsName || '-' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="tradeNum" label="交易数量" min-width="100">
        </el-table-column>
        <el-table-column prop="tradeAmount" label="总价" min-width="100">
        </el-table-column>
        <el-table-column
          prop="buyerFee"
          label="交易手续费（买家）"
          min-width="100"
        >
        </el-table-column>
        <el-table-column
          prop="sellFee"
          label="交易手续费（商家）"
          min-width="130"
        >
        </el-table-column>
        <el-table-column
          prop="platformUpbeatFee"
          label="平台技术服务费（商家）"
          min-width="130"
        >
        </el-table-column>

        <el-table-column
          prop="platformUserFee"
          label="推荐服务费（会员）"
          min-width="130"
        >
        </el-table-column>

        <el-table-column
          prop="userFee"
          label="商家服务费（会员）"
          min-width="130"
        >
        </el-table-column>

        <el-table-column
          prop="serviceFee"
          label="服务中心服务费（商家）"
          min-width="130"
        >
        </el-table-column>

        <el-table-column
          prop="retainUp"
          label="合规经营保证金"
          min-width="130"
        >
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="form.current"
        :limit.sync="form.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>
<script>
import {
  totalGoodsPage,
  totalGoodsTotal,
} from "@/api/transferAssets/statisticalCenter.js";
import store from "@/store";
export default {
  name: "Order",
  data() {
    return {
      form: {
        current: 1,
        pageSize: 10,
        statisticsEndTime: "",
        statisticsStartTime: "",
        goodsName: "",
        referrerId: "",
        realReferrerId: "",
        projectKeyword: "",
      },
      statisticData: {},
      resetForm: {},
      tableData: [],
      time: "",
      total: 0,
      roleId: store.getters.roleId,
    };
  },
  mounted() {
    this.resetForm = {
      ...this.form,
    };
    this.queryData();
  },
  methods: {
    queryData() {
      this.getData();
      this.getStatistic();
    },
    // 获取数据
    getData() {
      this.form.current = 1;
      this.form.pageSize = 10;
      const params = {
        ...this.form,
      };
      totalGoodsPage(params).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
      });
    },

    getStatistic() {
      totalGoodsTotal(this.form).then((res) => {
        this.statisticData = res.data;
      });
    },

    // 重置
    reset() {
      this.form = {
        ...this.resetForm,
      };
      this.time = "";
      this.queryData()
    },

    // 改变申请日期
    changeTime(time) {
      if(!time) {
        this.form.statisticsStartTime = "";
        this.form.statisticsEndTime = "";
        return;
      }
      this.form.statisticsStartTime = this.$dayjs(time[0]).format(
        "YYYY-MM-DD 00:00:00"
      );
      this.form.statisticsEndTime = this.$dayjs(time[1]).format(
        "YYYY-MM-DD 23:59:59"
      );
    },

    // 分页 直接按queryForm 开始查询
    getList() {
      console.log('this.form....',this.form);
      const params = {
        ...this.form,
      };
      totalGoodsPage(params).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
      });
    },

    // 导出
    exportFile() {
      const params = {
        ...this.form,
      };
      this.download(
        "/statistics-center/total-goods-export",
        {...params},
        `统计中心-商城订单.xlsx`
      );
    },
  },
};
</script>
<style scoped lang="scss">
.addRight {
  float: right;
  margin: 10px 0;
}
</style>
