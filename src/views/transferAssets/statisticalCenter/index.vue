<template>
  <el-tabs v-model="activeName" @tab-click="handleClick">
    <el-tab-pane label="总览" name="总览" lazy>
      <Overview></Overview>
    </el-tab-pane>
    <el-tab-pane label="凭证总览" name="凭证总览" lazy>
      <Lots></Lots>
    </el-tab-pane>
    <el-tab-pane label="平台收益" name="平台收益" lazy>
      <Platform></Platform>
    </el-tab-pane>
    <el-tab-pane label="商家收益" name="商家收益" lazy>
      <List></List>
    </el-tab-pane>
    <el-tab-pane label="交易所收益" name="交易所收益" lazy>
      <Exchange></Exchange>
    </el-tab-pane>
    <el-tab-pane label="服务中心销售" name="服务中心销售" lazy>
      <Service></Service>
    </el-tab-pane>
    <el-tab-pane label="商城订单" name="商城订单" lazy>
      <Order></Order>
    </el-tab-pane>
  </el-tabs>
</template>
<script>
import Overview from './components/overview'
import Lots from './components/lots'
import Platform from './components/platform'
import List from './components/list'
import Exchange from './components/exchange'
import Service from './components/service'
import Order from './components/order'

export default {
  name: "StatisticalCenter",
  components: {
    Overview,
    Lots,
    Platform,
    List,
    Exchange,
    Service,
    Order
  },

  data() {
    return {
      activeName: '总览'
    }
  },

  methods: {
    handleClick(tab, event) {
      this.activeName = tab.name
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep .el-tabs__header {
  margin: 0 20px;
}
</style>
