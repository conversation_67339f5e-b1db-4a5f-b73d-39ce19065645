<!--
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-04-07 08:07:38
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-18 14:16:07
-->
<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="60%"
    center
    :modal-append-to-body="false"
    :before-close="cleanForm"
    :modal="false"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div>
      <div class="topDiv">
        <el-button v-if="!!lotCode && val === 6" class="buttonStyle" @click="upload.open = true"
        >导入</el-button
        >
        <el-button type="primary" class="buttonStyle" @click="openSelect"
          >选择</el-button
        >
      </div>

      <el-table :data="tableData" class="divStyle" border :row-key="getKeys">
        <el-table-column prop="userId" label="会员ID" min-width="200">
        </el-table-column>
        <el-table-column label="会员" min-width="200">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image v-if="scope.row.headPortrait" :src="scope.row.headPortrait" fit="fill"></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.nickName }}</div>
                <div>
                  {{ scope.row.accountNumber || scope.row.userName }} 【{{ scope.row.realName || scope.row.userName }}】
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="倍数" min-width="200" v-if="val === 4">
          <template slot-scope="scope">
            <a-input-number
              v-model="scope.row.multiple" :min="0.01" :precision="2"
              :parser="value => parserFloat(value, 2)" placeholder=""></a-input-number>
          </template>
        </el-table-column>
        <el-table-column label="委托倍数" min-width="200" v-if="val === 4">
          <template slot-scope="scope">
            <a-input-number
              v-model="scope.row.entrustMultiple" :min="0.01" :precision="2"
              :parser="value => parserFloat(value, 2)" placeholder=""></a-input-number>
          </template>
        </el-table-column>
        <el-table-column label="最大买入量" min-width="200" v-if="val === 1">
          <template slot-scope="scope">
            <a-input-number v-model="scope.row.max" :min="0" :precision="0" :parser="parserInt" placeholder=""></a-input-number>
          </template>
        </el-table-column>
        <el-table-column label="规则(x:y,x和y都为正整数)" min-width="200" v-if="val === 3">
          <template slot-scope="scope">
            <el-input v-model="scope.row.lotRule" placeholder=""></el-input>
          </template>
        </el-table-column>

        <el-table-column label="规则(x:y,x和y都为正整数)" min-width="200" v-if="val === 5">
          <template slot-scope="scope">
            <el-input v-model="scope.row.saleRule" placeholder=""></el-input>
          </template>
        </el-table-column>
        <el-table-column min-width="200" label="操作">
          <template slot-scope="{ row }">
            <el-button
              type="text"
              style="color: red;"
              @click="deletetableData(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <third-dialog
        ref="third"
        @getData="getData"
        :list="tableData"
        :roleId="roleId"
      ></third-dialog>
      <import-dialog
        :open.sync="upload.open"
        :title="upload.title"
        :actionUrl="upload.actionUrl"
        :downloadUrl="upload.downloadUrl"
        :data="{ lotCode }"
        :onFinish="handleImportFinish"
      />
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" size="small" @click="save">确 定</el-button>
      <el-button size="small" @click="cleanForm">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script>
import ThirdDialog from "./thirdDialog.vue";
import {isMultiple, parserFloat, parserInt} from "@/utils/validate";
import ImportDialog from "@/components/ImportDialog/index.vue";
import {getLotForbid} from "@/api/transferAssets/lotList";
export default {
  name: "SecondDialog",
  components: {
    ImportDialog,
    ThirdDialog,
  },
  props: {
    lotId: {
      type: String,
      default: undefined,
    },
    lotCode: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      dialogVisible: false,
      title: "已选列表",
      tableData: [],
      val: "", // 确定是第几个弹窗
      roleId: undefined,
      saveFlag: true,
      upload: {
        open: false,
        title: "禁止挂单会员导入",
        actionUrl: process.env.VUE_APP_BASE_API + "/lot/check-registration",
        downloadUrl: 'https://cdn-newteapai.eazytec-cloud.com/registration.xlsx',
        downloadName: '用户导入模版'
      }
    };
  },

  methods: {
    parserInt,
    parserFloat,
    init(val, list) {
      if (val) {
        this.tableData = list;
      }
      this.dialogVisible = true;
      this.val = val;
      if (val === 1) {
        this.title = "单独会员最多买入量已选列表";
      }
      if (val === 2) {
        this.title = "大客户已选列表";
      }
      if (val === 3) {
        this.title = "大客户带商家凭证规则已选列表";
      }
      if (val === 4) {
        this.title = "禁止服务中心会员卖出已选列表";
      }
      if (val === 5) {
        this.title = "服务中心带商家凭证规则已选列表";
      }
      if (val === 6) {
        this.title = "禁止挂单会员已选列表";
      }
      this.roleId = /[45]/.test(val) ? 2 : undefined;
    },

    getKeys(row) {
      return row.userId;
    },

    // 打开配置弹框
    openSelect() {
      this.$refs.third.init(this.val, this.tableData);
    },

    cleanForm() {
      this.dialogVisible = false;
      this.tableData = [];
      this.val = "";
    },

    save() {
      if (this.val === 1) {
        for (let i = 0; i < this.tableData.length; i++) {
          if (!this.tableData[i].max) {
            this.$message.error("最大买入量为必填项");
            this.saveFlag = false;
            return;
          } else {
            this.saveFlag = true;
          }
        }
      }

      if (this.val === 3) {
        for (let i = 0; i < this.tableData.length; i++) {
          if (!isMultiple(this.tableData[i].lotRule)) {
            this.$message.error("规则需为x:y");
            this.saveFlag = false;
            return;
          } else {
            this.saveFlag = true;
          }
        }
      }

      if (this.val === 4) {
        for (let i = 0; i < this.tableData.length; i++) {
          if (!this.tableData[i].multiple) {
            this.$message.error("倍数为必填项");
            this.saveFlag = false;
            return;
          } else if (!this.tableData[i].entrustMultiple) {
            this.$message.error("委托倍数为必填项");
            this.saveFlag = false;
            return;
          } else {
            this.saveFlag = true;
          }
        }
      }

      if (this.val === 5) {
        for (let i = 0; i < this.tableData.length; i++) {
          if (!isMultiple(this.tableData[i].saleRule)) {
            this.$message.error("规则需为x:y");
            this.saveFlag = false;
            return;
          } else {
            this.saveFlag = true;
          }
        }
      }
      console.log('saveFlag...',this.saveFlag, this.val, this.tableData);
      if (this.saveFlag) {
        this.$emit("getChangeList", this.val, this.tableData);
      }
      this.cleanForm();
    },

    getData(list) {
      let data = [...new Set(list)];
      let before = this.tableData.filter((val) => !data.find((item) => item.userId === val.userId));
      data.forEach((item) => {
        const index = this.tableData.findIndex((val) => val.userId === item.userId);
        if (index >= 0) {
          Object.assign(item, this.tableData[index])
        }
      })
      this.tableData = before.concat(data);
    },

    deletetableData(item) {
      this.tableData = this.tableData.filter(
        (val) => val.userId !== item.userId
      );
    },

    handleUsers(list) {
      return list.map(item => {
        const {userName, realName} = item
        return {
          ...item,
          accountNumber: userName,
          userName: realName,
        }
      })
    },

    handleImportFinish(res) {
      this.getData(res.data)
    },
  },
};
</script>
<style scoped lang="scss">
.divStyle {
  height: 350px;
  overflow: auto;
}

.topDiv {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}

.topInput {
  margin-right: 10px;
}

.buttonStyle {
  height: 36px;
  float: right;
}
</style>
