<!--
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-04-02 13:27:19
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-04-10 13:47:04
-->
<template>
  <div>
    <el-form :model="form" ref="form" :rules="rules">
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="同上级服务中心交易(预支)"
            label-width="180px"
            prop="superTrade"
          >
            <el-radio-group v-model="form.superTrade">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
            <div>仅允许同上级服务中心交易（预支）默认为否</div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item
            label="赠送拍品为零预支"
            label-width="180px"
            prop="advance"
          >
            <el-radio-group v-model="form.advance">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
            <div>赠送拍品只有为零时才允许预支自主销售交易，默认为否</div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item
            label="没参与翻倍允许预支"
            label-width="180px"
            prop="isDouble"
          >
            <el-radio-group v-model="form.isDouble">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
            <div>没参与过翻倍允许预支，默认为否</div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item
            label="预约销售带票规则（个人用户）"
            label-width="240px"
            prop="advanceRule"
          >
            <el-input v-model="form.advanceRule" placeholder=""></el-input>
<!--            <div>-->
<!--              默认规则：2:4:4 预支拍品自主销售带拍品规则(个人用户)：3:2:1 表示 3表示会员-->
<!--              2拍品服务机构 1拍品上拍方-->
<!--            </div>-->
          </el-form-item>
        </el-col>
      </el-row>

<!--      <el-row>-->
<!--        <el-col :span="12">-->
<!--          <el-form-item-->
<!--            label="预约销售带票规则（个人用户）"-->
<!--            label-width="240px"-->
<!--            prop="gryhRule"-->
<!--          >-->
<!--            <el-input v-model="form.gryhRule" placeholder=""></el-input>-->
<!--            <div>-->
<!--              默认规则：2:4:4-->
<!--            </div>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--      </el-row>-->

      <el-row>
        <el-col :span="12">
          <el-form-item
            label="预约销售带票规则（企业用户）"
            label-width="240px"
            prop="comAdvanceRule"
          >
            <el-input v-model="form.comAdvanceRule" placeholder=""></el-input>
<!--            <div>-->
<!--              默认规则：4:3:3-->
<!--            </div>-->
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>
  </div>
</template>

<script>
export default {
  name: "FourthPage",

  data() {
    return {
      form: {
        superTrade: 0,
        advance: 0,
        isDouble: 0,
        advanceRule: "2:4:4",
        comAdvanceRule: "4:3:3",
      },
      total: 0,
      time: "",
      resetForm: {},
      tableData: [],
      rules: {
        advanceRule: [
          { required: true, message: "请输入预支销售带拍品规则（个人用户）", trigger: "change" },
          {
            pattern: /^[1-9]:[1-9]:[1-9]$/,
            message: "请填写正确规则",
            trigger: "blur",
          },
        ],
        comAdvanceRule: [
          { required: true, message: "请输入预约销售带票规则（企业用户）", trigger: "change" },
          {
            pattern: /^[1-9]:[1-9]:[1-9]$/,
            message: "请填写正确规则",
            trigger: "blur",
          },
        ],
      }
    };
  },

  methods: {
    /**
     * 返回表单的值，供父组件调用
     */
    getFormData() {
      const formData = {
        ...this.form,
      };
      return formData;
    },

    formValidate() {
      const validateRes = new Promise((resolve, reject) => {
        this.$refs["form"].validate((valid) => {
          if (valid) {
            resolve();
          } else {
            reject("fourth");
          }
        });
      });
      return validateRes;
    },

    indexChoose(res) {
      Object.keys(this.form).map((key) => {
        this.form[key] = res.bookingSettingEntity[key];
      });
    },
  },
};
</script>
