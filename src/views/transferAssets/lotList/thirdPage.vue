<!--
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-04-02 13:27:19
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-18 13:11:17
-->
<template>
  <div>
    <el-form :model="form" ref="form" :rules="rules">
      <el-row :gutter="24">
        <el-col :span="2">
          <div class="privateLabel">启用交易所</div>
        </el-col>
        <el-col :span="6">
          <el-form-item label="" label-width="0" prop="isTrade" style="margin-bottom:0">
            <el-switch
              v-model="form.isTrade"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
              @change="isTradeChange"
            >
            </el-switch>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8" v-if="form.isTrade === 1">
          <el-form-item label="交易所" label-width="72px" prop="tradeId">
            <el-select
              v-model="form.tradeId"
              placeholder="请选择交易所"
              style="width: 100%;"
              filterable
              clearable
            >
              <el-option
                v-for="item in memberList"
                :key="item.userId"
                :label="item.userId"
                :value="item.userId"
              >
                <span> [{{ item.userId }}]{{ item.nickName }} </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="2">
          <div class="privateLabel">启用商家</div>
        </el-col>
        <el-col :span="5">
          <el-form-item
            label=""
            label-width="0"
            prop="isUpbeat"
            style="margin-bottom: 0"
          >
            <el-switch
              v-model="form.isUpbeat"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
              @change="isUpbeatChange"
            >
            </el-switch>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8" v-if="form.isUpbeat === 1">
          <el-form-item
            label="商家"
            label-width="72px"
            prop="upbeatId"
          >
            <el-select
              v-model="form.upbeatId"
              placeholder="请选择商家"
              style="width: 100%;"
              filterable
              clearable
            >
              <el-option
                v-for="item in memberList"
                :key="item.userId"
                :label="item.userId"
                :value="item.userId"
              >
                <span> [{{ item.userId }}]{{ item.nickName }} </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="privateLabel"><span class="require">*</span>交易手续费（买家）%</div>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item
            label="平台"
            label-width="62px"
            prop="platformBuyerFee"
            style="margin-bottom: 0"
          >
            <el-input-number
              v-model="form.platformBuyerFee"
              :min="0"
              :max="100"
              placeholder="请输入"
            ></el-input-number>
            <div class="privateTip">平台收取交易手续费（买家）%</div>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="form.isUpbeat === 1">
          <el-form-item
            label="商家"
            label-width="122px"
            prop="upbeatBuyerFee"
            style="margin-bottom: 0"
          >
            <el-input-number
              v-model="form.upbeatBuyerFee"
              :min="0"
              :max="100"
              placeholder="请输入"
            ></el-input-number>
            <div class="privateTip">商家收取交易手续费（买家）%</div>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="form.isTrade === 1">
          <el-form-item
            label="交易所"
            label-width="72px"
            prop="tradPlatformFee"
            style="margin-bottom: 0"
          >
            <el-input-number
              v-model="form.tradPlatformFee"
              :min="0"
              :max="100"
              placeholder="请输入"
            ></el-input-number>
            <div class="privateTip">交易所从平台分得手续费%</div>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="privateLabel"><span class="require">*</span>交易手续费（卖家）%</div>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="平台" label-width="62px" prop="platformSaleFee" style="margin-bottom: 0"
          >
            <el-input-number
              v-model="form.platformSaleFee"
              placeholder="请输入"
            ></el-input-number>
            <div class="privateTip">平台收取卖方（交易）手续费%</div>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="form.isUpbeat === 1">
          <el-form-item
            label="商家"
            label-width="122px"
            prop="upbeatSaleFee"
            style="margin-bottom: 0"
          >
            <el-input-number
              v-model="form.upbeatSaleFee"
              :min="0"
              :max="100"
              placeholder="请输入"
            ></el-input-number>
            <div class="privateTip">商家收取卖方（交易）手续费%</div>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="form.isTrade === 1">
          <el-form-item label="交易所" label-width="72px" prop="tradeSaleFee" style="margin-bottom: 0"
          >
            <el-input-number
              v-model="form.tradeSaleFee"
              :min="0"
              :max="100"
              placeholder="请输入"
            ></el-input-number>
            <div class="privateTip">交易所从平台分得手续费%</div>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="privateLabel"><span class="require">*</span>平台技术服务费（商家）%</div>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item
            label="平台"
            label-width="62px"
            prop="platformUpbeatSaleFee"
          >
            <el-input-number
              v-model="form.platformUpbeatSaleFee"
              :min="0"
              :max="100"
              placeholder="请输入"
            ></el-input-number>
            <div class="privateTip">平台收取商家自主销售管理费%</div>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="form.isTrade === 1">
          <el-form-item
            label="交易所"
            label-width="72px"
            prop="tradeUpbeatFee"
          >
            <el-input-number
              v-model="form.tradeUpbeatFee"
              :min="0"
              :max="100"
              placeholder="请输入"
            ></el-input-number>
            <div class="privateTip">交易所从平台分得管理费%</div>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="privateLabel"><span class="require">*</span>推荐服务费（服务中心）%</div>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="平台" label-width="62px" prop="serviceSaleFee">
            <el-input-number
              v-model="form.serviceSaleFee"
              placeholder="请输入"
              :min="0"
              :max="100"
            ></el-input-number>
            <div class="privateTip">平台收取服务中心自主销售可用凭证管理费</div>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="form.isUpbeat === 1">
          <el-form-item
            label="商家"
            label-width="122px"
            prop="upbeatServiceSaleFee"
          >
            <el-input-number
              v-model="form.upbeatServiceSaleFee"
              placeholder="请输入"
              :min="0"
              :max="100"
            ></el-input-number>
            <div class="privateTip">商家收取服务中心自主销售可用凭证管理费</div>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="form.isTrade === 1">
          <el-form-item
            label="交易所"
            label-width="72px"
            prop="tradeServiceSaleFee"
          >
            <el-input-number
              v-model="form.tradeServiceSaleFee"
              placeholder="请输入"
              :min="0"
              :max="100"
            ></el-input-number>
            <div class="privateTip">交易所从平台分得管理费%</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item
            label="平台"
            label-width="62px"
            prop="serviceWholesaleFee"
          >
            <el-input-number
              v-model="form.serviceWholesaleFee"
              placeholder="请输入"
              :min="0"
              :max="100"
            ></el-input-number>
            <div class="privateTip">平台收取服务中心自主销售收购凭证管理费%</div>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="form.isUpbeat === 1">
          <el-form-item
            label="商家"
            label-width="122px"
            prop="upbeatServiceWholesaleFee"
          >
            <el-input-number
              v-model="form.upbeatServiceWholesaleFee"
              placeholder="请输入"
              :min="0"
              :max="100"
            ></el-input-number>
            <div class="privateTip">商家收取服务中心自主销售收购凭证管理费</div>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="form.isTrade === 1">
          <el-form-item
            label="交易所"
            label-width="72px"
            prop="tradeServiceWholesaleFee"
          >
            <el-input-number
              v-model="form.tradeServiceWholesaleFee"
              placeholder="请输入"
              :min="0"
              :max="100"
            ></el-input-number>
            <div class="privateTip">交易所从平台分得管理费%</div>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="privateLabel"><span class="require">*</span>推荐服务费（会员）%</div>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="平台" label-width="62px" prop="userSaleFee" style="margin-bottom: 0">
            <el-input-number
              v-model="form.userSaleFee"
              placeholder="请输入"
              :min="0"
              :max="100"
            ></el-input-number>
            <div class="privateTip">平台收取普通会员(自主销售)管理费%</div>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="form.isUpbeat === 1">
          <el-form-item
            label="商家"
            label-width="122px"
            prop="upbeatUserSaleFee"
            style="margin-bottom: 0"
          >
            <el-input-number
              v-model="form.upbeatUserSaleFee"
              placeholder="请输入"
              :min="0"
              :max="100"
            ></el-input-number>
            <div class="privateTip">商家收取普通会员(自主销售)管理费%</div>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="form.isTrade === 1">
          <el-form-item
            label="交易所"
            label-width="72px"
            prop="tradeUserSaleFee"
            style="margin-bottom: 0"
          >
            <el-input-number
              v-model="form.tradeUserSaleFee"
              placeholder="请输入"
              :min="0"
              :max="100"
            ></el-input-number>
            <div class="privateTip">交易所从平台分得管理费%</div>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="privateLabel"><span class="require">*</span>委托服务费（会员）%</div>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="平台" label-width="62px" prop="userLinkFee" style="margin-bottom: 0">
            <el-input-number
              v-model="form.userLinkFee"
              placeholder="请输入"
              :min="0"
              :max="100"
            ></el-input-number>
            <div class="privateTip">平台收取普通会员(会员挂单)管理费%</div>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="form.isUpbeat === 1">
          <el-form-item
            label="商家"
            label-width="122px"
            prop="upbeatUserLinkFee"
            style="margin-bottom: 0"
          >
            <el-input-number
              v-model="form.upbeatUserLinkFee"
              placeholder="请输入"
              :min="0"
              :max="100"
            ></el-input-number>
            <div class="privateTip">商家收取普通会员(会员挂单)管理费%</div>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="form.isTrade === 1">
          <el-form-item
            label="交易所"
            label-width="72px"
            prop="tradeUserLinkFee"
            style="margin-bottom: 0"
          >
            <el-input-number
              v-model="form.tradeUserLinkFee"
              placeholder="请输入"
              :min="0"
              :max="100"
            ></el-input-number>
            <div class="privateTip">交易所从平台分得管理费%</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8">
          <div class="privateLabel"><span class="require">*</span>合规经营保证金%</div>
        </el-col>
        <el-col :span="8">
          <div class="privateLabel"><span class="require">*</span>挂单折扣%</div>
        </el-col>
        <el-col :span="8">
          <div class="privateLabel"><span class="require">*</span>普通会员最多买入量</div>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item
            label=""
            label-width="0"
            prop="upbeatSaleFreeze"
          >
            <el-input-number
              v-model="form.upbeatSaleFreeze"
              :min="0"
              :max="100"
              placeholder="请输入"
            ></el-input-number>
            <div class="privateTip">商家卖出金额的n%资金进入保留资金</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label=""
            label-width="0"
            prop="userLinkDiscount"
          >
            <el-input-number
              v-model="form.userLinkDiscount"
              :min="0"
              :max="100"
              placeholder="请输入"
            ></el-input-number>
            <div class="privateTip">会员挂单卖给服务中心或商家的折扣，当前价*折扣=挂单价格</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label=""
            label-width="0"
            prop="userMaxBuyCount"
          >
            <el-input-number
              v-model="form.userMaxBuyCount"
              :min="0"
              placeholder="请输入"
            ></el-input-number>
            <div class="privateTip">普通会员最多买入量</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div>
      单独会员最多买入量
      <el-button type="text" @click="openDialog(1, userMaxBuyEntityList)"
      >选择
      </el-button
      >
      <div class="privateTip">单独会员购买量</div>
    </div>
    <div style="margin-top: 22px">
      大客户
      <el-button type="text" @click="openDialog(2, customerEntityList)"
      >选择
      </el-button
      >
      <div class="privateTip">大客户不受普通会员最多买入量限制</div>
    </div>
    <div style="margin-top: 22px">
      大客户带商家凭证规则
      <el-button type="text" @click="openDialog(3, customerRuleEntityList)"
      >选择
      </el-button
      >
    </div>
    <div style="margin-top: 22px">
      禁止服务中心会员卖出
      <el-button type="text" @click="openDialog(4, forbidSaleEntityList)"
      >选择
      </el-button
      >
    </div>
    <div style="margin-top: 22px">
      服务中心带商家凭证规则
      <el-button type="text" @click="openDialog(5, serviceRuleEntityList)"
      >选择
      </el-button
      >
    </div>
    <div style="margin-top: 22px">
      禁止挂单会员
      <el-button type="text" @click="openDialog(6, registrationEntityList)"
      >选择
      </el-button
      >
    </div>
    <second-dialog
      ref="sec"
      :lotId="form.lotId"
      :lotCode="form.lotCode"
      :memberList="memberList"
      @getChangeList="getChangeList"
    ></second-dialog>
  </div>
</template>

<script>
import SecondDialog from "./secondDialog.vue";

export default {
  name: "ThirdPage",
  components: {
    SecondDialog,
  },
  props: {
    memberList: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      form: {
        lotId: '',
        lotCode: '',
        isTrade: 1,
        upbeatId: "",
        isUpbeat: 1,
        tradeId: "",
        platformBuyerFee: undefined,
        upbeatBuyerFee: undefined,
        tradPlatformFee: undefined,
        platformSaleFee: undefined,
        upbeatSaleFee: undefined,
        tradeSaleFee: undefined,
        platformUpbeatSaleFee: undefined,
        tradeUpbeatFee: undefined,
        serviceSaleFee: undefined,
        upbeatServiceSaleFee: undefined,
        tradeServiceSaleFee: undefined,
        serviceWholesaleFee: undefined,
        upbeatServiceWholesaleFee: undefined,
        tradeServiceWholesaleFee: undefined,
        userSaleFee: undefined,
        upbeatUserSaleFee: undefined,
        tradeUserSaleFee: undefined,
        userLinkFee: undefined,
        upbeatUserLinkFee: undefined,
        tradeUserLinkFee: undefined,
        upbeatSaleFreeze: undefined,
        userLinkDiscount: undefined,
        userMaxBuyCount: undefined,
      },
      userMaxBuyEntityList: [], // 单独会员最大买入量
      customerEntityList: [], // 大客户
      customerRuleEntityList: [], // 大客户带商家凭证规则
      forbidSaleEntityList: [], // 禁止服务中心会员 卖出
      serviceRuleEntityList: [], // 服务中心带商家凭证规则
      registrationEntityList: [], // 禁止挂单会员
      rules: {
        platformBuyerFee: [
          {required: true, message: " ", trigger: "change"},
        ],
        upbeatBuyerFee: [
          {required: true, message: " ", trigger: "change"},
        ],
        tradPlatformFee: [
          {required: true, message: " ", trigger: "change"},
        ],

        platformSaleFee: [
          {required: true, message: " ", trigger: "change"},
        ],
        upbeatSaleFee: [
          {required: true, message: " ", trigger: "change"},
        ],
        tradeSaleFee: [
          {required: true, message: " ", trigger: "change"},
        ],

        platformUpbeatSaleFee: [
          {required: true, message: " ", trigger: "change"},
        ],
        tradeUpbeatFee: [
          {required: true, message: " ", trigger: "change"},
        ],
        serviceSaleFee: [
          {required: true, message: " ", trigger: "change"},
        ],

        upbeatServiceSaleFee: [
          {required: true, message: " ", trigger: "change"},
        ],
        tradeServiceSaleFee: [
          {required: true, message: " ", trigger: "change"},
        ],
        serviceWholesaleFee: [
          {required: true, message: " ", trigger: "change"},
        ],

        upbeatServiceWholesaleFee: [
          {required: true, message: " ", trigger: "change"},
        ],
        userSaleFee: [{required: true, message: " ", trigger: "change"}],
        tradeServiceWholesaleFee: [
          {required: true, message: " ", trigger: "change"},
        ],

        userLinkFee: [{required: true, message: " ", trigger: "change"}],

        upbeatUserLinkFee: [
          {required: true, message: " ", trigger: "change"},
        ],
        tradeUserLinkFee: [
          {required: true, message: " ", trigger: "change"},
        ],
        upbeatSaleFreeze: [
          {required: true, message: " ", trigger: "change"},
        ],
        userLinkDiscount: [
          {required: true, message: " ", trigger: "change"},
        ],
        userMaxBuyCount: [
          {required: true, message: " ", trigger: "change"},
        ],
        upbeatUserSaleFee: [
          {required: true, message: " ", trigger: "change"},
        ],
        tradeUserSaleFee: [
          {required: true, message: " ", trigger: "change"},
        ],
      },
    };
  },

  methods: {
    /**
     * 返回表单的值，供父组件调用
     */
    getFormData() {
      const formData = {
        ...this.form,
        // platformBuyerFee: this.form.platformBuyerFee
        //   ? Number(this.form.platformBuyerFee)
        //   : undefined,
        // upbeatBuyerFee: this.form.upbeatBuyerFee
        //   ? Number(this.form.upbeatBuyerFee)
        //   : undefined,
        // tradPlatformFee: this.form.tradPlatformFee
        //   ? Number(this.form.tradPlatformFee)
        //   : undefined,

        // platformSaleFee: this.form.platformSaleFee
        //   ? Number(this.form.platformSaleFee)
        //   : undefined,
        // upbeatSaleFee: this.form.upbeatSaleFee
        //   ? Number(this.form.upbeatSaleFee)
        //   : undefined,
        // tradeSaleFee: this.form.tradeSaleFee
        //   ? Number(this.form.tradeSaleFee)
        //   : undefined,

        // platformUpbeatSaleFee: this.form.platformUpbeatSaleFee
        //   ? Number(this.form.platformUpbeatSaleFee)
        //   : undefined,
        // tradeUpbeatFee: this.form.tradeUpbeatFee
        //   ? Number(this.form.tradeUpbeatFee)
        //   : undefined,
        // serviceSaleFee: this.form.serviceSaleFee
        //   ? Number(this.form.serviceSaleFee)
        //   : undefined,

        // upbeatServiceSaleFee: this.form.upbeatServiceSaleFee
        //   ? Number(this.form.upbeatServiceSaleFee)
        //   : undefined,
        // tradeServiceSaleFee: this.form.tradeServiceSaleFee
        //   ? Number(this.form.tradeServiceSaleFee)
        //   : undefined,
        // serviceWholesaleFee: this.form.serviceWholesaleFee
        //   ? Number(this.form.serviceWholesaleFee)
        //   : undefined,

        // upbeatServiceWholesaleFee: this.form.upbeatServiceWholesaleFee
        //   ? Number(this.form.upbeatServiceWholesaleFee)
        //   : undefined,
        // tradeServiceWholesaleFee: this.form.tradeServiceWholesaleFee
        //   ? Number(this.form.tradeServiceWholesaleFee)
        //   : undefined,
        // userSaleFee: this.form.userSaleFee
        //   ? Number(this.form.userSaleFee)
        //   : undefined,

        // upbeatUserSaleFee: this.form.upbeatUserSaleFee
        //   ? Number(this.form.upbeatUserSaleFee)
        //   : undefined,
        // tradeUserSaleFee: this.form.tradeUserSaleFee
        //   ? Number(this.form.tradeUserSaleFee)
        //   : undefined,
        // userLinkFee: this.form.userLinkFee
        //   ? Number(this.form.userLinkFee)
        //   : undefined,

        // upbeatUserLinkFee: this.form.upbeatUserLinkFee
        //   ? Number(this.form.upbeatUserLinkFee)
        //   : undefined,
        // tradeUserLinkFee: this.form.tradeUserLinkFee
        //   ? Number(this.form.tradeUserLinkFee)
        //   : undefined,
        // upbeatSaleFreeze: this.form.upbeatSaleFreeze
        //   ? Number(this.form.upbeatSaleFreeze)
        //   : undefined,
        // userLinkDiscount: this.form.userLinkDiscount
        //   ? Number(this.form.userLinkDiscount)
        //   : undefined,
        // userMaxBuyCount: this.form.userMaxBuyCount
        //   ? Number(this.form.userMaxBuyCount)
        //   : undefined,

        userMaxBuyEntityList: this.userMaxBuyEntityList,
        customerEntityList: this.customerEntityList,
        customerRuleEntityList: this.customerRuleEntityList,
        forbidSaleEntityList: this.forbidSaleEntityList,
        serviceRuleEntityList: this.serviceRuleEntityList,
        registrationEntityList: this.registrationEntityList,
      };
      return formData;
    },

    isTradeChange(val) {
      if (val === 0) {
        this.form.tradeId = "";
        this.form.tradPlatformFee = undefined;
        this.form.tradeSaleFee = undefined;
        this.form.tradeUpbeatFee = undefined;
        this.form.tradeServiceSaleFee = undefined;
        this.form.tradeServiceWholesaleFee = undefined;
        this.form.tradeUserSaleFee = undefined;
        this.form.tradeUserLinkFee = undefined;
      }
    },

    isUpbeatChange(val) {
      if (val === 0) {
        this.form.upbeatId = "";
        this.form.upbeatBuyerFee = undefined
        this.form.upbeatSaleFee = undefined;
        this.form.upbeatServiceSaleFee = undefined;
        this.form.upbeatServiceWholesaleFee = undefined;
        this.form.upbeatUserSaleFee = undefined;
        this.form.upbeatUserLinkFee = undefined;
      }
    },

    formValidate() {
      const validateRes = new Promise((resolve, reject) => {
        this.$refs["form"].validate((valid) => {
          if (valid) {
            resolve();
          } else {
            reject("third");
          }
        });
      });
      return validateRes;
    },

    indexChoose(res) {
      Object.keys(this.form).map((key) => {
        this.form[key] = res.tradeConfigEntity[key];
      });
      this.form.lotCode = res.lotCode;
      this.userMaxBuyEntityList = res.userMaxBuyEntityList;
      this.customerEntityList = res.customerEntityList;
      this.customerRuleEntityList = res.customerRuleEntityList;
      this.forbidSaleEntityList = res.forbidSaleEntityList;
      this.serviceRuleEntityList = res.serviceRuleEntityList;
      this.registrationEntityList = res.registrationEntityList;
    },

    // val 确定哪个弹框
    openDialog(val, list) {
      this.$refs.sec.init(val, list);
    },

    // 获取拿回来的 数据
    getChangeList(val, list) {
      if (val === 1) {
        this.userMaxBuyEntityList = list;
      }
      if (val === 2) {
        this.customerEntityList = list;
      }
      if (val === 3) {
        this.customerRuleEntityList = list;
      }
      if (val === 4) {
        this.forbidSaleEntityList = list;
      }
      if (val === 5) {
        this.serviceRuleEntityList = list;
      }
      if (val === 6) {
        this.registrationEntityList = list;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.titleStyle {
  margin: 0 0 10px 0;
  padding-left: 20px;
}

//::v-deep .el-form-item {
//  margin-right: 12px;
//}

::v-deep .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before, .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:before {
  content: '';
}

::v-deep .el-form-item__label {
  padding-left: 12px;
  height: 36px;
  line-height: 36px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 2px 0px 0px 2px;
  border: 1px solid #D9D9D9;
  text-align: left;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  font-style: normal;
  border-right: none;
}

::v-deep .el-input-number--medium {
  width: 100%;
}

::v-deep .el-input__inner {
  border-radius: 0px 2px 2px 0px;
}

.privateLabel {
  height: 36px;
  line-height: 36px;
  background: rgba(0, 0, 0, 0);
  border-radius: 2px 0px 0px 2px;
  border: 1px solid rgba(0, 0, 0, 0);
  text-align: left;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  font-style: normal;
}

.privateTip {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  line-height: 22px;
  text-align: left;
  font-style: normal;
}

.require {
  color: #FF4D4F;
  font-size: 14px;
}
</style>
