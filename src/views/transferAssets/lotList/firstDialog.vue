<!--
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-04-07 08:07:38
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-18 16:37:25
-->
<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="60%"
    center
    :modal-append-to-body="false"
    :before-close="cleanForm"
    :modal="false"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div class="divStyle">
      <el-form :model="form" ref="form"  @submit.native.prevent>
        <el-row>
          <el-col :span="12">
            <el-form-item label="会员ID/昵称/手机号/姓名" label-width="180px" prop="lotCode">
              <el-input v-model="form.keyword" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="" label-width="43px">
              <el-button @click="reset">重置</el-button>
              <el-button type="primary" @click="getData">查询</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table
        :data="tableData"
        class="tableStyle"
        ref="multipleTable"
        border
        :row-key="getKeys"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" :reserve-selection="true">
        </el-table-column>
        <el-table-column prop="userId" label="会员ID" min-width="200">
        </el-table-column>
        <el-table-column label="会员" min-width="200">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image
                  v-if="scope.row.headPortrait"
                  :src="scope.row.headPortrait"
                  fit="fill"
                ></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.nickName }}</div>
                <div>
                  {{ scope.row.accountNumber }} 【{{ scope.row.userName }}】
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="form.current"
        :limit.sync="form.pageSize"
        @pagination="getList"
      />
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" size="small" @click="save">确 定</el-button>
      <el-button size="small" @click="cleanForm">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { getServiceUsers } from "@/api/basis/basis.js";
export default {
  name: "FirstDialog",
  data() {
    return {
      dialogVisible: false,
      title: "选择服务中心",
      tableData: [],
      multipleSelection: [],
      secondSelect: [],
      form: {
        current: 1,
        pageSize: 10,
        keyword: '',
        roleId: 2,
      },
      total: 0,
    };
  },

  watch: {
    // tableData(val) {
    //   let data = this.secondSelect;
    //   val.forEach((item) => {
    //     data?.forEach((value) => {
    //       if (item.userId === value.userId) {
    //         this.$refs.multipleTable.toggleRowSelection(item);
    //       }
    //     });
    //   });
    // },
  },

  methods: {
    reset() {
      this.form = {
        ...this.$options.data().form,
      };
      this.getList();
    },
    init(row) {
      this.dialogVisible = true;
      this.getData();
      if (row) {
        this.$nextTick(() => {
          row.forEach(item => {
            this.$refs.multipleTable.toggleRowSelection(item);
          });
          // this.multipleSelection = row;
          // this.secondSelect = row;
        })
      }
    },

    getKeys(row) {
      return row.userId;
    },

    // 分页获取数据
    getData() {
      const params = {
        ...this.form,
        current: 1,
        pageSize: 10,
        roleId: 2
      };
      getServiceUsers(params).then((res) => {
        this.tableData = res.data.records;
        this.total = res.data.total;
      });
    },

    // 分页接口
    getList() {
      const params = {
        ...this.form,
      };
      getServiceUsers(params).then((res) => {
        this.tableData = res.data.records;
        this.total = res.data.total;
      });
    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    cleanForm() {
      this.form = {
        ...this.$options.data().form,
      };
      this.dialogVisible = false;
      this.tableData = [];
      this.multipleSelection = [];
      this.form.current = 1
    },
    save() {
      this.$emit("changeServiceUsers", this.multipleSelection);
      this.cleanForm();
    },
  },
};
</script>
<style scoped lang="scss">
.divStyle {
  height: 450px;
  overflow: auto;
}
</style>
