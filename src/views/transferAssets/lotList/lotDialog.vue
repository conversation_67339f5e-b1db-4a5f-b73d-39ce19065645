<!--
 * @Description: 新增/编辑凭证
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-03-25 08:34:09
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-15 13:34:19
-->
<template>
  <el-drawer
    :title="title"
    :visible.sync="dialogVisible"
    :before-close="cleanForm"
    size="80%"
    destroy-on-close
    :wrapperClosable='false'
  >
    <div class="whole">
      <div class="topStyle">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="基本信息" name="first">
            <first-page :memberList="memberList" ref="fpg"></first-page>
          </el-tab-pane>
          <el-tab-pane label="凭证详情" name="second">
            <second-page ref="spg"></second-page>
          </el-tab-pane>
          <el-tab-pane label="交易设置" name="third">
            <third-page :memberList="memberList" ref="tpg"></third-page>
          </el-tab-pane>
          <el-tab-pane label="预支设置" name="fourth">
            <fourth-page ref="fupg"></fourth-page>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="bottomStyle">
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" size="small" :loading="saveFlag" @click="saveForm"
            >确 定</el-button
          >
          <el-button size="small" @click="cleanForm">取 消</el-button>
        </span>
      </div>
    </div>
  </el-drawer>
</template>
<script>
import FirstPage from "./firstPage.vue";
import SecondPage from "./secondPage.vue";
import ThirdPage from "./thirdPage.vue";
import FourthPage from "./fourthPage.vue";
import { queryList } from "@/api/basis/basis.js";
import { save, info, update } from "@/api/transferAssets/lotList.js";
export default {
  name: "LotDialog",
  components: {
    FirstPage,
    SecondPage,
    ThirdPage,
    FourthPage,
  },
  dicts: ["sys_user_sex", "role_id"],
  data() {
    return {
      dialogVisible: false,
      title: "新增凭证",
      saveFlag: false,
      activeName: "first",
      form: {
        lotId: "",
      },
      memberList: [],
      detail: {},
    };
  },

  methods: {
    async init(row) {
      this.dialogVisible = true;
      this.getMember(); //调用会员列表接口
      if (row) {
        this.title = "编辑凭证";
        const params = row.lotId;
        setTimeout(async () => {
          await this.$refs.fpg.getProjectData()
          info(params).then((res) => {

            this.detail = res.data;
            this.form.lotId = res.data.lotId;
            if (this.detail.serviceUsers && this.detail.serviceUsers.length > 0) {
              this.detail.serviceUsers.forEach(serviceUser => {
                serviceUser.accountNumber = serviceUser.mobile;
              })
            }
            this.$nextTick(() => {
              this.$refs.fpg.indexChoose(this.detail);
              this.$refs.spg.indexChoose(this.detail);
              this.$refs.tpg.indexChoose(this.detail);
              this.$refs.fupg.indexChoose(this.detail);
            });
          });
        })


      } else {
        setTimeout(() => {
          this.$refs.fpg.getProjectData()
        })
      }
    },

    getMember() {
      queryList().then((res) => {
        this.memberList = res.data;
      });
    },

    handleClick(tab, event) {
      console.log(tab, event);
    },

    cleanForm() {
      this.dialogVisible = false;
      this.saveFlag = false;
      this.activeName = "first";
      this.title = "新增凭证";
      this.form = {
        lotId: "",
      };
    },

    saveForm() {
      if (this.saveFlag) {
        return;
      }
      this.$nextTick(() => {
        const formData1 = this.$refs.fpg.getFormData();
        const formData2 = this.$refs.spg.getFormData();
        const formData3 = this.$refs.tpg.getFormData();
        const formData4 = this.$refs.fupg.getFormData();
        const formData = {
          ...this.form,
          ...formData1,
          ...formData2,
          serviceUsers: formData1.serviceUsers,
          bookingSettingEntity: formData4,
          tradeConfigEntity: formData3,
          userMaxBuyEntityList: formData3.userMaxBuyEntityList,
          customerEntityList: formData3.customerEntityList,
          customerRuleEntityList: formData3.customerRuleEntityList,
          forbidSaleEntityList: formData3.forbidSaleEntityList,
          serviceRuleEntityList: formData3.serviceRuleEntityList,
          registrationEntityList: formData3.registrationEntityList,
        };
        // 表单值验证
        this.divVerify().then(() => {
          const params = {
            ...formData,
          };
          this.saveFlag = true;
          if (this.form.lotId) {
            update(params)
              .then((res) => {
                this.$message.success("修改成功");
                this.$emit("getData");
                this.cleanForm();
              })
              .finally(() => {
                this.saveFlag = false;
              });
          } else {
            save(params)
              .then((res) => {
                this.$message.success("保存成功");
                this.$emit("getData");
                this.cleanForm();
              })
              .finally(() => {
                this.saveFlag = false;
              });
          }
        });
      });
    },

    // 校验
    divVerify() {
      const validateRes1 = this.$refs.fpg.formValidate();
      const validateRes2 = this.$refs.spg.formValidate();
      const validateRes3 = this.$refs.tpg.formValidate();
      const validateRes4 = this.$refs.fupg.formValidate();
      return Promise.all([
        validateRes1,
        validateRes2,
        validateRes3,
        validateRes4,
      ]);
    },
  },
};
</script>

<style>
.fatherTitle {
  font-size: 18px;
  font-weight: bolder;
}
.sonTitle {
  font-weight: bold;
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.topStyle {
  height: 90%;
  width: 100%;
  overflow: auto;
}
.bottomStyle {
  text-align: center;
  padding-top: 10px;
}
</style>
