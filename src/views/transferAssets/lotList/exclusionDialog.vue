<!--
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-03-25 08:34:09
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-14 20:10:00
-->
<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="30%"
    center
    :modal-append-to-body="false"
    :before-close="cleanForm"
    :modal="false"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <el-form ref="form" :model="form" :rules="rules">
      <el-row style="margin-top: 10px;">
        <el-col :span="24">
          <el-form-item
            label="除权日期"
            label-width="90px"
            prop="exclusionTime"
          >
            <el-date-picker
              v-model="form.exclusionTime"
              type="date"
              placeholder="选择日期"
              style="width: 100%;"
              :disabled="val === 2"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="转存期" label-width="90px" prop="uploadingDay">
            <el-input
              v-model="form.uploadingDay"
              maxlength="10"
              show-word-limit
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="起售日期" label-width="90px" prop="saleTime">
            <el-date-picker
              v-model="form.saleTime"
              type="date"
              placeholder="选择日期"
              disabled
              style="width: 100%;"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="说明" label-width="90px" prop="remark">
            <el-input
              type="textarea"
              placeholder="请输入内容"
              v-model="form.remark"
              show-word-limit
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" size="small" :loading="saveFlag" @click="saveForm">确 定</el-button>
      <el-button size="small" @click="cleanForm">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  exclusion,
  uploadingDay,
  updateExclusion,
} from "@/api/transferAssets/lotList.js";
export default {
  name: "ExclusionDialog",
  data() {
    return {
      dialogVisible: false,
      title: "除权",
      saveFlag: false,
      form: {
        id: "",
        exclusionTime: "",
        saleTime: "",
        uploadingDay: "",
        remark: "",
        lotId: "",
      },
      val: "",
      rules: {
        exclusionTime: [
          { required: true, message: "请选择除权时间", trigger: "change" },
        ],
        saleTime: [
          { required: true, message: "请选择起售时间", trigger: "change" },
        ],
        uploadingDay: [
          { required: true, message: "请填写转存期", trigger: "change" },
        ],
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        },
      }
    };
  },

  watch: {
    "form.uploadingDay": {
      handler() {
        if (this.form.exclusionTime) {
          this.form.saleTime = this.$dayjs(this.form.exclusionTime).add(
            Number(this.form.uploadingDay),
            "day"
          );
        }
      },
      immediate: true,
    },
    "form.exclusionTime": {
      handler() {
        if (this.form.uploadingDay) {
          this.form.saleTime = this.$dayjs(this.form.exclusionTime).add(
            Number(this.form.uploadingDay),
            "day"
          );
        }
      },
      immediate: true,
    },
  },

  methods: {
    init(val, row) {
      if (row) {
        this.dialogVisible = true;
        this.val = val;
        this.form = {
          id: row.id,
          exclusionTime: row.exclusionTime,
          saleTime: row.saleTime,
          uploadingDay: row.uploadingDay,
          remark: row.remark,
          lotId: row.lotId,
        };
      }
    },

    cleanForm() {
      this.dialogVisible = false;
      this.saveFlag = false;
      this.form = {
        id: "",
        exclusionTime: "",
        saleTime: "",
        uploadingDay: "",
        remark: "",
        lotId: "",
      };
    },

    saveForm() {
      if (this.saveFlag) {
        return;
      }
      this.saveFlag = true;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const params = {
            ...this.form,
            exclusionTime: this.$dayjs(this.form.exclusionTime).format(
              "YYYY-MM-DD 09:30:00"
            ),
            saleTime: this.$dayjs(this.form.saleTime).format(
              "YYYY-MM-DD 09:30:00"
            ),
          };
          if (this.val === 1) {
            if (false && this.form.id) {
              updateExclusion(params)
                .then((res) => {
                  this.$message.success("修改除权成功");
                  this.cleanForm();
                  this.$emit("getData");
                })
                .finally(() => {
                  this.saveFlag = false;
                });
            } else {
              exclusion(params)
                .then((res) => {
                  this.$message.success("除权成功");
                  this.cleanForm();
                  this.$emit("getData");
                })
                .finally(() => {
                  this.saveFlag = false;
                });
            }
          }
          if (this.val === 2) {
            uploadingDay(params)
              .then((res) => {
                this.$message.success("转存期修改成功");
                this.cleanForm();
                this.$emit("getData");
              })
              .finally(() => {
                this.saveFlag = false;
              });
          }
        } else {
          this.saveFlag = false;
        }
      });
    },
  },
};
</script>

<style></style>
