<!--
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-04-07 08:07:38
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-18 16:22:15
-->
<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="60%"
    height="300"
    center
    :modal-append-to-body="false"
    :before-close="cleanForm"
    :modal="false"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div>
      <div class="topDiv">
        <el-input v-model="form.keyword" class="topInput"></el-input>
        <el-button type="primary" style="height: 36px;" @click="getData"
          >查询</el-button
        >
      </div>
      <el-table
        :data="tableData"
        ref="multipleTable"
        border
        class="divStyle"
        :row-key="getKeys"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" :reserve-selection="true">
        </el-table-column>
        <el-table-column prop="userId" label="会员ID" min-width="200">
        </el-table-column>
        <el-table-column label="会员" min-width="200">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image v-if="scope.row.headPortrait" :src="scope.row.headPortrait" fit="fill"></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.nickName }}</div>
                <div>
                  {{ scope.row.accountNumber }} 【{{ scope.row.userName }}】
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="form.current"
        :limit.sync="form.pageSize"
        @pagination="getList"
      />
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" size="small" @click="save">确 定</el-button>
      <el-button size="small" @click="cleanForm">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { getServiceUsers } from "@/api/basis/basis.js";
export default {
  name: "ThirdDialog",

  props: {
    roleId: {
      type: Number,
      default: undefined,
    },
  },

  data() {
    return {
      dialogVisible: false,
      title: "选择会员",
      tableData: [],
      val: "", //确定是第几个弹窗
      multipleSelection: [],
      secondSelect: [],
      form: {
        current: 1,
        pageSize: 10,
        keyword: "",
        roleId: undefined,
      },
      total: 0,
    };
  },

  watch: {
    tableData(val) {
      let data = this.secondSelect;
      val.forEach((item) => {
        data?.forEach((value) => {
          if (item.userId === value.userId) {
            this.$refs.multipleTable.toggleRowSelection(item);
          }
        });
      });
    },
    roleId: {
      handler(val) {
        this.form.roleId = val;
      },
      immediate: true,
    },
  },

  methods: {
    init(val, list) {
      this.dialogVisible = true;
      this.val = val;
      this.multipleSelection = list;
      this.secondSelect = list;
      this.getData();
    },

    // 分页获取数据
    getData() {
      const params = {
        ...this.form,
        current: 1,
        pageSize: 10,
      };
      getServiceUsers(params).then((res) => {
        this.tableData = res.data.records;
        this.total = res.data.total;
      });
    },

    getKeys(row) {
      return row.userId;
    },

    // 分页接口
    getList() {
      const params = {
        ...this.form,
      };
      getServiceUsers(params).then((res) => {
        this.tableData = res.data.records;
        this.total = res.data.total;
      });
    },

    handleSelectionChange(val) {
      // 当前页的userIds
      const currentUserIds = this.tableData.map(item => item.userId);
      // 当前页选中的userIds
      const selectUserIds = val.map(item => item.userId);
      // 移除当前页的数据并保留其他页的数据
      const otherPageSelections = this.multipleSelection.filter(item =>
        !currentUserIds.includes(item.userId)
      );
      // 合并当前页的选中项和其他页的选中项
      const newSelections = otherPageSelections.concat(
        this.tableData.filter(item => selectUserIds.includes(item.userId))
      );
      // 根据 userId 去重
      const uniqueSelections = [];
      const seenIds = new Set();
      for (const item of newSelections) {
        if (!seenIds.has(item.userId)) {
          seenIds.add(item.userId);
          uniqueSelections.push(item);
        }
      }
      // 更新选中项
      // 处理取消选中的项
      this.multipleSelection = uniqueSelections;
      console.log('multipleSelection', this.multipleSelection)
    },

    cleanForm() {
      this.dialogVisible = false;
      this.tableData = [];
      this.multipleSelection = [];
      this.val = "";
      this.title = "已选列表";
      this.form = {...this.$options.data().form, roleId: this.roleId};
    },

    save() {
      let data = this.multipleSelection;
      this.$emit("getData", data);
      this.cleanForm();
    },
  },
};
</script>
<style scoped lang="scss">
.divStyle {
  height: 350px;
  overflow: auto;
}
.topDiv {
  display: flex;
  margin-bottom: 10px;
}
.topInput {
  margin-right: 10px;
}
</style>
