<!--
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-04-02 13:27:19
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-20 08:20:58
-->
<template>
  <div>
    <el-form :model="form" ref="form" :rules="rules">
      <el-row>
        <el-col :span="6">
          <el-form-item label="排序" label-width="100px" prop="sorted">
            <el-input v-model="form.sorted"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="凭证代码" label-width="100px" prop="lotCode">
            <el-input
              v-model="form.lotCode"
              :disabled="isDisabled"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="凭证名称" label-width="100px" prop="lotName">
            <el-input
              v-model="form.lotName"
              :disabled="isDisabled"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="凭证照片" label-width="100px" prop="lotImg">
            <el-upload
              class="avatar-uploader"
              :action="upLoadUrl"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="form.lotImg" :src="form.lotImg" class="avatar" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <div>建议尺寸 300*300 px</div> -->
      <el-row>
        <el-col :span="6">
          <el-form-item label="当前价格" label-width="100px" prop="lotPrice">
            <el-input
              v-model="form.lotPrice"
              @input="checkPrice('lotPrice')"
              :disabled="isDisabled"
            >
              <template slot="prepend">¥</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="价格范围" label-width="100px" prop="lotPrice1">
            <el-input
              v-model="form.lotPrice1"
              @input="checkPrice('lotPrice1')"
              style="width: 95%;"
              :disabled="isDisabled"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="2">
          <el-form-item label="-" label-width="12px" prop="lotPrice2">
            <el-input
              v-model="form.lotPrice2"
              @input="checkPrice('lotPrice2')"
              :disabled="isDisabled"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="发行总量" label-width="100px" prop="lotCount">
            <el-input
              v-model="form.lotCount"
              :disabled="isDisabled"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="已消耗凭证数量" label-width="140px" prop="consumeNum">
            <el-input-number
              v-model="form.consumeNum"
              :disabled="isDisabled"
              :min="0"
              :precision="0"
              style="width: 98%"
              controls-position="right"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="项目" label-width="60px" prop="projectId">
            <el-select v-model="form.projectId" style="width: 100%" placeholder="请选择" @change="changeProject">
              <el-option
                v-for="item in projectList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商家" label-width="100px" prop="upbeatUser">
            <el-select
              v-model="form.upbeatUser"
              placeholder="选择项目后自动填充"
              style="width: 100%;"
              filterable
              :disabled="true"
              @clear="saveMemberList = upbeatList"
              :filter-method="filterMethod"
            >
              <el-option
                v-for="item in saveMemberList"
                :key="item.userId"
                :label="item.userName"
                :value="item.userId"
              >
                <span> [{{ item.userId }}]{{ item.userName }} </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="服务中心" label-width="100px">
            <el-button type="text" @click="openFirst">选择</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-table :data="form.serviceUsers" class="tableStyle" border>
      <el-table-column prop="userId" label="会员ID" min-width="200">
      </el-table-column>
      <el-table-column label="会员" min-width="200">
        <template slot-scope="scope">
          <div class="span_all">
            <div class="span_left">
              <el-image
                v-if="scope.row.headPortrait"
                :src="scope.row.headPortrait"
                fit="fill"
              ></el-image>
              <img
                v-else
                src="@/assets/images/member.png"
                alt="donate"
                width="100%"
              />
            </div>
            <div class="span_right">
              <div>{{ scope.row.nickName }}</div>
              <div>
                {{ scope.row.accountNumber }} 【{{ scope.row.userName }}】
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column min-width="200" label="操作">
        <template slot-scope="{ row }">
          <el-button
            type="text"
            style="color: red;"
            @click="deleteServiceUsers(row)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <first-dialog
      ref="firstDia"
      :memberList="memberList"
      @changeServiceUsers="changeServiceUsers"
    >
    </first-dialog>
  </div>
</template>
<script>
import FirstDialog from "./firstDialog.vue";
import {mapGetters} from "vuex";
import GetProjectList from "@/layout/mixin/GetProjectList";

export default {
  name: "FirstPage",
  mixins: [GetProjectList],
  props: {
    memberList: {
      type: Array,
      default: [],
    },
  },
  components: {
    FirstDialog,
  },
  data() {
    return {
      upLoadUrl: process.env.VUE_APP_BASE_API + "/pic/upload",
      form: {
        lotId: undefined,
        sorted: "",
        lotCode: "",
        lotName: "",
        lotImg: "",
        lotPrice: "",
        lotCount: "",
        consumeNum: "",
        lotPrice1: "",
        lotPrice2: "",
        upbeatUser: "",
        serviceUsers: [],
        projectId: "",
      },
      total: 0,
      time: "",
      resetForm: {},
      rules: {
        sorted: [{ required: true, message: "请填写排序", trigger: "change" }],
        lotCode: [
          { required: true, message: "请填写凭证代码", trigger: "change" },
        ],
        lotName: [
          { required: true, message: "请填写凭证名称", trigger: "change" },
        ],
        lotImg: [
          { required: true, message: "请填写凭证照片", trigger: "change" },
        ],
        lotPrice: [
          { required: true, message: "请填写当前价格", trigger: "change" },
        ],
        lotCount: [
          { required: true, message: "请填写发行总量", trigger: "change" },
        ],
        consumeNum: [
          { required: false, message: "请填写已消耗凭证数量", trigger: "change" },
        ],
        lotPrice1: [
          { required: true, message: "请填写起始价格", trigger: "change" },
        ],
        upbeatUser: [
          { required: true, message: "请选择商家", trigger: "change" },
        ],
        projectId: [
          { required: true, message: "请选择项目", trigger: "change" },
        ],
      },
      saveMemberList:[]
    };
  },
  watch: {
    memberList() {
      this.saveMemberList = this.upbeatList;
    }
  },
  computed: {
    ...mapGetters(['isAdmin']),
    upbeatList() {
      return this.memberList.filter(v => v.roleId === 3)
    },
    isDisabled() {
      return this.form.lotId && !this.isAdmin;
    }
  },
  methods: {
    changeProject(val){
      console.log(val);
      this.form.upbeatUser = this.projectList.find((item)=>item.id === val)?.upbeatUser
    },
    filterMethod(query) {
      console.log(query,this.upbeatList);
      if(!query){
        this.saveMemberList = this.upbeatList;
      }else {
        this.saveMemberList = this.upbeatList.filter((item) => {
          return (
            (item.nickName+'').indexOf(query) > -1 ||
            (item.accountNumber+'').indexOf(query) > -1 ||
            (item.userId+'').indexOf(query) > -1 ||
            (item.userName+'').indexOf(query) > -1
          );
        });
      }
    },

    /**
     * 返回表单的值，供父组件调用
     */
    getFormData() {
      const formData = {
        ...this.form,
      };
      return formData;
    },

    formValidate() {
      const validateRes = new Promise((resolve, reject) => {
        this.$refs["form"].validate((valid) => {
          if (valid) {
            resolve();
          } else {
            reject("first");
          }
        });
      });
      return validateRes;
    },

    indexChoose(res) {
      Object.keys(this.form).map((key) => {
        this.form[key] = res[key];
      });
      this.form.serviceUsers = res.serviceUsers;
      console.log(res.serviceUsers, res, this.form);
    },

    handleAvatarSuccess(res, file) {
      this.form.lotImg = file.response.name;
    },

    changeServiceUsers(data) {
      this.form.serviceUsers = [...new Set(data)];
    },

    beforeAvatarUpload(file) {
      const type = ["image/jpeg", "image/jpg", "image/png", "image/svg"];
      const isJPG = type.includes(file.type);
      const isLt2M = file.size / 1024 / 1024 < 10;

      if (!isJPG) {
        this.$message.error(`图片格式错误!`);
        return false;
      }
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 10MB!");
        return false;
      }
      return isJPG && isLt2M;
    },

    openFirst() {
      this.$refs.firstDia.init(this.form.serviceUsers);
    },

    deleteServiceUsers(item) {
      this.form.serviceUsers = this.form.serviceUsers.filter(
        (val) => val.userId !== item.userId
      );
    },
    checkPrice(name) {
      this.form[name] = this.form[name].replace(/[^\d|\.]/g, "");
      if (this.form[name] < 0) {
        this.form[name] = 0;
      }
      if (this.form[name] > 99999.99) {
        this.form[name] = 99999.99;
      }
      this.form[name] = this.form[name] + "";
      const decimalPointIndex = this.form[name].indexOf(".");
      if (
        decimalPointIndex !== -1 &&
        this.form[name].length - 1 !== decimalPointIndex
      ) {
        let frontValue = this.form[name].substr(0, decimalPointIndex);
        let smallValue = this.form[name].substr(decimalPointIndex + 1, 2);
        this.form[name] = frontValue + "." + smallValue.replace(/\./g, "");
      }
    },
  },
};
</script>
<style scoped>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.tableStyle {
  height: 400px;
  width: 50%;
  overflow: auto;
  margin-left: 30px;
}
</style>
