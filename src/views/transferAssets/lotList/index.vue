<!--
 * @Description: 凭证管理
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-03-22 13:13:22
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-18 08:58:36
-->
<template>
  <div class="whole">
    <el-form :model="form" ref="form">
      <el-row>
        <el-col :span="8">
          <el-form-item label="凭证代码" label-width="110px" prop="lotCode">
            <el-input v-model="form.lotCode" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="凭证标题" label-width="110px" prop="lotName">
            <el-input v-model="form.lotName" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="发布时间" label-width="110px">
            <el-date-picker
              style="width: 100%;"
              v-model="time"
              type="daterange"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="changeTime"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="状态" label-width="110px" prop="lotState">
            <el-select
              v-model="form.lotState"
              placeholder="请选择状态"
              style="width: 100%;"
            >
              <el-option label="隐藏" :value="0"></el-option>
              <el-option label="开盘" :value="1"></el-option>
              <el-option label="停盘" :value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="" label-width="43px">
            <el-button @click="reset"> 重置</el-button>
            <el-button type="primary" @click="getData">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div>
      <div class="addRight" v-if="roleId != 8">
        <el-button type="primary" @click="addMember()">新建</el-button>
        <el-button @click="exportFile">导出</el-button>
      </div>
      <el-table :data="tableData" style="width: 100%;" border v-loading="loading">
        <el-table-column prop="sorted" label="排序" min-width="50">
        </el-table-column>
        <el-table-column prop="date" label="凭证" min-width="200">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image :src="scope.row.lotImg" fit="fill"></el-image>
              </div>
              <div class="span_right">
                <div>{{ scope.row.lotName }}</div>
                <div>【{{ scope.row.lotCode }}】</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="lotPrice" label="价格" min-width="90">
          <template slot-scope="{row}">
            <span v-if="row.lotPrice">{{ row.lotPrice }}</span>
            <span v-else>-</span>
            <el-button
              type="text"
              @click="increasePrice(row)"
              v-if="$dayjs(row.saleTime) < $dayjs() && row.lotState === 1"
            >
              涨价
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="价格范围" min-width="80">
          <template slot-scope="scope">
            {{ scope.row.lotPrice1 }}-{{ scope.row.lotPrice2 }}
          </template>
        </el-table-column>
        <el-table-column prop="lotCount" label="发行总量/已消耗凭证数量" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.lotCount }}/{{ scope.row.consumeNum ||  0  }}
          </template>
        </el-table-column>
        <el-table-column prop="roundTime" label="轮次" min-width="60">
          <template slot-scope="scope">
            {{ scope.row.times }} - {{ scope.row.roundTime }}
          </template>
        </el-table-column>
        <el-table-column prop="publishTime" label="发布时间" min-width="80">
        </el-table-column>
        <el-table-column prop="lotState" label="状态" min-width="100">
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.lotState"
              placeholder="请选择状态"
              style="width: 100%;"
              :disabled="scope.row.updateStatus == 0"
              @change="changeState(scope.row)"
            >
              <el-option label="隐藏" :value="0"></el-option>
              <el-option label="开盘" :value="1"></el-option>
              <el-option label="停盘" :value="2"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="exclusionTime" label="除权日期" min-width="80">
        </el-table-column>
        <el-table-column prop="uploadingDay" label="转存期" min-width="60">
        </el-table-column>
        <el-table-column prop="saleTime" label="起售日期" min-width="80">
        </el-table-column>
        <el-table-column prop="addedFlag" label="可交易时间段">
          <template slot-scope="scope">
            <span v-if="scope.row.tradeStartTime">
              {{ scope.row.tradeStartTime? moment(scope.row.tradeStartTime,'HH:mm').format('HH:mm') : '' }} - {{ scope.row.tradeEndTime? moment(scope.row.tradeEndTime,'HH:mm').format('HH:mm') : '' }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="120">
          <template slot-scope="{ row }">
            <template v-if="roleId != 8">
              <el-button type="text" @click="editMember(row)" v-if="row.updateStatus || (userId === row.createUser) || isAdmin">
                编辑
              </el-button>
              <el-button
                type="text"
                @click="openExclusion(1, row)"
                v-if="($dayjs(row.exclusionTime) > $dayjs() || !row.exclusionTime || (row.roundTime === 200 && row.lotState === 2)) && row.exclusionStatus === 0"
              >
                <span> 除权 </span>
              </el-button>
              <el-button
                type="text"
                @click="openExclusion(2, row)"
                v-if="
                $dayjs(row.exclusionTime) < $dayjs() &&
                $dayjs(row.saleTime) > $dayjs()
              "
              >
                转存期
              </el-button>
              <el-button
                type="text"
                @click="issuance(row)"
                v-if="isProd() && row.approveStatus == 2 && isDeal()"
              >
                发行
              </el-button>
              <el-button type="text" @click="openTradingDialog(row)" v-if="moment(row.saleTime).isBefore() && row.lotState === 1">
                交易时间
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="form.current"
        :limit.sync="form.pageSize"
        @pagination="getList"
      />
    </div>
    <lot-dialog ref="lot" @getData="getList"></lot-dialog>
    <exclusion-dialog ref="exclusion" @getData="getList"></exclusion-dialog>
    <TradingDialog ref="tradingDialogRef" @getData="getList"></TradingDialog>
  </div>
</template>
<script>
import { list, updateState, increase, issuance } from "@/api/transferAssets/lotList.js";
import LotDialog from "./lotDialog.vue";
import ExclusionDialog from "./exclusionDialog.vue";
import store from "@/store";
import { isDeal } from "@/utils/utils";
import {mapGetters} from "vuex";
import TradingDialog from "@/views/transferAssets/lotList/TradingDialog.vue";
import moment from "moment";
import {isProd} from '@/utils/tool'

export default {
  name: "LotList",
  components: {
    LotDialog,
    ExclusionDialog,
    TradingDialog
  },
  data() {
    return {
      form: {
        current: 1,
        pageSize: 10,
        lotCode: "",
        lotName: "",
        publishStartTime: "",
        publishEndTime: "",
        lotState: "",
      },
      total: 0,
      time: "",
      resetForm: {},
      tableData: [],
      src:
        "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
      roleId: store.getters.roleId,
      loading: false,
    };
  },
  computed: {
    ...mapGetters(["userId", "isAdmin"]),
  },
  mounted() {
    this.resetForm = {
      ...this.form,
    };
    this.getData();
  },
  methods: {
    isProd,
    isDeal,
    moment,
    // 发行
    openTradingDialog(row){
      this.$refs.tradingDialogRef.init(row)
    },
    issuance(row) {
      this.$confirm("是否确认发行该凭证?", "发行确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const params = row.lotId;
          issuance(params).then((res) => {
            this.$message({
              type: "success",
              message: "发行成功!",
            });
            this.getList();
          });
        })
    },
    // 获取数据
    getData() {
      this.form.current = 1;
      this.form.pageSize = 10;
      this.getList();
    },

    // 分页接口
    async getList() {
      this.loading = true;
      await list(this.form).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
      }).finally(() => {
        this.loading = false;
      });
    },

    // 重置
    reset() {
      this.form = {
        ...this.resetForm,
      };
      this.time = "";
      this.getData();
    },

    // 涨价
    async increasePrice(row) {
      await this.getList()
      let selectData = this.tableData.find(item => item.lotId === row.lotId)
      let msgStr = `<span style="font-weight: 700">是否执行将“${selectData.lotName}”价格从${selectData.lotPrice}上涨至${selectData.lotPrice + (selectData.lotPrice2 - selectData.lotPrice1) / 200}？</span><br/>
                    <span style="color: red">请注意，此操作一旦执行无法撤回，并将产生重大影响,</span> 你还要继续吗？`
      this.$confirm(msgStr, "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        customClass: 'sureUpPrice',
        dangerouslyUseHTMLString: true
      })
        .then(() => {
          this.loading = true;
          const params = row.lotId;
          increase(params).then((res) => {
            this.$message({
              type: "success",
              message: "涨价成功",
            });
          }).finally(() => {
            this.getList();
          });
        })
        .catch(() => {});
    },

    // 打开除权弹窗
    openExclusion(val, row) {
      this.$refs.exclusion.init(val, row);
    },

    changeState(row) {
      this.$confirm("是否确认修改该条凭证状态?", "修改确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const params = {
            lotId: row.lotId,
            state: row.lotState,
          };
          updateState(params).then((res) => {
            this.$message({
              type: "success",
              message: "状态修改成功!",
            });
            this.getList();
          });
        }).catch(()=>{
          this.getList();
      })
    },

    // 新增凭证
    addMember() {
      this.$refs.lot.init();
    },

    // 编辑凭证
    editMember(row) {
      this.$refs.lot.init(row);
    },

    // 导出
    exportFile() {
      const params = {
        ...this.form,
      };
      this.download("/lot/export-lot-list", { ...params }, `凭证管理.xlsx`);
    },

    // 改变申请日期
    changeTime(time) {
      this.form.publishStartTime = this.$dayjs(time[0]).format(
        "YYYY-MM-DD 00:00:00"
      );

      this.form.publishEndTime = this.$dayjs(time[1]).format(
        "YYYY-MM-DD 23:59:59"
      );
    },
  },
};
</script>
<style scoped lang="scss">

.addRight {
  float: right;
  margin: 10px 0;
}
</style>
<style>
.sureUpPrice {
  .el-message-box__status{
    top: 20% !important;
  }
}
</style>
