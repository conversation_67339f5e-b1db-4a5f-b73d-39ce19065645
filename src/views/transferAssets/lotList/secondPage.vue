<!--
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-04-02 13:27:19
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-10 11:13:08
-->
<template>
  <div>
    <el-form
      :model="form"
      :rules="rules"
      ref="form"
      label-width="100px"
      class="demo-form formbox"
    >
      <el-form-item label="凭证详情" prop="lotDesc" class="hightedit">
        <editor v-model="form.lotDesc" ref="editor" :height="500" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
export default {
  name: "secondPage",
  data() {
    return {
      form: {
        lotDesc: "",
      },
      total: 0,
      time: "",
      resetForm: {},
      tableData: [],
      rules: {
        lotDesc: [
          { required: true, message: "请填写凭证详情", trigger: "change" },
        ],
      },
    };
  },

  computed: {
    styles() {
      let style = {};
      if (this.minHeight) {
        style.minHeight = `${this.minHeight}px`;
      }
      if (this.height) {
        style.height = `${this.height}px`;
      }
      return style;
    },
  },

  methods: {
    /**
     * 返回表单的值，供父组件调用
     */
    getFormData() {
      const formData = {
        ...this.form,
      };
      return formData;
    },

    formValidate() {
      const validateRes = new Promise((resolve, reject) => {
        this.$refs["form"].validate((valid) => {
          if (valid) {
            resolve();
          } else {
            reject("second");
          }
        });
      });
      return validateRes;
    },

    indexChoose(res) {
      Object.keys(this.form).map((key) => {
        this.form[key] = res[key];
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.formbox ::v-deep {
  .hightedit {
    //height: 350px;
  }
}
</style>
