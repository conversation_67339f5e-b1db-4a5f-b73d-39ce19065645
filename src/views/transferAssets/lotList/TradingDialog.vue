<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="30%"
    center
    :modal-append-to-body="false"
    :before-close="cleanForm"
    :modal="false"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <el-form ref="form" :model="form" :rules="rules">
      <el-row style="margin-top: 10px;">
        <el-col :span="24">
          <el-form-item
            label="开市时间"
            label-width="90px"
            prop="tradeStartTime"
          >
            <el-time-picker
              v-model="form.tradeStartTime"
              placeholder="选择开市时间"
              style="width: 100%;"
              format="HH:mm"
            >
            </el-time-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="收市时间" label-width="90px" prop="tradeEndTime">
            <el-time-picker
              v-model="form.tradeEndTime"
              placeholder="选择收市时间"
              style="width: 100%;"
              format="HH:mm"
            >
            </el-time-picker>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" size="small" :loading="saveFlag" @click="saveForm">确 定</el-button>
      <el-button size="small" @click="cleanForm">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from "moment";
import {update, updateTradeTime} from "@/api/transferAssets/lotList.js";
export default {
  name: "TradingDialog",
  data() {
    return {
      dialogVisible: false,
      title: "可交易时段",
      saveFlag: false,
      form: {
        tradeStartTime: undefined,
        tradeEndTime: undefined,
        lotId: "",
      },
      rules: {
        // 开市时间必须早于收市时间
        tradeStartTime: [
          { required: true, message: "请选择开市时间", trigger: "change" },
          {
            validator: (rule, value, callback) => {
              if (this.form.tradeEndTime && value) {
                // 增加开市时间不能等于收市时间的判断
                if (this.$dayjs(value).isAfter(this.form.tradeEndTime) || this.$dayjs(value).isSame(this.form.tradeEndTime)) {
                  callback(new Error("开市时间不能晚于或者等于收市时间"));
                }else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
        // 收市时间不能早于或者等于开市时间
        tradeEndTime: [
          { required: true, message: "请选择收市时间", trigger: "change" },
          {
            validator: (rule, value, callback) => {
              if (this.form.tradeStartTime && value) {
                // 增加收市时间不能等于开市时间的判断
                if (this.$dayjs(value).isBefore(this.form.tradeStartTime) || this.$dayjs(value).isSame(this.form.tradeStartTime)) {
                  callback(new Error("收市时间不能早于或者等于开市时间"));
                }else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
      },
    };
  },

  methods: {
    init(row) {
      this.dialogVisible = true;
      if (row) {
        this.form = {
          tradeStartTime: moment(row.tradeStartTime, "HH:mm"),
          tradeEndTime: moment(row.tradeEndTime, "HH:mm"),
          lotId: row.lotId,
        };
        if(!row.tradeStartTime){
            //不存在值的话 tradeStartTime 设置为09:00 tradeEndTime 设置为21:00
            this.$set(this.form, "tradeStartTime", moment("09:00", "HH:mm"));
        }
        if(!row.tradeEndTime){
            this.$set(this.form, "tradeEndTime", moment("21:00", "HH:mm"));
        }
      }
    },

    cleanForm() {
      this.dialogVisible = false;
      this.saveFlag = false;
      this.form = {
        tradeStartTime: "",
        tradeEndTime: "",
        lotId: "",
      };
    },

    saveForm() {
      if (this.saveFlag) {
        return;
      }
      this.saveFlag = true;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const params = {
            ...this.form,
            tradeStartTime: this.$dayjs(this.form.tradeStartTime).format(
              "HH:mm"
            ),
            tradeEndTime: this.$dayjs(this.form.tradeEndTime).format(
              "HH:mm"
            ),
          }
          updateTradeTime(params)
            .then((res) => {
              this.$message.success("保存成功");
              this.cleanForm();
              this.$emit("getData");
            })
            .finally(() => {
              this.saveFlag = false;
            });
        } else {
          this.saveFlag = false;
        }
      });
    },
  },
};
</script>

<style></style>
