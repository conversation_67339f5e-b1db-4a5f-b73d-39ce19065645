<template>
  <el-dialog
    title="操作明细"
    :visible.sync="dialogVisible"
    width="720"
    :close-on-click-modal="false"
    :before-close="cleanForm"
    :modal-append-to-body="false"
    :modal="false"
    destroy-on-close
    >
    <el-table :data="tableData" style="width: 100%;" border max-height="680">
      <el-table-column label="序号" type="index" align="center">
        <template slot-scope="scope">
          <span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column :resizable="false" prop="registTime" label="操作时间" min-width="120">
      </el-table-column>
      <el-table-column prop="status" label="状态" min-width="50">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 1">正常</el-tag>
          <el-tag v-if="scope.row.status === 2" type="danger">禁用</el-tag>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="form.current"
      :limit.sync="form.pageSize"
      @pagination="getList"
    />
  </el-dialog>
</template>

<script>
import {
  oneTouchDetailPageList,
} from '@/api/transferAssets/entrustedOrderPlacement';
export default {
  name: "detailDialog",
  props: {
    detailId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        current: 1,
        pageSize: 10,
      },
    };
  },
  methods: {
    open() {
      this.dialogVisible = true;
      if (this.detailId) {

      }
    },
    getData() {
      const params = {
        ...this.form,
        current: 1,
        pageSize: 10,
      };
      oneTouchDetailPageList(params).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
        this.form.current = 1;
        this.form.pageSize = 10;
      });
    },
    cleanForm() {
      this.dialogVisible = false;
    },
  },
}
</script>

<style scoped lang="less">

</style>
