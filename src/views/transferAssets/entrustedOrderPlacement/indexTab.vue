<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick" class="whole">
      <el-tab-pane label="自动委托" name="auto">
        <Auto v-if="activeName === 'auto'" />
      </el-tab-pane>
      <el-tab-pane label="一键采购" name="oneTouch">
        <OneTouch v-if="activeName === 'oneTouch'" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Auto from './index';
import OneTouch from './oneTouch';
export default {
  name: "indexTab",
  components: {
    Auto,
    OneTouch,
  },
  data() {
    return {
      activeName: 'auto'
    }
  },
  methods: {
    handleClick(tab, event) {
      this.activeName = tab.name
    }
  }
}
</script>

<style scoped>

</style>
