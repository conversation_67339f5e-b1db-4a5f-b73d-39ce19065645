<template>
  <div class="whole">
    <el-form :model="form" ref="form">
      <el-row>
        <el-col :span="8">
          <el-form-item label="凭证代码" label-width="110px" prop="lotCode">
            <el-input v-model="form.lotCode"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item
            label="会员ID/昵称/手机号/姓名"
            label-width="180px"
            prop="userName"
          >
            <el-input v-model="form.keyword"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="所属项目" label-width="110px" prop="type">
            <el-select v-model="form.projectId" style="width: 100%" placeholder="请选择">
              <el-option
                v-for="item in projectList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="状态" label-width="110px" prop="type">
            <el-select
              v-model="form.status"
              placeholder="请选择状态"
              style="width: 100%;"
            >
              <el-option v-for="item in Object.keys(entrustedOrderStatusEnum)" :key="item" :label="entrustedOrderStatusEnum[item]" :value="item"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="" label-width="15px">
            <el-button @click="reset"> 重置 </el-button>
            <el-button type="primary" @click="getData">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div>
      <el-table :data="tableData" style="width: 100%;" border>
        <el-table-column prop="userId" label="会员ID" width="120">
        </el-table-column>
        <el-table-column prop="date" label="会员" min-width="150">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image
                  v-if="scope.row.headPortrait"
                  :src="scope.row.headPortrait"
                  fit="fill"
                ></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.nickName }}</div>
                <div>
                  {{ scope.row.accountNumber }} 【{{ scope.row.userName }}】
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="凭证" min-width="150">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image :src="scope.row.lotImg" fit="fill"></el-image>
              </div>
              <div class="span_right">
                <div>{{ scope.row.lotName }}</div>
                <div>【{{ scope.row.lotCode }}】</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="所属项目" min-width="100">
          <template slot-scope="scope">
            {{ scope.row.projectName || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 2 ? 'info' : 'default'">
              <span>{{ scope.row.statusName }}</span>
            </el-tag>
          </template>
        </el-table-column>
<!--        <el-table-column prop="name" label="操作" min-width="50">
          <template slot-scope="{ row }">
            <el-button type="text" @click="handleDetail(row)">
              明细
            </el-button>
          </template>
        </el-table-column>-->
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="form.current"
        :limit.sync="form.pageSize"
        @pagination="getList"
      />
    </div>
    <DetailDialog ref="detailDialog" />
  </div>
</template>
<script>
import {
  entrustPageList,
  deleteEntrust,
  oneTouchPageList,
} from "@/api/transferAssets/entrustedOrderPlacement.js";
import DetailDialog from "./detailDialog";
import GetProjectList from "@/layout/mixin/GetProjectList";
import { entrustedOrderStatusEnum } from "@/utils/enumeration";

export default {
  name: "OneTouch",
  mixins: [GetProjectList],
  components: {
    DetailDialog,
  },
  dicts: ["trade_type"],
  data() {
    return {
      entrustedOrderStatusEnum: entrustedOrderStatusEnum,
      form: {
        current: 1,
        pageSize: 10,
        lotCode: "",
        keyword: "",
        projectId: "",
        status: "",
      },
      resetForm: {},
      tableData: [],
      total: 0,
    };
  },
  computed: {
    filterType() {
      return this.dict.type.trade_type.filter(item => item.label !== '商家')
    },
  },
  mounted() {
    this.resetForm = {
      ...this.form,
    };
    this.getData();
    this.getProjectData();
  },
  methods: {
    handleDetail ({id}) {

    },
    // 获取数据
    getData() {
      const params = {
        ...this.form,
        current: 1,
        pageSize: 10,
      };
      oneTouchPageList(params).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
        this.form.current = 1;
        this.form.pageSize = 10;
      });
    },

    // 重置
    reset() {
      this.form = {
        ...this.resetForm,
      };
      this.getData();
    },

    // 分页 直接按queryForm 开始查询
    getList() {
      const params = {
        ...this.form,
      };
      oneTouchPageList(params).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
      });
    },

    // 导出
    exportFile() {},
  },
};
</script>
<style scoped lang="scss">
.addRight {
  float: right;
  margin: 10px 0;
}
.toClear {
  clear: both;
}
.numberStyle {
  background: #e7f8ff;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  border: 1px solid #e3f4ff;
  margin-bottom: 10px;
}
</style>
