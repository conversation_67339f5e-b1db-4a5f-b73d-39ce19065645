<!--
 * @Description: 凭证管理
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-03-22 13:13:22
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-14 19:59:53
-->
<template>
  <div class="whole">
    <el-form :model="form" ref="form">
      <el-row>
        <el-col :span="8">
          <el-form-item label="凭证代码" label-width="110px" prop="lotCode">
            <el-input v-model="form.lotCode"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item
            label="会员ID/昵称/手机号/姓名"
            label-width="180px"
            prop="userName"
          >
            <el-input v-model="form.userName"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="(委托对象)会员ID/昵称/手机号/姓名"
            label-width="250px"
            prop="entrustUser"
          >
            <el-input v-model="form.entrustUser"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="类型" label-width="110px" prop="type">
            <el-select
              v-model="form.type"
              placeholder="请选择类型"
              style="width: 100%;"
            >
              <el-option
                v-for="dict in filterType"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="状态" label-width="110px" prop="status">
            <el-select
              v-model="form.status"
              placeholder="请选择状态"
              style="width: 100%;"
            >
              <el-option v-for="item in Object.keys(entrustedOrderStatusEnum)" :key="item" :label="entrustedOrderStatusEnum[item]" :value="item"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="" label-width="15px">
            <el-button @click="reset"> 重置 </el-button>
            <el-button type="primary" @click="getData">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div>
      <el-table :data="tableData" style="width: 100%;" border>
        <el-table-column prop="createTime" label="发生时间" min-width="100">
        </el-table-column>
        <el-table-column prop="date" label="会员" min-width="150">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image v-if="scope.row.avatar" :src="scope.row.avatar" fit="fill"></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.nickName }}  <span >({{ scope.row.userId}})</span></div>
                <div>{{ scope.row.userName }} 【{{ scope.row.realName }}】</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="委托对象" min-width="150">
          <template slot-scope="scope">
            <div class="span_all" v-if="scope.row.entrustRealName">
              <div class="span_left">
                <el-image v-if="scope.row.entrustAvatar" :src="scope.row.entrustAvatar" fit="fill"></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.entrustNickName }} <span >({{ scope.row.entrustUserId}})</span></div>
                <div>
                  {{ scope.row.entrustUserName }} 【{{
                    scope.row.entrustRealName
                  }}】
                </div>
              </div>
            </div>
            <div class="span_all" v-else>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="凭证" min-width="150">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image :src="scope.row.lotImg" fit="fill"></el-image>
              </div>
              <div class="span_right">
                <div>{{ scope.row.lotName }}</div>
                <div>【{{ scope.row.lotCode }}】</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="lotCount" label="已成交数量" min-width="120">
        </el-table-column>
        <el-table-column prop="saleTypeName" label="类型" min-width="100">
        </el-table-column>
        <el-table-column label="资产" min-width="100">
          可用凭证
        </el-table-column>
        <el-table-column prop="name" label="操作" min-width="100">
          <template slot-scope="{ row }">
            <div v-if="row.status == 4">
              已完成
            </div>
            <el-button v-else-if="row.status !== 3" type="text" @click="deleteItem(row)">
              删除
            </el-button>
            <div v-else>
              已撤回
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="form.current"
        :limit.sync="form.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>
<script>
import {
  entrustPageList,
  deleteEntrust,
} from "@/api/transferAssets/entrustedOrderPlacement.js";
import { entrustedOrderStatusEnum } from "@/utils/enumeration";
export default {
  name: "EntrustedOrderPlacement",
  dicts: ["trade_type"],
  data() {
    return {
      entrustedOrderStatusEnum: entrustedOrderStatusEnum,
      form: {
        current: 1,
        pageSize: 10,
        lotCode: "",
        userName: "",
        entrustUser: "",
        type: "",
        status: "",
      },
      resetForm: {},
      tableData: [],
      total: 0,
    };
  },
  computed: {
    filterType() {
      return this.dict.type.trade_type.filter(item => item.label !== '商家')
    },
  },
  mounted() {
    this.resetForm = {
      ...this.form,
    };
    this.getData();
  },
  methods: {
    // 获取数据
    getData() {
      const params = {
        ...this.form,
        current: 1,
        pageSize: 10,
      };
      entrustPageList(params).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
        this.form.current = 1;
        this.form.pageSize = 10;
      });
    },

    // 重置
    reset() {
      this.form = {
        ...this.resetForm,
      };
      this.getData();
    },

    // 禁用
    deleteItem(row) {
      this.$confirm("是否确认删除该条记录?", "删除确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const params = row.id;
          deleteEntrust(params).then((res) => {
            this.$message({
              type: "success",
              message: "撤回成功!",
            });
            this.getData();
          });
        })
        .catch(() => {});
    },

    // 分页 直接按queryForm 开始查询
    getList() {
      const params = {
        ...this.form,
      };
      entrustPageList(params).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
      });
    },

    // 导出
    exportFile() {},
  },
};
</script>
<style scoped lang="scss">
.addRight {
  float: right;
  margin: 10px 0;
}
.toClear {
  clear: both;
}
.numberStyle {
  background: #e7f8ff;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  border: 1px solid #e3f4ff;
  margin-bottom: 10px;
}
</style>
