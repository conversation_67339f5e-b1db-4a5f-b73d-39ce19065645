<template>
  <div class="whole">
    <el-form :model="form" ref="form">
      <el-row>
        <el-col :span="6">
          <el-form-item label="会员ID/昵称/手机号/姓名" label-width="180px">
            <el-input v-model="form.keyword"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="账号状态" label-width="110px">
            <el-select
              v-model="form.status"
              placeholder="请选择状态"
              style="width: 100%;"
            >
              <el-option label="正常" value="1"></el-option>
              <el-option label="禁用" value="2"></el-option>
              <el-option label="注销" value="3"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="申请时间" label-width="110px">
            <el-date-picker
              style="width: 100%;"
              v-model="time"
              type="daterange"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="changeTime"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="" label-width="24px">
            <el-button @click="reset"> 重置</el-button>
            <el-button type="primary" @click="getData">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div>
      <el-tabs v-model="activeName" @tab-click="handleTabClick">
        <el-tab-pane label="全部" name="0">
          <el-table :data="tableData" style="width: 100%;" border>
            <el-table-column prop="id" label="会员ID" min-width="60">
            </el-table-column>
            <el-table-column prop="date" label="会员" min-width="150">
              <template slot-scope="scope">
                <div class="span_all">
                  <div class="span_left">
                    <el-image
                      v-if="scope.row.headPortrait"
                      :src="scope.row.headPortrait"
                      fit="fill"
                    ></el-image>
                    <img
                      v-else
                      src="@/assets/images/member.png"
                      alt="donate"
                      width="100%"
                    />
                  </div>
                  <div class="span_right">
                    <div>{{ scope.row.nickName }}</div>
                    <div>
                      {{ scope.row.accountNumber }} 【{{ scope.row.userName }}】
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="balance" label="余额" min-width="80"></el-table-column>
            <el-table-column prop="lotCount" label="总资产" min-width="80"></el-table-column>
            <el-table-column prop="cancelStatus" label="审核状态" min-width="52">
              <template slot-scope="scope">
                <span v-if="!scope.row.cancelStatus"></span>
                <el-tag v-if="scope.row.cancelStatus === 1">
                  <span v-if="scope.row.cancelStatus === 1">待审核</span>
                </el-tag>
                <el-tag v-if="scope.row.cancelStatus === 2" type="success">
                  <span v-if="scope.row.cancelStatus === 2">审核成功</span>
                </el-tag>
                <el-tag v-if="scope.row.cancelStatus === 3" type="danger">
                  <span v-if="scope.row.cancelStatus === 3">审核拒绝</span>
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="账号状态" min-width="50">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status === 1">
                  <span v-if="scope.row.status === 1"> 正常</span></el-tag>
                <el-tag v-if="scope.row.status === 2" type="danger">
                  <span v-if="scope.row.status === 2"> 禁用</span></el-tag>
                <el-tag v-if="scope.row.status === 3" type="info">
                  <span v-if="scope.row.status === 3">注销</span></el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="cancelTime" label="申请时间" min-width="120"></el-table-column>
            <el-table-column prop="cancelReason" label="驳回原因" min-width="120" :show-overflow-tooltip="true"></el-table-column>
            <el-table-column label="操作" min-width="100">
              <template slot-scope="{ row }">
                <template v-if="roleId != 8 && row.cancelStatus === 1">
                  <el-button type="text" @click="changCancelstatus(row)">
                    通过
                  </el-button>
                  <el-button type="text" @click="handleReject(row)">
                    驳回
                  </el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="form.pageNum"
            :limit.sync="form.pageSize"
            @pagination="getList"
          />
        </el-tab-pane>
        <el-tab-pane label="待审核" name="1">
          <el-table :data="tableData" style="width: 100%;" border>
            <el-table-column prop="id" label="会员ID" min-width="60">
            </el-table-column>
            <el-table-column prop="date" label="会员" min-width="150">
              <template slot-scope="scope">
                <div class="span_all">
                  <div class="span_left">
                    <el-image
                      v-if="scope.row.headPortrait"
                      :src="scope.row.headPortrait"
                      fit="fill"
                    ></el-image>
                    <img
                      v-else
                      src="@/assets/images/member.png"
                      alt="donate"
                      width="100%"
                    />
                  </div>
                  <div class="span_right">
                    <div>{{ scope.row.nickName }}</div>
                    <div>
                      {{ scope.row.accountNumber }} 【{{ scope.row.userName }}】
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="balance" label="余额" min-width="80"></el-table-column>
            <el-table-column prop="lotCount" label="总资产" min-width="80"></el-table-column>
            <el-table-column prop="cancelStatus" label="审核状态" min-width="52">
              <template slot-scope="scope">
                <span v-if="!scope.row.cancelStatus"></span>
                <el-tag v-if="scope.row.cancelStatus === 1">
                  <span v-if="scope.row.cancelStatus === 1">待审核</span>
                </el-tag>
                <el-tag v-if="scope.row.cancelStatus === 2" type="success">
                  <span v-if="scope.row.cancelStatus === 2">审核成功</span>
                </el-tag>
                <el-tag v-if="scope.row.cancelStatus === 3" type="danger">
                  <span v-if="scope.row.cancelStatus === 3">审核拒绝</span>
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="账号状态" min-width="50">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status === 1">
                  <span v-if="scope.row.status === 1"> 正常</span></el-tag>
                <el-tag v-if="scope.row.status === 2" type="danger">
                  <span v-if="scope.row.status === 2"> 禁用</span></el-tag>
                <el-tag v-if="scope.row.status === 3" type="info">
                  <span v-if="scope.row.status === 3">注销</span></el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="cancelTime" label="申请时间" min-width="120"></el-table-column>
            <el-table-column prop="cancelReason" label="驳回原因" min-width="120" :show-overflow-tooltip="true"></el-table-column>
            <el-table-column label="操作" min-width="100">
              <template slot-scope="{ row }">
                <template v-if="roleId != 8">
                  <el-button type="text" @click="changCancelstatus(row)">
                    通过
                  </el-button>
                  <el-button type="text" @click="handleReject(row)">
                    驳回
                  </el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="form.pageNum"
            :limit.sync="form.pageSize"
            @pagination="getList"
          />
        </el-tab-pane>
        <el-tab-pane label="审核通过" name="2">
          <el-table :data="tableData" style="width: 100%;" border>
            <el-table-column prop="id" label="会员ID" min-width="60">
            </el-table-column>
            <el-table-column prop="date" label="会员" min-width="150">
              <template slot-scope="scope">
                <div class="span_all">
                  <div class="span_left">
                    <el-image
                      v-if="scope.row.headPortrait"
                      :src="scope.row.headPortrait"
                      fit="fill"
                    ></el-image>
                    <img
                      v-else
                      src="@/assets/images/member.png"
                      alt="donate"
                      width="100%"
                    />
                  </div>
                  <div class="span_right">
                    <div>{{ scope.row.nickName }}</div>
                    <div>
                      {{ scope.row.accountNumber }} 【{{ scope.row.userName }}】
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="balance" label="余额" min-width="80"></el-table-column>
            <el-table-column prop="lotCount" label="总资产" min-width="80"></el-table-column>
            <el-table-column prop="cancelStatus" label="审核状态" min-width="52">
              <template slot-scope="scope">
                <span v-if="!scope.row.cancelStatus"></span>
                <el-tag v-if="scope.row.cancelStatus === 1">
                  <span v-if="scope.row.cancelStatus === 1">待审核</span>
                </el-tag>
                <el-tag v-if="scope.row.cancelStatus === 2" type="success">
                  <span v-if="scope.row.cancelStatus === 2">审核成功</span>
                </el-tag>
                <el-tag v-if="scope.row.cancelStatus === 3" type="danger">
                  <span v-if="scope.row.cancelStatus === 3">审核拒绝</span>
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="账号状态" min-width="50">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status === 1">
                  <span v-if="scope.row.status === 1"> 正常</span></el-tag>
                <el-tag v-if="scope.row.status === 2" type="danger">
                  <span v-if="scope.row.status === 2"> 禁用</span></el-tag>
                <el-tag v-if="scope.row.status === 3" type="info">
                  <span v-if="scope.row.status === 3">注销</span></el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="cancelTime" label="申请时间" min-width="120"></el-table-column>
            <el-table-column prop="cancelReason" label="驳回原因" min-width="120" :show-overflow-tooltip="true"></el-table-column>
            <el-table-column label="操作" min-width="100">
              <template slot-scope="{ row }">
                <span>-</span>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="form.pageNum"
            :limit.sync="form.pageSize"
            @pagination="getList"
          />
        </el-tab-pane>
        <el-tab-pane label="审核未通过" name="3">
          <el-table :data="tableData" style="width: 100%;" border>
            <el-table-column prop="id" label="会员ID" min-width="60">
            </el-table-column>
            <el-table-column prop="date" label="会员" min-width="150">
              <template slot-scope="scope">
                <div class="span_all">
                  <div class="span_left">
                    <el-image
                      v-if="scope.row.headPortrait"
                      :src="scope.row.headPortrait"
                      fit="fill"
                    ></el-image>
                    <img
                      v-else
                      src="@/assets/images/member.png"
                      alt="donate"
                      width="100%"
                    />
                  </div>
                  <div class="span_right">
                    <div>{{ scope.row.nickName }}</div>
                    <div>
                      {{ scope.row.accountNumber }} 【{{ scope.row.userName }}】
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="balance" label="余额" min-width="80"></el-table-column>
            <el-table-column prop="lotCount" label="总资产" min-width="80"></el-table-column>
            <el-table-column prop="cancelStatus" label="审核状态" min-width="52">
              <template slot-scope="scope">
                <span v-if="!scope.row.cancelStatus"></span>
                <el-tag v-if="scope.row.cancelStatus === 1">
                  <span v-if="scope.row.cancelStatus === 1">待审核</span>
                </el-tag>
                <el-tag v-if="scope.row.cancelStatus === 2" type="success">
                  <span v-if="scope.row.cancelStatus === 2">审核成功</span>
                </el-tag>
                <el-tag v-if="scope.row.cancelStatus === 3" type="danger">
                  <span v-if="scope.row.cancelStatus === 3">审核拒绝</span>
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="账号状态" min-width="50">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status === 1">
                  <span v-if="scope.row.status === 1"> 正常</span></el-tag>
                <el-tag v-if="scope.row.status === 2" type="danger">
                  <span v-if="scope.row.status === 2"> 禁用</span></el-tag>
                <el-tag v-if="scope.row.status === 3" type="info">
                  <span v-if="scope.row.status === 3">注销</span></el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="cancelTime" label="申请时间" min-width="120"></el-table-column>
            <el-table-column prop="cancelReason" label="驳回原因" min-width="120" :show-overflow-tooltip="true"></el-table-column>
            <el-table-column label="操作" min-width="100">
              <template slot-scope="{ row }">
                <span>-</span>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="form.pageNum"
            :limit.sync="form.pageSize"
            @pagination="getList"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
    <RejectModal ref="rejectModal" @getData="getData" />
  </div>
</template>
<script>
  import {queryAll, cancelApprove} from "@/api/basis/logoutApply.js";
  import store from "@/store";
  import RejectModal from "./reject";

  export default {
    name: "LogoutApplyList",
    components: {
      RejectModal,
    },
    data() {
      return {
        form: {
          pageNum: 1,
          pageSize: 10,
          approveEndTime: "",
          approveStartTime: "",
          status: "",
          keyword: "",
        },
        total: 0,
        time: "",
        roleData: [],
        resetForm: {},
        tableData: [],
        src: "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
        sonTableData: [],
        roleId: store.getters.roleId,
        activeName: "0",
      };
    },
    mounted() {
      this.resetForm = {
        ...this.form,
      };
      this.getData();
    },
    methods: {
      handleReject({id}) {
        this.$refs.rejectModal.setMemberId(id)
        this.$refs.rejectModal.show()
      },
      changCancelstatus({id}) {
        this.$modal
          .confirm('是否确认通过？')
          .then(function () {
            return cancelApprove({memberId: id, status: 2});
          })
          .then(() => {
            this.getData();
            this.$modal.msgSuccess("审核成功");
          })
          .catch(() => { });
      },
      handleTabClick(tab, event) {
        this.activeName = tab._props.name
        this.getData()
      },
      // 获取数据
      getData() {
        const params = {
          ...this.form,
          pageNum: 1,
          pageSize: 10,
          cancelStatus: this.activeName == 0 ? "" : this.activeName,
        };
        console.log(params)
        queryAll(params).then((res) => {
          this.tableData = res.data.records;
          this.total = res.data.total;
          this.form.pageNum = 1;
        });
      },

      // 分页接口
      getList() {
        const params = {
          ...this.form,
          cancelStatus: this.activeName == 0 ? "" : this.activeName,
        };
        queryAll(params).then((res) => {
          this.tableData = res.data.records;
          this.total = res.data.total;
        });
      },

      // 重置
      reset() {
        this.form = {
          ...this.resetForm,
        };
        this.time = "";
        this.getData();
      },

      // 禁用
      disableMember(row) {
        if (row.status === 1) {
          this.$confirm("是否确认禁用该条记录?", "禁用确认", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              const params = {
                id: row.id,
                status: 2,
              };
              updateStatus(params).then((res) => {
                this.$message({
                  type: "success",
                  message: "禁用成功!",
                });
                this.getData();
              });
            })
            .catch(() => {
            });
        } else {
          this.$confirm("是否确认启用该记录?", "启用确认", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              const params = {
                id: row.id,
                status: 1,
              };
              updateStatus(params).then((res) => {
                this.$message({
                  type: "success",
                  message: "启用成功!",
                });
                this.getData();
              });
            })
            .catch(() => {
            });
        }
      },

      // 改变申请日期
      changeTime(time) {
        if (!time) {
          Object.assign(this.form, {
            approveStartTime: '',
            approveEndTime: '',
          });
          return
        }
        this.form.approveStartTime = this.$dayjs(this.time[0]).format(
          "YYYY-MM-DD 00:00:00"
        );
        this.form.approveEndTime = this.$dayjs(this.time[1]).format(
          "YYYY-MM-DD 23:59:59"
        );
      },
    },
  };
</script>
<style scoped lang="scss">
  .addRight {
    float: right;
    margin: 10px 0;
  }
</style>
