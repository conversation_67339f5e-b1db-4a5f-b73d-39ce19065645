<template>
  <!-- 创建表 -->
  <el-dialog title="请填写驳回原因" :visible.sync="visible" width="30%" append-to-body :close-on-click-modal="false">
    <el-input type="textarea" :rows="3" placeholder="请输入" v-model="content"></el-input>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleOk">确 定</el-button>
      <el-button @click="close">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {cancelApprove} from "@/api/basis/logoutApply.js";
export default {
  data() {
    return {
      // 遮罩层
      visible: false,
      memberId: "",
      // 文本内容
      content: ""
    };
  },
  methods: {
    // 显示弹框
    show() {
      this.visible = true;
    },
    close() {
      this.visible = false;
      this.content = "";
    },
    setMemberId(memberId) {
      this.memberId = memberId;
    },
    handleOk() {
      const params = {
        memberId: this.memberId,
        status: 3
      }
      if (this.content) {
        params.reason = this.content
      }
      cancelApprove(params).then((res) => {
        if (res.code === 200) {
          this.visible = false;
          this.$modal.msgSuccess("审核成功");
          this.$emit("getData");
        }
      });
    }
  }
};
</script>
