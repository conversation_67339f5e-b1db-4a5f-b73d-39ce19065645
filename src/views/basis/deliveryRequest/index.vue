<!--
 * @Description: 提货申请
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-03-22 13:13:22
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-14 19:53:53
-->
<template>
  <div class="whole">
    <el-form :model="form" ref="form">
      <el-row>
        <el-col :span="6">
          <el-form-item label="会员ID/昵称/手机号/姓名" label-width="180px">
            <el-input v-model="form.keyword"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="申请时间" label-width="110px">
            <el-date-picker
              style="width: 100%;"
              v-model="time"
              type="daterange"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="changeTime"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
<!--        <el-col :span="6">-->
<!--          <el-form-item label="凭证ID" label-width="110px" prop="lotCode">-->
<!--            <el-input v-model="form.lotCode"></el-input>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
        <el-col :span="6">
          <el-form-item label="提货申请单号" label-width="110px" prop="oddNumber">
            <el-input v-model="form.oddNumber"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="状态" label-width="110px" prop="status">
            <el-select
              v-model="form.status"
              placeholder="请选择状态"
              style="width: 100%;"
            >
              <el-option label="待审核" value="1"></el-option>
              <el-option label="审核通过" value="2"></el-option>
              <el-option label="已完成" value="3"></el-option>
              <el-option label="驳回" value="4"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="所属项目" label-width="82px" prop="projectName">
            <el-input v-model="form.projectName"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>

        <el-col :span="6">
          <el-form-item label="" label-width="24px">
            <el-button @click="reset"> 重置 </el-button>
            <el-button type="primary" @click="getData">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>
    <div>
      <div class="addRight">
        <el-button @click="exportFile">导出</el-button>
      </div>
      <div class="toClear"></div>
      <div class="numberStyle">
        总数量: <span style="color: red;">{{ totalList.totalNum }}</span> 总额:
        <span style="color: red;">{{ totalList.totalCount }}</span>
      </div>
      <el-table :data="tableData" style="width: 100%;" border>
        <el-table-column prop="oddNumber" label="提货申请单号" min-width="200">
        </el-table-column>
        <el-table-column prop="goodsOrderId" label="订单单号" min-width="200">
          <template slot-scope="scope">
            <div>{{ scope.row.goodsOrderId || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="会员" min-width="250">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image v-if="scope.row.headPortrait" :src="scope.row.headPortrait" fit="fill"></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.nickName }}</div>
                <div>
                  {{ scope.row.accountNumber }} 【{{ scope.row.userName }}】
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="商品" prop="shoppz" width="220">
          <template slot-scope="scope">
            <div class="span_all" v-if="scope.row.goodsName ">
              <div class="span_left">
                <img
                  v-if="scope.row.goodsImg"
                  :src="scope.row.goodsImg"
                  alt=""
                />
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.goodsName }}</div>
              </div>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column prop="projectName" label="所属项目" width="200"/>
<!--        <el-table-column prop="date" label="凭证" min-width="250">-->
<!--          <template slot-scope="scope">-->
<!--            <div class="span_all">-->
<!--              <div class="span_left">-->
<!--                <el-image :src="scope.row.lotImg" fit="fill"></el-image>-->
<!--              </div>-->
<!--              <div class="span_right">-->
<!--                <div>{{ scope.row.lotName }}</div>-->
<!--                <div>【{{ scope.row.lotCode }}】</div>-->
<!--              </div>-->
<!--            </div>-->
<!--          </template>-->
<!--        </el-table-column>-->

        <el-table-column prop="date" label="提货金" min-width="150">
          <template slot-scope="scope">
            <div v-if="scope.row.deliveryAmount && Number(scope.row.deliveryAmount) >=0">
              {{ scope.row.goodsCount || '-' }}*{{ scope.row.goodsPrice || '-' }}* {{ scope.row.deliveryFee || '-'}}%={{
                scope.row.deliveryAmount
              }}
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column prop="serviceFee" label="交易手续费" width="110">
        </el-table-column>
        <el-table-column prop="date" label="收货人" min-width="150">
          <template slot-scope="scope">
            <div>
              <div>
                {{ scope.row.receivingName }} {{ scope.row.receivingPhone }}
              </div>
              <div>{{ scope.row.receivingArea }}</div>
            </div>
          </template>
        </el-table-column>
<!--        <el-table-column prop="property" label="资产" width="110">-->
<!--        </el-table-column>-->
        <el-table-column prop="detailedAddress" label="详细地址" min-width="150">
          <template slot-scope="scope">
            <div> {{ scope.row.province }}{{ scope.row.city }}{{ scope.row.area }}{{ scope.row.street }}{{ scope.row.detailedAddress }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === 1">
              <span v-if="scope.row.status === 1"> 待审核</span></el-tag
            >
            <el-tag v-if="scope.row.status === 2">
              <span v-if="scope.row.status === 2"> 审核通过</span></el-tag
            >
            <el-tag v-if="scope.row.status === 3">
              <span v-if="scope.row.status === 3"> 已完成</span></el-tag
            >
            <el-tag v-if="scope.row.status === 4" type="danger">
              <span v-if="scope.row.status === 4"> 驳回</span></el-tag
            >
          </template>
        </el-table-column>
        <el-table-column prop="applyTime" label="发生时间" width="110">
        </el-table-column>
        <el-table-column prop="name" label="操作" width="80" fixed="right">
          <template slot-scope="{ row }">
<!--            <el-button-->
<!--              type="text"-->
<!--              v-if="row.status === 2"-->
<!--              @click="delivery(row)"-->
<!--            >-->
<!--              发货-->
<!--            </el-button>-->

            <el-button
              type="text"
              v-if="row.status === 1"
              @click="auditDelivery(row)"
            >
              审核
            </el-button>

            <el-button
              type="text"
              v-if="row.status === 4"
              @click="checkReasons(row)"
            >
              查看原因
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="form.pageNum"
        :limit.sync="form.pageSize"
        @pagination="getList"
      />
    </div>
    <delivery-auditing @getData="getData" ref="delivery"></delivery-auditing>
  </div>
</template>
<script>
import {
  queryAllDelivery,
  queryTotal,
  editDelivery,
} from "@/api/basis/basis.js";
import DeliveryAuditing from "./deliveryAuditing.vue";
export default {
  name: "DeliveryRequest",
  components: {
    DeliveryAuditing,
  },
  data() {
    return {
      form: {
        pageNum: 1,
        pageSize: 10,
        applyEndTime: "",
        applyStartTime: "",
        keyword: "",
        lotCode: "",
        oddNumber: "",
        status: "",
        projectName: "",
      },
      time: "",
      resetForm: {},
      tableData: [],
      totalList: {
        totalCount: "",
        totalNum: "",
      },
      total: 0,
    };
  },
  mounted() {
    this.resetForm = {
      ...this.form,
    };
    this.getData();
  },
  methods: {
    // 获取数据
    getData() {
      const params = {
        ...this.form,
        pageNum: 1,
        pageSize: 10,
      };
      queryAllDelivery(params).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
        this.form.pageNum = 1;
      });
      queryTotal(params).then((res) => {
        this.totalList.totalCount = res.totalCount;
        this.totalList.totalNum = res.totalNum;
      });
    },

    // 重置
    reset() {
      this.form = {
        ...this.resetForm,
      };
      this.time = "";
      this.getData();
    },

    // 分页 直接按queryForm 开始查询
    getList() {
      const params = {
        ...this.form,
      };
      queryAllDelivery(params).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
      });
    },

    // 改变时间
    changeTime() {
      this.form.applyStartTime = this.$dayjs(this.time[0]).format(
        "YYYY-MM-DD 00:00:00"
      );
      this.form.applyEndTime = this.$dayjs(this.time[1]).format(
        "YYYY-MM-DD 23:59:59"
      );
    },

    // 导出
    exportFile() {
      const params = {
        ...this.form,
      };
      this.download(
        "/eazyDeliveryInformation/export",
        { ...params },
        `提货申请.xlsx`
      );
    },

    // 打开审核弹窗
    auditDelivery(row) {
      this.$refs.delivery.init(row, 1);
    },

    checkReasons(row) {
      this.$refs.delivery.init(row, 2);
    },

    delivery(row) {
      this.$confirm("是否确认发货?", "发货确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const params = {
            ...row,
            status: 3,
          };
          editDelivery(params).then((res) => {
            this.$message({
              type: "success",
              message: "发货成功!",
            });
            this.getData();
          });
        })
        .catch(() => {});
    },
  },
};
</script>
<style scoped lang="scss">
.addRight {
  float: right;
  margin: 10px 0;
}
.toClear {
  clear: both;
}
.numberStyle {
  background: #e7f8ff;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  border: 1px solid #e3f4ff;
  margin-bottom: 10px;
}
</style>
