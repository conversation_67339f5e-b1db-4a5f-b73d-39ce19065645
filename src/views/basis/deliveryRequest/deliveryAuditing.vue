<!--
 * @Description: 审核弹窗
 * @version:
 * @Author: 孙姜
 * @Date: 2023-07-17 16:15:22
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-03-29 15:27:47
-->
<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="30%"
    center
    :modal-append-to-body="false"
    :before-close="cleanForm"
    :close-on-click-modal="false"
    :modal="false"
    destroy-on-close
    top="9vh"
  >
    <el-form ref="form" :model="form" :rules="rules">
      <el-row style="margin-top: 10px;">
        <el-col :span="24">
          <el-form-item
            label="是否通过"
            label-width="85px"
            prop="status"
            v-if="flag === 1"
          >
            <el-select
              v-model="form.status"
              placeholder="请选择是否通过"
              style="width: 100%;"
              @change="changeStatus"
            >
              <el-option label="通过" :value="2"></el-option>
              <el-option label="驳回" :value="4"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="驳回原因" label-width="85px" prop="returnReason">
            <el-input
              :readonly="form.status === 2 ||  flag === 2"
              v-model="form.returnReason"
              type="textarea"
              placeholder="驳回原因"
              maxlength="100"
              show-word-limit
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" size="small" @click="saveForm" v-if="flag === 1"
        >确 定</el-button
      >
      <el-button size="small" @click="cleanForm" v-if="flag === 1"
        >取 消</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { editDelivery } from "@/api/basis/basis.js";
export default {
  name: "DeliveryAuditing",
  data() {
    return {
      dialogVisible: false,
      title: "审核",
      saveFlag: false,
      flag: "",
      form: {
        id: "",
        status: undefined,
      },
    };
  },

  computed: {
    rules() {
      return {
        status: [
          { required: true, message: "请选择是否通过", trigger: "change" },
        ],
        returnReason: [
          {
            required: this.form.status !== 2,
            message: "请输入驳回原因",
            trigger: "change",
            whitespace: true
          },
        ],
      };
    },
  },

  methods: {
    changeStatus() {
     this.form.returnReason = "";
    },
    init(row, check) {
      if (check === 1) {
        this.dialogVisible = true;
        this.form = { ...row };
        this.form.status = undefined;
        this.flag = check;
      } else {
        this.dialogVisible = true;
        this.form = { ...row };
        this.flag = check;
      }
      setTimeout(()=>{
        this.$refs["form"].clearValidate();
      }, 400)
      // this.$refs["form"].clearValidate();
    },

    cleanForm() {
      this.dialogVisible = false;
      this.saveFlag = false;
      this.flag = "";
      this.form = {
        id: "",
      };
    },

    saveForm() {
      if (this.saveFlag) {
        return;
      }
      this.saveFlag = true;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const params = {
            ...this.form,
          };
          editDelivery(params)
            .then((res) => {
              this.$message.success("审核成功");
              this.cleanForm();
              this.$emit("getData");
            })
            .finally(() => {
              this.saveFlag = false;
            });
        } else {
          this.saveFlag = false;
        }
      });
    },
  },
};
</script>

<style></style>
