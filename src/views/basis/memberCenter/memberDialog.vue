<!--
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-03-25 08:34:09
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-18 11:00:46
-->
<template>
<!--  <el-dialog-->
<!--    :title="title"-->
<!--    :visible.sync="dialogVisible"-->
<!--    width="60%"-->
<!--    center-->
<!--    :modal-append-to-body="false"-->
<!--    :before-close="cleanForm"-->
<!--    :modal="false"-->
<!--    :close-on-click-modal="false"-->
<!--    destroy-on-close-->
<!--  >-->
  <a-drawer
    :title="title"
    width="560"
    :closable="true"
    @close="cleanForm"
    :visible="dialogVisible"
    :maskClosable="false"
    class="drawer-default"
  >
    <div class="dialogStyle">
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <div class="fatherTitle">基本信息</div>
        <el-row style="margin-top: 10px;" :gutter="24">
          <el-col :span="12">
            <el-form-item label="会员ID" label-width="100px" prop="id">
              <el-input placeholder="请输入" v-model="form.id" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="会员头像" label-width="100px" prop="headPortrait">
              <el-upload
                class="avatar-uploader"
                :action="upLoadUrl"
                :show-file-list="false"
                type="small"
                :on-success="handleAvatarSuccessFirst"
                :before-upload="beforeAvatarUpload"
              >
                <img
                  v-if="form.headPortrait"
                  :src="form.headPortrait"
                  class="avatar"
                />
                <template v-else>
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                  <div class="avatar-uploader-text">
                    上传照片
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="角色" label-width="100px" prop="roleId">
              <el-select
                v-model="form.roleId"
                placeholder="请选择角色"
                style="width: 100%;"
              >
                <el-option
                  v-for="dict in roleData"
                  :key="dict.id"
                  :label="dict.roleName"
                  :value="dict.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号" label-width="100px" prop="accountNumber">
              <el-input placeholder="请输入" v-model="form.accountNumber"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="昵称" label-width="100px" prop="nickName">
              <el-input placeholder="请输入" v-model="form.nickName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别" label-width="100px" prop="sex">
              <el-select
                v-model="form.sex"
                placeholder="请选择性别"
                style="width: 100%;"
              >
                <el-option
                  v-for="dict in dict.type.sys_user_sex"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="姓名" label-width="100px" prop="userName">
              <el-input placeholder="请输入" v-model="form.userName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.id" label="登录密码" label-width="100px">
              <el-input
                placeholder="请输入密码"
                v-model="form.loginPassword"
                show-password
              ></el-input>
            </el-form-item>
            <el-form-item
              v-if="!form.id"
              label="登录密码"
              label-width="100px"
              prop="loginPassword"
            >
              <el-input
                placeholder="请输入密码"
                minlength="6"
                v-model="form.loginPassword"
                show-password
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item
              label="所属机构姓名"
              label-width="100px"
              prop="referrerUserName"
            >
              <el-input :readonly="true" placeholder="选择所属机构后自动带出" v-model="form.referrerUserName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              v-if="!form.id"
              label="交易密码"
              label-width="100px"
              prop="transactionPassword"
            >
              <el-input
                placeholder="请输入密码"
                minlength="6"
                v-model="form.transactionPassword"
                show-password
              ></el-input>
            </el-form-item>
            <el-form-item v-if="form.id" label="交易密码" label-width="100px">
              <el-input
                placeholder="请输入密码"
                v-model="form.transactionPassword"
                show-password
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item
              label="所属机构"
              label-width="100px"
              prop="referrerId"
            >
              <el-select
                v-model="form.referrerId"
                placeholder="请按照手机/ID/昵称/姓名搜索"
                style="width: 100%;"
                filterable
                @clear="saveUserList = userList"
                :filter-method="filterMethod"
                @change="changeReferrers"
                @blur="selectReferredBlur"
              >
                <el-option
                  v-for="item in saveUserList"
                  :key="item.userId"
                  :label="item.userName"
                  :value="item.userId"
                >
                  <span> [{{ item.userId }}]{{ item.userName }}</span>
                </el-option>
              </el-select>
<!--              <el-input-->
<!--                placeholder="请输入"-->
<!--                v-model="form.referrerId"-->
<!--                oninput="value=value.replace(/[^0-9.]/g,'')"-->
<!--                maxlength="6"-->
<!--                @blur="onReferrer"-->
<!--              >-->
<!--              </el-input>-->
            </el-form-item>
          </el-col>
<!--          <el-col :span="12">
            <el-form-item label="身份证号码" label-width="100px" prop="idCard">
              <el-input
                placeholder="请输入身份证号码"
                v-model="form.idCard"
              ></el-input>
            </el-form-item>
          </el-col>-->
        </el-row>
<!--        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item
              label="身份证国徽照"
              label-width="110px"
              prop="idCardFrontPic"
            >
              <el-upload
                class="avatar-uploader"
                :action="upLoadUrl"
                :show-file-list="false"
                :on-success="handleAvatarSuccessSecond"
                :before-upload="beforeAvatarUpload"
              >
                <img
                  v-if="form.idCardFrontPic"
                  :src="form.idCardFrontPic"
                  class="avatar"
                />
                <template v-else>
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                  <div class="avatar-uploader-text">
                    上传照片
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="身份证人像照"
              label-width="110px"
              prop="idCardEmblemPic"
            >
              <el-upload
                class="avatar-uploader"
                :action="upLoadUrl"
                :show-file-list="false"
                :on-success="handleAvatarSuccessThird"
                :before-upload="beforeAvatarUpload"
              >
                <img
                  v-if="form.idCardEmblemPic"
                  :src="form.idCardEmblemPic"
                  class="avatar"
                />
                <template v-else>
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                  <span class="avatar-uploader-text">
                    上传照片
                  </span>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
            <el-col :span="8">
                <el-form-item
                  label="证件生效日期"
                  label-width="110px"
                  prop="validPeriodStart"
                >
                  <el-date-picker
                    style="width: 100%"
                    v-model="form.validPeriodStart"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择证件生效日期">
                  </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="证件失效日期"
                label-width="110px"
                prop="validPeriodEnd"
              >
                <el-date-picker
                  v-if="this.form.validPeriodEnd != '长期'"
                  style="width: 100%"
                  v-model="form.validPeriodEnd"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择证件失效日期">
                </el-date-picker>
                <span v-else>长期</span>
              </el-form-item>
            </el-col>
          <el-col :span="8">
              <el-form-item
                label="是否长期"
                label-width="110px"
                prop="isLongTerm"
              >
                <el-radio-group v-model="isLongTerm" @change="longTermChange">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>-->
        <div class="fatherTitle">
          银行账户
          <el-button type="text" @click="addBankAccount" style="margin-left: 16px">
            新增银行账户
          </el-button>
        </div>
        <div v-for="(item, index) in form.bankAccountList" :key="index">
          <div class="sonTitle">
            银行账户{{ index + 1 }}
            <el-button
              type="text"
              v-if="(index>0)"
              @click="deleteBankAccount(index)"
              class="del-btn"
            >
              删除
            </el-button>
          </div>
          <el-row style="margin-top: 10px;" :gutter="24">
            <el-col :span="12">
              <el-form-item
                label="开户行"
                label-width="100px"
                prop="bankAddress"
              >
                <el-input v-model="item.bankAddress" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="银行账户名"
                label-width="100px"
                prop="bankAccountName"
              >
                <el-input v-model="item.bankAccountName" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="银行账号"
                label-width="100px"
                prop="bankAccount"
              >
                <el-input v-model="item.bankAccount" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="联行号"
                label-width="100px"
                prop="bankNo"
              >
                <el-input v-model="item.bankNo" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider></el-divider>
        </div>
      </el-form>
    </div>

<!--    <span slot="footer" class="dialog-footer">-->
     <div class="drawer-bootom-button">
      <a-button type="primary"  style="margin-right: 8px" @click="saveForm">提 交</a-button>
      <a-button  @click="cleanForm">取 消</a-button>
     </div>
<!--    </span>-->
<!--  </el-dialog>-->
  </a-drawer>
</template>
<script>
import {add, edit, getUserName, getRole, queryReferrer} from "@/api/basis/basis.js";
import {valiPassword, valiPassword1, valiPhone} from "@/utils/validate";
import moment from "moment";

export default {
  name: "MemberDialog",
  dicts: ["sys_user_sex"],
  data() {

    return {
      upLoadUrl: process.env.VUE_APP_BASE_API + "/pic/upload",
      dialogVisible: false,
      title: "新增会员",
      saveFlag: false,
      roleData: [],
      isLongTerm: undefined,
      form: {
        id: "",
        accountNumber: "",
        headPortrait: "",
        nickName: "",
        sex: "",
        userName: "",
        loginPassword: "",
        transactionPassword: "",
        referrerId: "",
        referrerUserName: "",
        idCard: "",
        roleId: "",
        idCardFrontPic: "",
        idCardEmblemPic: "",
        validPeriodStart: '',
        validPeriodEnd: '',
        bankAccountList: [
          {
            bankAccount: "",
            bankNo: "",
            bankAccountName: "",
            bankAddress: "",
          },
        ],
      },
      rules: {
        accountNumber: [
          { required: true, message: "请输入正确手机号", trigger: "change" },
          { validator: valiPhone, trigger: "blur" },
        ],
        headPortrait: [
          { required: false, message: "请添加会员头像", trigger: "change" },
        ],
        nickName: [
          { required: true, message: "请填写昵称", trigger: "change" },
        ],
        sex: [{ required: true, message: "请填写性别", trigger: "change" }],
        userName: [
          { required: true, message: "请填写姓名", trigger: "change" },
        ],
        referrerId: [
          { required: true, message: "请填写所属机构ID", trigger: "change" },
        ],
        loginPassword: [
          { required: true, message: "请填写登录密码", trigger: "change" },
          {
            trigger: "blur",
            validator: valiPassword,
          },
        ],
        transactionPassword: [
          { required: true, message: "请填写交易密码", trigger: "change" },
          {
            trigger: "blur",
            validator: valiPassword1,
          },
        ],

        idCard: [
          { required: true, message: "请填写身份证号码", trigger: "change" },
        ],
        idCardFrontPic: [
          { required: true, message: "请添加身份证国徽照", trigger: "change" },
        ],
        idCardEmblemPic: [
          { required: true, message: "请添加身份证人像照", trigger: "change" },
        ],
        roleId: [{ required: true, message: "请选择角色", trigger: "change" }],
        validPeriodStart: [
          { required: true, message: "请选择证件生效日期", trigger: "change" },
        ],
        validPeriodEnd: [
          { required: true, message: "请选择证件失效日期", trigger: "change" },
        ],
      },
      saveUserList: [],
      userList:[]

    };
  },

  created() {
    this.getRole();
  },

  methods: {
    longTermChange(value) {
      // console.log('???????', value)
      this.isLongTerm = value;
      if (value == 1) {
        this.form.validPeriodEnd = '长期';
      } else {
        this.form.validPeriodEnd = '';
      }
    },
    selectReferredBlur(e){
      setTimeout(()=>{
        if(!this.form.referrerId){
          this.form.referrerUserName = '';
          this.saveUserList = this.userList;
        }
      },300)
    },
    changeReferrers(referrerId){
      console.log(referrerId);
      if(!referrerId){
        this.form.referrerUserName = '';
        return
      }

      this.userList.forEach(item => {
        if(item.userId === referrerId){
          this.form.referrerUserName = item.userName;
        }
      })

    },
    filterMethod(query) {
      console.log(query,this.userList);
      if(!query){
        this.saveUserList = this.userList;
      }else {
        this.saveUserList = this.userList.filter((item) => {
          return (
            (item.nickName+'').indexOf(query) > -1 ||
            (item.accountNumber+'').indexOf(query) > -1 ||
            (item.userId+'').indexOf(query) > -1 ||
            (item.userName+'').indexOf(query) > -1
          );
        });
      }
    },
    async init(row) {
      this.dialogVisible = true;
      await this.getUserList()
      this.$nextTick(() => {
        this.$refs?.form?.clearValidate();
      })
      if (row) {
        this.title = "编辑会员";
        this.isLongTerm = row.validPeriodEnd && row.validPeriodEnd == '长期' ? 1 : 0;
        this.form = {
          ...row,
          validPeriodStart: !row.validPeriodStart ? '' : new Date(moment(row.validPeriodStart)),
          validPeriodEnd: !row.validPeriodEnd ? '' : (row.validPeriodEnd == '长期' ? '长期' : new Date(moment(row.validPeriodEnd))),
          sex: (row.sex || row.sex == 0) ? String(row.sex) : undefined,
          loginPassword: "",
          transactionPassword: "",
          headPortrait: row?.headPortrait || require('@/assets/images/member.png'),
          bankAccountList: row.bankAccountList
            ? row.bankAccountList
            : [
                {
                  bankAccount: "",
                  bankNo: "",
                  bankAccountName: "",
                  bankAddress: "",
                },
              ],
        };
        console.log('XXXXX',this.form)
      }
    },
    async getUserList() {
      await queryReferrer().then((res) => {
        if (res.code === 200) {
          this.userList = res.data;
          this.saveUserList = res.data;
        }
      });
    },
    getRole() {
      getRole().then((res) => {
        this.roleData = res.data;
      });
    },

    // 新增银行账户
    addBankAccount() {
      this.form.bankAccountList.push({
        bankAccount: "",
        bankNo: "",
        bankAccountName: "",
        bankAddress: "",
      });
    },

    // 删除银行账户
    deleteBankAccount(index) {
      this.form.bankAccountList.splice(index, 1);
    },

    handleAvatarSuccessFirst(res, file) {
      this.form.headPortrait = file.response.name;
    },

    handleAvatarSuccessSecond(res, file) {
      this.form.idCardFrontPic = file.response.name;
    },

    handleAvatarSuccessThird(res, file) {
      this.form.idCardEmblemPic = file.response.name;
    },

    beforeAvatarUpload(file) {
      const type = ["image/jpeg", "image/jpg", "image/png", "image/svg"];
      const isJPG = type.includes(file.type);
      const isLt2M = file.size / 1024 / 1024 < 10;

      if (!isJPG) {
        this.$message.error(`图片格式错误!`);
        return false;
      }
      if (!isLt2M) {
        this.$message.error("上传会员头像图片大小不能超过 10MB!");
        return false;
      }
      return isJPG && isLt2M;
    },

    cleanForm() {
      this.dialogVisible = false;
      this.saveFlag = false;
      this.title = "新增会员";
      this.isLongTerm = '';
      this.form = this.$options.data().form;
    },

    saveForm() {
      if (this.saveFlag) {
        return;
      }
      this.saveFlag = true;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const params = {
            ...this.form,
            id: this.form.id ? parseInt(this.form.id) : undefined,
            referrerId: parseInt(this.form.referrerId),
            sex: parseInt(this.form.sex),
            roleId: parseInt(this.form.roleId),
            headPortrait: this.form.headPortrait.includes('data:image/png') ? '': this.form.headPortrait,
            validPeriodStart: !this.form.validPeriodStart ? "" : (this.form.validPeriodStart == '长期' ? this.form.validPeriodStart : moment(this.form.validPeriodStart).format('YYYY-MM-DD')),
            validPeriodEnd: !this.form.validPeriodEnd ? "" : (this.form.validPeriodEnd == '长期' ? this.form.validPeriodEnd : moment(this.form.validPeriodEnd).format('YYYY-MM-DD')),
          };
          if (this.form.id) {
            edit(params)
              .then((res) => {
                this.$message.success("编辑成功");
                this.cleanForm();
                this.$emit("getData");
              })
              .finally(() => {
                this.saveFlag = false;
              });
          } else {
            add(params)
              .then((res) => {
                this.$message.success("新增成功");
                this.cleanForm();
                this.$emit("getData");
              })
              .finally(() => {
                this.saveFlag = false;
              });
          }
        } else {
          this.saveFlag = false;
        }
      });
    },
    // 获取买方姓名
    onReferrer() {
      if (this.form.referrerId !== "") {
        const params = {
          memberId: this.form.referrerId,
        };
        getUserName(params).then((res) => {
          if (res.code === 200) {
            this.form.referrerUserName = res.data.userName;
          } else {
            uni.showToast({
              title: res.msg || "服务响应异常",
              icon: "error",
            });
          }
        });
      }
    },
  },
};
</script>

<style>
.fatherTitle {
  font-size: 18px;
  font-weight: bolder;
}

.sonTitle {
  font-weight: bold;
  line-height: 22px;
  margin-top: 8px;
}

.dialogStyle {
  //height: 450px;
  //padding: 10px;
  max-height: calc(100vh - 140px);
  overflow: auto;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>

<style lang="less" scoped>
.avatar-uploader-icon {
  width: 110px;
  height: 110px;
  line-height: 80px;
}
.avatar-uploader-text {
  position: absolute;
  left: 49%;
  transform: translate(-50%,0);
  top: 55px;
  color: rgba(0,0,0,0.88);
}
.avatar {
  width: 110px;
  height: 110px;
}
::v-deep .el-form-item__label {
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
}

.el-row{
  margin-right: 0!important;
}

.del-btn {
  margin-left: 30px;
  color: #F5222D;
}
.el-divider--horizontal {
  margin: 0 0 24px 0;
}


</style>
