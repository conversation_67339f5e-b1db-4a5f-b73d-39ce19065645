<template>
  <a-drawer
    :title="title"
    width="560"
    :closable="true"
    @close="cleanForm"
    :visible="dialogVisible"
    :maskClosable="false"
    class="drawer-default auth-dialog"
  >
    <div class="dialogStyle">
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <el-row>
          <el-col :span="24">
            <el-form-item label="会员类型" label-width="100px" >
              <el-radio-group v-model="form.type" @input="changeType" >
                <el-radio :label="0">个人</el-radio>
                <el-radio :label="1">企业</el-radio>
                <el-radio :label="2">个体工商户</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <template v-if="form.type == 0">

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="证件类型" label-width="100px" prop="idCardType">
                <el-select @change="changeIdCardType" :clearable="false" v-model="form.idCardType" placeholder="请选择证件类型" style="width: 100%;">
                  <el-option
                    v-for="dict in idCardTypeList"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="姓名" label-width="100px" prop="userName">
                <el-input  placeholder="请输入" v-model="form.userName"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="性别" label-width="100px" prop="sex">
                <el-select
                  v-model="form.sex"
                  placeholder="请选择性别"
                  style="width: 100%;"
                >
                  <el-option
                    v-for="dict in dict.type.sys_user_sex"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="身份证号码" label-width="100px" prop="idNumber">
                <el-input
                  placeholder="请输入"
                  v-model="form.idNumber"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" v-if="form.idCardType == '1'">
            <el-col :span="12">
              <el-form-item
                label="身份证国徽照"
                label-width="110px"
                prop="frontImage"
              >
                <el-upload
                  class="avatar-uploader"
                  :action="upLoadUrl"
                  :show-file-list="false"
                  :on-success="handleAvatarSuccessSecond"
                  :before-upload="beforeAvatarUpload"
                >
                  <img
                    v-if="form.frontImage"
                    :src="form.frontImage"
                    class="avatar"
                  />
                  <template v-else>
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                    <div class="avatar-uploader-text">
                      上传照片
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="身份证人像照"
                label-width="110px"
                prop="backImage"
              >
                <el-upload
                  class="avatar-uploader"
                  :action="upLoadUrl"
                  :show-file-list="false"
                  :on-success="handleAvatarSuccessThird"
                  :before-upload="beforeAvatarUpload"
                >
                  <img
                    v-if="form.backImage"
                    :src="form.backImage"
                    class="avatar"
                  />
                  <template v-else>
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                    <span class="avatar-uploader-text">
                    上传照片
                  </span>
                  </template>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" v-else-if="form.idCardType == '2'">
            <el-col :span="12">
              <el-form-item
                label="证件照片"
                label-width="110px"
                prop="backImage"
              >
                <el-upload
                  class="avatar-uploader"
                  :action="upLoadUrl"
                  :show-file-list="false"
                  :on-success="handleAvatarSuccessThird"
                  :before-upload="beforeAvatarUpload"
                >
                  <img
                    v-if="form.backImage"
                    :src="form.backImage"
                    class="avatar"
                  />
                  <template v-else>
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                    <span class="avatar-uploader-text" style="width: 100%">
                    上传证件照片
                  </span>
                  </template>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">
                <el-form-item
                  label="证件生效日期"
                  label-width="110px"
                  prop="validPeriodStart"
                >
                  <el-date-picker
                    style="width: 100%"
                    v-model="form.validPeriodStart"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择证件生效日期">
                  </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="证件失效日期"
                label-width="110px"
                prop="validPeriodEnd"
              >
                <span v-if="this.form.validPeriodEnd == '长期'">长期</span>
                <el-date-picker
                  v-else
                  style="width: 100%"
                  v-model="form.validPeriodEnd"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择证件失效日期">
                </el-date-picker>
              </el-form-item>
          </el-col>
            <el-col :span="8">
              <el-form-item
                label="是否长期"
                label-width="110px"
                prop="isLongTerm"
              >
                <el-radio-group v-model="isLongTerm" @change="longTermChange">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        <template v-if="form.type != 0">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="公司名称" label-width="100px" prop="name">
                <el-input  placeholder="请输入" v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="统一社会信用代码" label-width="100px" prop="businessLicense">
                <el-input  placeholder="请输入" v-model="form.businessLicense"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="法人" label-width="100px" prop="legalPersonName">
                <el-input  placeholder="请输入" v-model="form.legalPersonName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="营业执照"
                label-width="110px"
                prop="businessLicensePic"
              >
                <el-upload
                  class="avatar-uploader"
                  :action="upLoadUrl"
                  :show-file-list="false"
                  :on-success="handleAvatarSuccessFour"
                  :before-upload="beforeAvatarUpload"
                >
                  <img
                    v-if="form.businessLicensePic"
                    :src="form.businessLicensePic"
                    class="avatar"
                  />
                  <template v-else>
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                    <div class="avatar-uploader-text">
                      上传照片
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">
                <el-form-item
                  label="证件生效日期"
                  label-width="110px"
                  prop="validPeriodStart"
                >
                  <el-date-picker
                    style="width: 100%"
                    v-model="form.validPeriodStart"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择证件生效日期">
                  </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="证件失效日期"
                label-width="110px"
                prop="validPeriodEnd"
              >
                <span v-if="this.form.validPeriodEnd == '长期'">长期</span>
                <el-date-picker
                  v-else
                  style="width: 100%"
                  v-model="form.validPeriodEnd"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择证件失效日期">
                </el-date-picker>
              </el-form-item>
          </el-col>
            <el-col :span="8">
              <el-form-item
                label="是否长期"
                label-width="110px"
                prop="isLongTerm"
              >
                <el-radio-group v-model="isLongTerm" @change="longTermChange">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </el-form>
    </div>

     <div class="drawer-bootom-button">
      <a-button type="primary" v-if="checkCanSubmit()" style="margin-right: 8px" @click="saveForm">提 交</a-button>
      <a-button style="margin-right: 8px" @click="cleanForm">取 消</a-button>
      <a-button v-if="this.userType == 1" type="danger"  style="margin-right: 8px" @click="cleanAuth">清除认证</a-button>
     </div>
  </a-drawer>
</template>
<script>
import {add, comRealName, edit, identifyIdCard, userRealname, identifyBusinessLicense, clearCertification} from "@/api/basis/basis.js";
import {isDeal} from "@/utils/utils";
import {valiPhone} from "@/utils/validate";
import moment from 'moment';

export default {
  name: "AuthDialog",
  dicts: ["sys_user_sex"],
  data() {

    return {
      upLoadUrl: process.env.VUE_APP_BASE_API + "/pic/upload",
      dialogVisible: false,
      title: "认证",
      roleName:undefined,
      roleId: undefined,
      userType: undefined,
      rowType: 0,
      isLongTerm: undefined,
      overDue: false,
      form: {
        id: "",
        accountNumber: "",
        sex: "",
        name: "",
        userName: "",
        idNumber: "",
        frontImage: "",
        backImage: "",
        type: 0,
        businessLicensePic: "",
        businessLicense: "",
        legalPersonName: "",
        idCardType: '1',
        validPeriodStart: '',
        validPeriodEnd: '',
      },
      //保存初始值，切换type是判断是否填入原始值
      validPeriodStartInit: '',
      validPeriodEndInit: '',
      typeInit: 0,
      idCardTypeList: [
        { label: "身份证", value: "1" },
        { label: "中国香港身份证", value: "2" },
      ],
      rules: {
        accountNumber: [
          { required: true, message: "请输入正确手机号", trigger: "change" },
          { validator: valiPhone, trigger: "blur" },
        ],
        sex: [{ required: true, message: "请选择性别", trigger: "change" }],
        userName: [
          { required: true, message: "请填写姓名", trigger: "change" },
        ],
        idCardType: [
          { required: true, message: "请选择证件类型", trigger: "change" },
        ],
        name: [
          { required: true, message: "请填写公司名称", trigger: "change" },
        ],
        idNumber: [
          { required: true, message: "请填写身份证号码", trigger: "change" },
        ],
        frontImage: [
          { required: true, message: "请添加身份证国徽照", trigger: "change" },
        ],
        backImage: [
          { required: true, message: "请添加身份证人像照", trigger: "change" },
        ],
        businessLicensePic: [
          { required: true, message: "请添加营业执照", trigger: "change" },
        ],
        businessLicense: [
          { required: true, message: "请填写统一社会信用代码", trigger: "change" },
        ],
        legalPersonName: [
          { required: true, message: "请填写法人", trigger: "change" },
        ],
        validPeriodStart: [
          { required: true, message: "请选择证件生效日期", trigger: "change" },
        ],
        validPeriodEnd: [
          { required: true, message: "请选择证件失效日期", trigger: "change" },
        ],
      },
    };
  },

  created() {
  },

  methods: {
    isDeal,
    longTermChange(value) {
      this.isLongTerm = value;
      if (value == 1) {
        this.form.validPeriodEnd = '长期';
      } else {
        this.form.validPeriodEnd = '';
      }
    },
    changeIdCardType(){
      this.form.userName = '';
      this.form.sex = '';
      this.form.idNumber = '';
      this.form.frontImage = '';
      this.form.backImage = '';
      this.$nextTick(()=>{
        this.$refs?.form?.clearValidate();
      })
    },
    changeType(value){
      this.$nextTick(()=>{
        this.$refs?.form?.clearValidate();
      })
      if (this.userType == 1) {
        if (value != this.typeInit) {
          this.form.validPeriodStart = '';
          this.form.validPeriodEnd = '';
          this.isLongTerm = undefined;
        } else {
          this.form.validPeriodStart = this.validPeriodStartInit;
          this.form.validPeriodEnd = this.validPeriodEndInit;
          this.isLongTerm = this.validPeriodEndInit == '长期' ? 1 : 0;
        }
      }
    },
    checkCanSubmit() {
      let flag = true;
      // 已认证且为个人的，在不过期情况下，不能再次认证个人
      // if (this.userType == 1 && this.form.type == 0 && !this.overDue) {
      //   flag = false;
      // }
      return flag
    },
    init(row, title) {
      console.log('row>>>>>>>>', row)
      this.dialogVisible = true;
      this.$nextTick(()=>{
        this.$refs?.form?.clearValidate();
      })
      this.rowType = row.type;
      this.form.id = row.id;

      // if(['服务中心','商家'].includes(row.roleName)){
      //   this.form.type = 1;
      // }
      // console.log('roleId...',row.roleId)
      // if(['2','3'].includes(row.roleId+'')) {
      //   this.form.type = row.type;
      // }
      if (row.userType == '1') {
        this.form.type = 1;
      } else {
        if (row.userType == '0') {
          if(['2','3'].includes(row.roleId+'')) {
            this.form.type = 1;
          } else {
            this.form.type = 0;
          }
        }
      }
      if (row.validPeriodEnd && row.validPeriodEnd != '长期' &&  moment().isAfter(this.validPeriodEnd)) {
        this.overDue = true;
      }
      this.userType = row.userType;
      this.roleName = row.roleName;
      this.title = title;

      this.roleId = row.roleId + '';


      if (row.userType == 1 && row) {
        if (row.type == 0) {
          this.form = {
            id: row.id,
            type: row.type,
            idCardType: row.idCardType + '',
            userName: row.userName,
            sex: row.sex + '',
            idNumber: row.idCard,
            backImage: row.idCardEmblemPic,
            frontImage: row.idCardFrontPic,
            validPeriodStart: row.validPeriodStart,
            validPeriodEnd: row.validPeriodEnd,
          };
        } else {
          this.form = {
            id: row.id,
            type: row.type,
            name: row.userName,
            businessLicense: row.businessLicense,
            legalPersonName: row.legalPersonName,
            businessLicensePic: row.businessLicensePic,
            validPeriodStart: row.validPeriodStart,
            validPeriodEnd: row.validPeriodEnd,
          }
        }
        this.isLongTerm = row.validPeriodEnd == '长期' ? 1 : 0;
        this.validPeriodEndInit = row.validPeriodEnd;
        this.validPeriodStartInit = row.validPeriodStart;
        this.typeInit = row.type;
      }
    },

    handleAvatarSuccessSecond(res, file) {
      this.form.frontImage = file.response.name;
      if(file.response.name){
        identifyIdCard({
          backImage:file.response.name,
          idCardType: this.form.idCardType,
        }).then(res=>{
          console.log('res...',res);
          if(res.code === 200){
            if(Object.keys(res.data || {}).length > 0) {
              const {validPeriod} = res.data;
              if(validPeriod) {
                const [startCardDate, endCardDate] = validPeriod.split('-')
                if(startCardDate) {
                  const startCardDateRep = startCardDate.replace(/\./g, "");
                  this.form.validPeriodStart = moment(startCardDateRep).format('YYYY-MM-DD');
                }
                if(endCardDate) {
                  if(endCardDate == '长期') {
                    this.form.validPeriodEnd = '长期';
                    this.isLongTerm = 1;
                  } else {
                    const endCardDateRep = endCardDate.replace(/\./g, "");
                    this.form.validPeriodEnd = moment(endCardDateRep).format('YYYY-MM-DD');
                    this.isLongTerm = 0;
                  }
                }
		          }
            }
          }
        })
      }
    },

    handleAvatarSuccessThird(res, file) {
      this.form.backImage = file.response.name;
      if(file.response.name){
        identifyIdCard({
          backImage:file.response.name,
          idCardType: this.form.idCardType,
        }).then(res=>{
          console.log('res...',res);
          if(res.code === 200){
            if(Object.keys(res.data || {}).length > 0) {
              this.form.userName = res.data.name;
              this.form.idNumber = res.data.idNumber;
              this.form.sex = res.data.sex == '男' ? '0' : '1'
            }
          }
        })
      }
    },

    handleAvatarSuccessFour(res, file) {
      this.form.businessLicensePic = file.response.name;
      if(file.response.name){
        identifyBusinessLicense({
          backImage:file.response.name,
        }).then(res=>{
          console.log('res...',res);
          if(res.code === 200){
            if(Object.keys(res.data || {}).length > 0) {
              const {creditCode, companyName, validPeriod, validFromDate, validToDate, legalPerson} = res.data;
              this.form.name = companyName;
              this.form.businessLicense = creditCode;
              this.form.legalPersonName = legalPerson;
              if(validFromDate) {
                this.form.validPeriodStart = moment(validFromDate).format('YYYY-MM-DD');
              }

              if(validToDate) {
                if(validToDate == '29991231') {
                  this.form.validPeriodEnd = '长期'
                  this.isLongTerm = 1;
                } else {
                  this.isLongTerm = 0;
                  this.form.validPeriodEnd = moment(validToDate).format('YYYY-MM-DD');
                }
              }
            }
          }
        })
      }
    },

    beforeAvatarUpload(file) {
      const type = ["image/jpeg", "image/jpg", "image/png", "image/svg"];
      const isJPG = type.includes(file.type);
      const isLt2M = file.size / 1024 / 1024 < 10;

      if (!isJPG) {
        this.$message.error(`图片格式错误!`);
        return false;
      }
      if (!isLt2M) {
        this.$message.error("上传会员头像图片大小不能超过 10MB!");
        return false;
      }
      return isJPG && isLt2M;
    },

    cleanForm() {
      this.dialogVisible = false;
      this.isLongTerm = '';
      this.form = {
        id: "",
        accountNumber: "",
        sex: "",
        userName: "",
        businessLicense: "",
        legalPersonName: "",
        name: "",
        idNumber: "",
        frontImage: "",
        backImage: "",
        type: 0,
        businessLicensePic: "",
        validPeriodStart: "",
        validPeriodEnd: "",
        idCardType: '1',
      };
      this.$emit("getData");
    },
    cleanAuth() {
      const that = this;
      this.$modal.confirm('是否确认直接清除认证信息，恢复到未认证状态？').then(function () {
        return clearCertification({ id: that.form.id });
      }).then(() => {
        this.$modal.msgSuccess("清除认证信息成功");
        this.cleanForm();
      }).catch(() => { });
    },
    saveForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // const params = {
          //   ...this.form,
          //   id: this.form.id ? parseInt(this.form.id) : undefined,
          //   sex: this.form.type == 0 ? parseInt(this.form.sex): null,
          //   name: this.form.type == 0 ? this.form.userName : this.form.name,
          // };
          const params = {}
          if (this.form.type == 0) {
            params.id = this.form.id ? parseInt(this.form.id) : undefined;
            params.backImage = this.form.backImage;
            params.frontImage = this.form.frontImage;
            params.idCardType = this.form.idCardType;
            params.idNumber = this.form.idNumber;
            params.name = this.form.userName;
            params.userName = this.form.userName;
            params.validPeriodEnd = this.form.validPeriodEnd;
            params.validPeriodStart = this.form.validPeriodStart;
            params.type = this.form.type;
            params.sex = parseInt(this.form.sex);
          } else {
            params.id = this.form.id ? parseInt(this.form.id) : undefined;
            params.businessLicense = this.form.businessLicense;
            params.businessLicensePic = this.form.businessLicensePic;
            params.legalPersonName = this.form.legalPersonName;
            params.name = this.form.name;
            params.type = this.form.type;
            params.validPeriodEnd = this.form.validPeriodEnd;
            params.validPeriodStart = this.form.validPeriodStart;
          }
          let promise = this.form.type == 0 ? userRealname(params) : comRealName(params);
          promise.then((res) => {
            if (res.code === 200) {
              this.$message.success("操作成功");
              this.cleanForm();
            } else {
              this.$message.error(res.msg || "操作失败");
            }
          }).finally(() => {
          });
        } else {
        }
      });
    },
  },
};
</script>

<style>
.dialogStyle {
  max-height: calc(100vh - 140px);
  overflow: auto;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
</style>

<style lang="less" scoped>
.avatar-uploader-icon {
  width: 110px;
  height: 110px;
  line-height: 80px;
}
.avatar-uploader-text {
  position: absolute;
  left: 49%;
  transform: translate(-50%,0);
  top: 55px;
  color: rgba(0,0,0,0.88);
}
.avatar {
  width: 110px;
  height: 110px;
}
::v-deep .el-form-item__label {
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
}

.el-row{
  margin-right: 0!important;
}

.del-btn {
  margin-left: 30px;
  color: #F5222D;
}
.el-divider--horizontal {
  margin: 0 0 24px 0;
}
</style>
