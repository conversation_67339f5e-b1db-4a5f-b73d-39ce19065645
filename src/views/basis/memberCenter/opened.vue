<template>
  <a-drawer
    :title="title"
    width="720"
    :closable="true"
    @close="cleanForm"
    :visible="dialogVisible"
    :maskClosable="false"
    class="drawer-default auth-dialog"
  >
    <div class="dialogStyle" >
      <div style="margin-bottom: 12px" v-if="rowDetail">
        <div class="fatherTitle">审核信息</div>
        <div class="open-info-content">
          <span>审核状态:</span>
          <span>{{approveStatusObj[rowDetail.approveStatus]}}</span>
        </div>
        <div class="open-info-content">
          <span>审核url:</span>
          <span>{{rowDetail.url}}</span>
        </div>
        <div class="open-info-content">
          <span>url失效时间:</span>
          <span>{{rowDetail.expireTime}}</span>
        </div>
        <div class="open-info-content" v-if="rowDetail.approveStatus == 'F'">
          <span>失败原因:</span>
          <span>{{rowDetail.failReason || '-'}}</span>
        </div>
      </div>
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <div class="fatherTitle">基本信息</div>
        <el-row>
          <el-col :span="24">
            <el-form-item label="企业类型" label-width="100px" >
              <el-radio-group v-model="form.type">
                <el-radio :label="1">企业</el-radio>
                <el-radio :label="2">个体工商户</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="法人姓名" label-width="100px" prop="legalPersonName">
              <el-input  placeholder="请输入" v-model="form.legalPersonName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="法人证件号码" label-width="100px" prop="legalPersonIdCard">
              <el-input
                placeholder="请输入"
                v-model="form.legalPersonIdCard"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item
              label="证件生效日期"
              label-width="110px"
              prop="idCardStartDate"
            >
              <el-date-picker
                style="width: 100%"
                v-model="form.idCardStartDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择证件生效日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item
              label="证件失效日期"
              label-width="110px"
              prop="idCardEndDate"
            >
              <el-date-picker
                :disabled="form.isLongTerm"
                style="width: 100%"
                v-model="form.idCardEndDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择证件失效日期">
              </el-date-picker>
            </el-form-item>

          </el-col>

          <el-col :span="3">
            <el-form-item
              label="是否长期"
              label-width="110px"
            >
              <el-checkbox v-model="form.isLongTerm" label="" @change="changeIsLongTerm"></el-checkbox>
            </el-form-item>

          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item
              label="身份证人像面"
              label-width="110px"
              prop="idCardFrontPic"
            >
              <el-upload
                class="avatar-uploader"
                :action="upLoadUrl"
                :show-file-list="false"
                :on-success="handleAvatarSuccessSecond"
                :before-upload="beforeAvatarUpload"
              >
                <img
                  v-if="form.idCardFrontPic"
                  :src="form.idCardFrontPic"
                  class="avatar"
                />
                <template v-else>
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                  <div class="avatar-uploader-text">
                    上传照片
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="身份证国徽面"
              label-width="110px"
              prop="idCardEmblemPic"
            >
              <el-upload
                class="avatar-uploader"
                :action="upLoadUrl"
                :show-file-list="false"
                :on-success="handleAvatarSuccessThird"
                :before-upload="beforeAvatarUpload"
              >
                <img
                  v-if="form.idCardEmblemPic"
                  :src="form.idCardEmblemPic"
                  class="avatar"
                />
                <template v-else>
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                  <span class="avatar-uploader-text">
                  上传照片
                </span>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="身份证详细地址" label-width="100px" prop="idCardAddress">
              <el-input style="width: 98%" placeholder="请输入" v-model="form.idCardAddress"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="公司名称" label-width="100px" prop="name">
              <el-input  placeholder="请输入" v-model="form.name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="统一社会信用代码" label-width="100px" prop="businessLicense">
              <el-input  placeholder="请输入" v-model="form.businessLicense"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="公司简称" label-width="100px" prop="shortName">
              <el-input  placeholder="请输入" v-model="form.shortName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item
              label="营业执照"
              label-width="110px"
              prop="businessLicensePic"
            >
              <el-upload
                class="avatar-uploader"
                :action="upLoadUrl"
                :show-file-list="false"
                :on-success="handleAvatarSuccessFour"
                :before-upload="beforeAvatarUpload"
              >
                <img
                  v-if="form.businessLicensePic"
                  :src="form.businessLicensePic"
                  class="avatar"
                />
                <template v-else>
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                  <div class="avatar-uploader-text">
                    上传照片
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24" v-if="isEdit">
          <el-col :span="12">
            <el-form-item
              label="商户信息变更申请表"
              label-width="110px"
              prop="businessInfoChange"
            >
              <el-upload
                class="avatar-uploader"
                :action="upLoadUrl"
                :show-file-list="false"
                :on-success="handleAvatarSuccessSix"
                :before-upload="beforeAvatarUpload"
              >
                <img
                  v-if="form.businessInfoChange"
                  :src="form.businessInfoChange"
                  class="avatar"
                />
                <template v-else>
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                  <div class="avatar-uploader-text">
                    上传照片
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="营业执照地址" label-width="100px" prop="allBusinessAddress">
<!--              <el-cascader-->
<!--                ref="cityCascaderRef"-->
<!--                style="width: 98%"-->
<!--                v-model="form.allBusinessAddress"-->
<!--                :options="cityOptions"-->
<!--                :props="{ label:'name', value: 'name', children: 'children' }"-->
<!--               >-->

<!--              </el-cascader>-->

              <el-cascader
                :options="test_allBusinessAddress"
                style="width: 98%"
                ref="cityCascaderRef"
                v-model="form.allBusinessAddress"
                :props="props">
              </el-cascader>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row >
          <el-col :span="24">
            <el-form-item
              label="营业场所照"
              label-width="110px"
              prop="shopPic"
            >
              <el-upload
                ref="shopRef"
                class="avatar-uploader"
                :action="upLoadUrl"
                list-type="picture-card"
                :on-success="handleAvatarSuccessFive"
                :before-upload="beforeAvatarUpload"
                :on-remove="handleRemove"
                :file-list="form.shopPic"
                :limit="3"
              >

                <div style="display: flex;flex-direction: column;align-items: center;justify-content: center;height: 100%">
                  <i class="el-icon-plus" style="position: absolute;top: 30px"></i>
                  <span style="transform: translateY(20px)">上传照片</span>
                </div>
              </el-upload>
              <span>平台的经营店铺页面截图，至少三张</span>
            </el-form-item>

          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="营业执照详细地址" label-width="100px" prop="businessAddress">
              <el-input placeholder="请输入" style="width: 98%" v-model="form.businessAddress"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="fatherTitle">银行账户</div>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="开户行" label-width="100px" prop="bankCategory">
              <el-select filterable v-model="form.bankCategory" placeholder="请选择" @change="changeCategory">
                <el-option
                  v-for="item in categoryOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="省" label-width="100px" prop="province" required>
              <el-select filterable v-model="form.province" placeholder="请选择" @change="changeProvince">
                <el-option
                  v-for="item in provinceOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="市" label-width="100px" prop="city" required>
              <el-select filterable v-model="form.city" placeholder="请选择" @change="changeCity">
                <el-option
                  v-for="item in cityOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="区县" label-width="100" prop="bankAddress">
              <el-select filterable v-model="form.bankAddress" placeholder="请选择" @change="changeAddress">
                <el-option
                  v-for="item in addressOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="开户支行" label-width="100" prop="bankName">
              <el-select filterable v-model="form.bankNo" placeholder="请选择" @change="changeBankNumber" class="w-full">
                <el-option
                  v-for="item in bankNumberOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span='12'>
            <el-form-item label="银行账户名" label-width="100px" prop="bankAccountName">
              <el-input  placeholder="请输入" v-model="form.bankAccountName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span='12'>
            <el-form-item label="银行账号" label-width="100px" prop="bankAccount">
              <el-input  placeholder="请输入" v-model="form.bankAccount"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
    </div>

    <div class="drawer-bootom-button" v-if="(rowDetail && rowDetail.approveStatus && rowDetail.approveStatus != 'S') || !rowDetail">
      <a-button type="primary" :loading="btnLoading" style="margin-right: 8px" @click="saveForm">提 交</a-button>
      <a-button  @click="cleanForm">取 消</a-button>
    </div>
  </a-drawer>
</template>
<script>
import {
  approveAdd,
  approveDetail,
  approveUpdate,
  getAddress,
  getBankNumber,
  getCategory,
  getProvince,
  getCity,
  getEazyCityList
} from '@/api/basis/basis.js'
import {isDeal} from "@/utils/utils";
import moment from "moment";

export default {
  name: "opened",
  dicts: [],
  data() {
    return {
      btnLoading: false,
      upLoadUrl: process.env.VUE_APP_BASE_API + "/pic/upload",
      dialogVisible: false,
      title: "开通",
      form: {
        memberId: "",
        legalPersonName: "",
        allBusinessAddress: [],
        legalPersonIdCard: "",
        idCardFrontPic: "",
        idCardEmblemPic: "",
        idCardEndDate: "",
        idCardStartDate: "",
        type: 1,
        idCardAddress: "",
        name: "",
        businessLicense: "",
        businessLicensePic: "",
        businessInfoChange: "", // 商户信息变更申请表
        businessAddress: "",
        shopPic: [],
        bankCategory: "",
        province: '',
        city: '',
        bankAddress: '',
        bankNo: "",
        bankName: "",
        bankAccountName: "",
        bankAccount: "",
        shortName: "",
        isLongTerm: false,
      },
      test_allBusinessAddress:[],
      rules: {
        idCardStartDate: [
          { required: true, message: "请选择证件生效日期", trigger: "change" },
        ],
        idCardEndDate: [
          { required: true, message: "请选择证件失效日期", trigger: "change" },
        ],
        legalPersonName: [
          { required: true, message: "请填写法人姓名", trigger: "change" },
        ],
        allBusinessAddress: [
          { required: true, message: "请选择营业执照地址", trigger: "change" },
        ],
        legalPersonIdCard: [
          { required: true, message: "请填写法人身份证号码", trigger: "change" },
        ],
        idCardFrontPic: [
          { required: true, message: "请添加身份证人像面", trigger: "change" },
        ],
        idCardEmblemPic: [
          { required: true, message: "请添加身份证国徽面", trigger: "change" },
        ],
        idCardAddress: [
          { required: true, message: "请填写详细地址", trigger: "change" },
        ],
        name: [
          { required: true, message: "请填写公司名称", trigger: "change" },
        ],
        businessLicense: [
          { required: true, message: "请填写统一社会信用代码", trigger: "change" },
        ],
        shortName: [
          { required: true, message: "请填写公司简称", trigger: "change" },
        ],
        businessLicensePic: [
          { required: true, message: "请添加营业执照", trigger: "change" },
        ],
        businessInfoChange: [
          { required: true, message: "请添加商户信息变更申请表", trigger: "change" },
        ],
        businessAddress: [
          { required: true, message: "请填写详细地址", trigger: "change" },
        ],
        shopPic: [
          {  type: 'array' ,required: true, message: "请添加营业场所照", trigger: "change" },
          // 至少3张
          { validator: (rule, value, callback) => {
            if (value.length < 3) {
              callback(new Error("至少上传3张图片"));
            } else {
              callback();
            }
          }, trigger: "change" },
        ],
        bankCategory: [
          { required: true, message: "请选择开户行", trigger: "change" },
        ],
        province: [
          { required: true, message: "请选择省", trigger: "change" },
        ],
        city: [
          { required: true, message: "请选择市", trigger: "change" },
        ],
        bankAddress: [
          { required: true, message: "请选择区县", trigger: "change" },
        ],
        bankName: [
          { required: true, message: "请选择开户支行", trigger: "change" },
        ],
        bankAccountName: [
          { required: true, message: "请填写银行账户名", trigger: "change" },
        ],
        bankAccount: [
          { required: true, message: "请填写银行账号", trigger: "change" },
        ],
      },
      provinceOptions: [],
      cityOptions: [],
      categoryOptions: [], // 总行数据
      addressOptions: [], // 所在地数据
      bankNumberOptions: [], // 开户行数据
      props: {
        lazy: true,
        lazyLoad (node, resolve) {
          const { level } = node;
          getEazyCityList({
            id: node.value || 0
          }).then((res) => {
            if (res.code === 200) {
              const  nodes = res.data.map(item => ({
                value: item.id,
                label: item?.addrName || '',
                leaf: level >= 2
              }));
              resolve(nodes);
            }
          });
        }
      },
      isEdit: false,
      approveStatusObj:{
        "R":"进件中",
        "A":"审批中",
        "F":"进件失败",
        "S":"进件成功",
        "M":"修改审批中"
      },
      rowDetail: undefined
    };
  },

  created() {
    this.getCategory()
    this.getProvince()
  },

  methods: {
    isDeal,
    changeIsLongTerm(val){
      if(val){
        this.form.idCardEndDate = new Date('9999-12-31')
      }else {
        this.form.idCardEndDate = new Date()
      }
    },
    async changeCategory(value) {
      console.log(value);
      this.form.bankCategory = value
      this.form.bankNo = ""
      this.form.bankName = ""
      this.bankNumberOptions = []
      if (value) await this.getBankNumber(value, this.form.bankAddress)
    },
    async changeProvince(value) {
      console.log(value);
      this.form.province = value
      this.form.bankNo = ""
      this.form.bankName = ""
      this.bankNumberOptions = []
      this.form.city = ''
      this.cityOptions = []
      this.form.bankAddress = ""
      this.addressOptions = []
      if (value) this.getCity()
    },
    async changeCity(value) {
      console.log(value);
      this.form.city = value
      this.form.bankNo = ""
      this.form.bankName = ""
      this.form.bankAddress = ""
      this.addressOptions = []
      if (value) await this.getAddress()
    },
    async changeAddress(value) {
      console.log(value);
      this.form.bankAddress = value
      this.form.bankNo = ""
      this.form.bankName = ""
      this.bankNumberOptions = []
      if (value) await this.getBankNumber(this.form.bankCategory, value)
    },
    changeBankNumber(val){
      const { name } = this.bankNumberOptions.find(item => item.code === val)
      this.form.bankName = name
      console.log('res...', this.form.bankName, this.form.bankNo);
    },
    // getEazyCityList() {
    //   getEazyCityList().then((res) => {
    //     if (res.code === 200) {
    //       this.cityOptions = res.data;
    //     }
    //   });
    // },
    getCategory() {
      getCategory().then((res) => {
        if (res.code === 200) {
          this.categoryOptions = res.data;
        }
      });
    },
    getProvince() {
      getProvince().then((res) => {
        if (res.code === 200) {
          this.provinceOptions = res.data;
        }
      });
    },
    getCity() {
      getCity({province: this.form.province}).then((res) => {
        if (res.code === 200) {
          this.cityOptions = res.data;
        }
      });
    },
    async getBankNumber(category, address) {
      if (!category || !address) return
      await getBankNumber(
        {
          category,
          address
        }
      ).then(res => {
        if (res.code === 200) {
          this.bankNumberOptions = res.data;
        }
      })
    },
    async getAddress() {
      await getAddress({
        city: this.form.city
      }).then(res => {
        if (res.code === 200) {
          this.addressOptions = res.data;
        }
      })
    },
    //随机生成uid
    guid() {
      return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
        var r = (Math.random() * 16) | 0,
          v = c == "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      });
    },
    init: async function (row, isEdit = false ,isChangeTitle= false) {
      this.title = isChangeTitle ? "变更" : "开通"
      //更新
      if (isEdit || row.approveId) {
        await approveDetail({id: row.approveId}).then(res => {
          if (res.code === 200) {
            row = {...res.data,approveid: row.approveId}
            this.rowDetail = res.data
          }
        })
      }
      this.form = {
        ...this.form,
        memberId: row.id,
      }
      if (row.approveid) {
        this.form = {
          ...this.form,
          id: row.approveId,
        }
      }
      this.isEdit = isEdit

      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs?.form?.clearValidate();
      })
      // 进件不会进入 不确定先不改
      if (row.approveid) {
        if (row.bankCategory) {
          await this.changeCategory(row.bankCategory)
        }
        if (row.bankAddress) {
          await this.changeAddress(row.bankAddress)
        }
        const { province, city } = this.bankNumberOptions.find(item => item.code === row.bankNo)
        if (province) {
          this.form.province = province
          this.getCity()
        }
        if (city) {
          this.form.city = city
          this.getAddress()
        }
        row?.fileList?.forEach(item => {
          if (item.type === 1) {
            this.form.businessLicensePic = item.fileUrl
          } else if (item.type === 20) {
            this.form.idCardFrontPic = item.fileUrl
          } else if (item.type === 21) {
            this.form.idCardEmblemPic = item.fileUrl
          } else if (item.type === 3) {
            this.form.shopPic.push({
              name: item.fileUrl,
              url: item.fileUrl,
              uid: this.guid(),
              status: "success"
            })
          }else if(item.type === 9){
            this.form.businessInfoChange = item.fileUrl
          }
        })
        this.form = {
          ...this.form,
          ...row,
          allBusinessAddress: [row.businessProvince, row.businessCity, row.businessTown],
          idCardStartDate: row.idCardStartDate ? new Date(row.idCardStartDate) : "",
          idCardEndDate: row.idCardEndDate ? new Date(row.idCardEndDate) : "",
        };
        if(moment(row.idCardEndDate).format('YYYY-MM-DD') === '9999-12-31'){
          this.form.isLongTerm = true
        }
        await this.loadCityData(row)
      }
    },
    async loadCityData(row) {
      let returnData = []
      let level1Date = []
      let level2Date = []

      const res = await getEazyCityList({ id: 0 })
      if (res.code === 200) {
        returnData = res.data.map(item => ({
          value: item.id,
          label: item?.addrName || '',
          leaf: false
        }));
        if(row.businessAddress){
          const res1 = await getEazyCityList({ id: row.businessProvince })
          if (res1.code === 200) {
            level1Date = res1.data.map(item => ({
              value: item.id,
              label: item?.addrName || '',
              leaf: false
            }));
            if (row.businessCity){
              const res2 = await getEazyCityList({ id: row.businessCity })
              if (res2.code === 200) {
                level2Date = res2.data.map(item => ({
                  value: item.id,
                  label: item?.addrName || '',
                  leaf: true
                }));
                returnData.forEach(item => {
                  if(item.value === row.businessProvince){
                    item.children = level1Date
                    level1Date.forEach(item1 => {
                      if(item1.value === row.businessCity){
                        item1.children = level2Date
                      }
                    })
                  }
                })
                this.test_allBusinessAddress = returnData
              }
            }
          }
        }
      }
    },
    // 通过url获取base64
    getBase64FromUrl(url) {
      return fetch(url)
        .then(response => response.blob())
        .then(blob => new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onloadend = () => resolve(reader.result);
          reader.onerror = reject;
          reader.readAsDataURL(blob);
        }));
    },

    handleAvatarSuccessSecond(res, file) {
      this.form.idCardFrontPic = file.response.name;
    },

    handleAvatarSuccessThird(res, file) {
      this.form.idCardEmblemPic = file.response.name;
    },

    handleAvatarSuccessFour(res, file) {
      this.form.businessLicensePic = file.response.name;
    },

    handleAvatarSuccessSix(res, file) {
      this.form.businessInfoChange = file.response.name;
    },

    handleAvatarSuccessFive(res, file) {
      const newFile = {
        name: file.response.name,
        url: file.response.name,
        uid: file.response.uid
      };
      if(!this.form.shopPic || this.form.shopPic.length === 0) {
        this.form.shopPic = [newFile]
      }else  {
        this.form.shopPic.push(newFile)
      }
    },
    handleRemove(file, fileList) {
      console.log(file,fileList);
      this.form.shopPic = fileList
    },

    beforeAvatarUpload(file) {
      const type = ["image/jpeg", "image/jpg", "image/png", "image/svg"];
      const isJPG = type.includes(file.type);
      const isLt2M = file.size / 1024 / 1024 < 10;

      if (!isJPG) {
        this.$message.error(`图片格式错误!`);
        return false;
      }
      if (!isLt2M) {
        this.$message.error("上传会员头像图片大小不能超过 10MB!");
        return false;
      }
      return isJPG && isLt2M;
    },

    cleanForm() {
      this.dialogVisible = false;
      this.form = this.$options.data().form;
      this.rowDetail = undefined
      this.$emit("getData");
    },

    saveForm() {
      console.log('res...',this.$refs.cityCascaderRef.getCheckedNodes(false));
      this.$refs["form"].validate(async (valid) => {
        if (valid) {
          const params = {
            ...this.form,
            memberId: this.form.memberId ? parseInt(this.form.memberId) : undefined,
            idCardStartDate: this.form.idCardStartDate ? moment(this.form.idCardStartDate).format('YYYY-MM-DD'):"",
            idCardEndDate: this.form.idCardEndDate ? moment(this.form.idCardEndDate).format('YYYY-MM-DD'):"",
          };
          let fileList = []
          if (this.form.idCardFrontPic) {
            let file = {}
            file = {
              fileUrl: this.form.idCardFrontPic,
              type: 20
            }
            fileList.push(file)
          }
          if (this.form.idCardEmblemPic) {
            let file = {}
            file = {
              fileUrl: this.form.idCardEmblemPic,
              type: 21
            }
            fileList.push(file)
          }
          if (this.form.businessLicensePic) {
            let file = {}
            file = {
              fileUrl: this.form.businessLicensePic,
              type: 1
            }
            fileList.push(file)
          }

          if (this.form.businessInfoChange) {
            let file = {}
            file = {
              fileUrl: this.form.businessInfoChange,
              type: 9
            }
            fileList.push(file)
          }

          if (this.form.shopPic && this.form.shopPic.length > 0) {
            for (const item of this.form.shopPic) {
              let file = {}
              file = {
                fileUrl: item.url,
                type: 3
              }
              fileList.push(file)
            }
          }

          params.fileList = fileList
          params.businessProvince = this.$refs.cityCascaderRef.getCheckedNodes(false)?.[0]?.path?.[0] || ""
          params.businessCity = this.$refs.cityCascaderRef.getCheckedNodes(false)?.[0]?.path?.[1] || ""
          params.businessTown = this.$refs.cityCascaderRef.getCheckedNodes(false)?.[0]?.path?.[2] || ""
          this.btnLoading = true;
          if (this.isEdit) {
            approveUpdate(params)
              .then((res) => {
                this.$message.success("变更成功");
                this.cleanForm();
              })
              .finally(() => {
                this.btnLoading = false;
              });
          } else {
            approveAdd(params)
              .then((res) => {
                this.$message.success("新增成功");
                this.cleanForm();
              })
              .finally(() => {
                this.btnLoading = false;
              });
          }
        }
      });
    },
  },
};
</script>

<style>

.fatherTitle {
  font-size: 18px;
  font-weight: bolder;
}
.open-info-content {
  display: flex;
  gap: 16px;
}

.dialogStyle {
  max-height: calc(100vh - 140px);
  overflow: auto;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
</style>

<style lang="less" scoped>
.avatar-uploader-icon {
  width: 110px;
  height: 110px;
  line-height: 80px;
}
.avatar-uploader-text {
  position: absolute;
  left: 49%;
  transform: translate(-50%,0);
  top: 55px;
  color: rgba(0,0,0,0.88);
}
.avatar {
  width: 110px;
  height: 110px;
}
::v-deep .el-form-item__label {
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
}

.el-row{
  margin-right: 0!important;
}

.del-btn {
  margin-left: 30px;
  color: #F5222D;
}
.el-divider--horizontal {
  margin: 0 0 24px 0;
}
::v-deep .el-upload-list__item-actions ,::v-deep .el-upload-list__item,::v-deep .el-upload--picture-card{
  width: 110px;
  height: 110px;
}
</style>
