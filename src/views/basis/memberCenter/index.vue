<!--
 * @Description: 会员中心
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-03-22 13:13:22
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-14 19:54:19
-->
<template>
  <div class="whole">
    <el-form :model="form" ref="form">
      <el-row>
        <el-col :span="6">
          <el-form-item label="会员ID/昵称/手机号/姓名" label-width="180px">
            <el-input placeholder="请输入" v-model="form.keyword" ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="注册时间" label-width="110px">
            <el-date-picker
              style="width: 100%;"
              v-model="time"
              type="daterange"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="changeTime"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="所属机构" label-width="110px">
            <el-input placeholder="请输入" v-model="form.referrerKeyword"></el-input>
          </el-form-item>
        </el-col>
<!--        <el-col :span="6">-->
<!--          <el-form-item label="实际所属机构" label-width="110px">-->
<!--            <el-input v-model="form.realReferrerKeyword"></el-input>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
        <el-col :span="6">
          <el-form-item label="角色" label-width="110px">
            <el-select
              v-model="form.roleId"
              placeholder="请选择角色"
              style="width: 100%;"
            >
              <el-option
                v-for="dict in roleData"
                :key="dict.id"
                :label="dict.roleName"
                :value="dict.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="状态" label-width="110px">
            <el-select
              v-model="form.status"
              placeholder="请选择状态"
              style="width: 100%;"
            >
              <el-option label="正常" value="1"></el-option>
              <el-option label="禁用" value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="类型" label-width="110px">
            <el-select
              v-model="form.type"
              placeholder="请选择类型"
              style="width: 100%;"
            >
              <el-option label="未认证" :value="0"></el-option>
              <el-option label="个人" :value="1"></el-option>
              <el-option label="企业" :value="2"></el-option>
              <el-option label="个体工商户" :value="3"></el-option>
<!--              <el-option label="普通会员"></el-option>-->
<!--              <el-option label="个人会员" :value="0"></el-option>-->
<!--              <el-option label="企业会员" :value="1"></el-option>-->
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="所属项目" label-width="110px">
            <el-input placeholder="请输入" v-model="form.projectName"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="" label-width="24px">
            <el-button @click="reset"> 重置</el-button>
            <el-button type="primary" @click="getData(true)">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div>
      <div class="addRight" v-if="roleId != 8">
        <el-button type="primary" @click="addMember()">新建</el-button>
        <el-button @click="exportFile">导出</el-button>
        <el-button @click="upload.open = true">第三方权益导入</el-button>
      </div>
      <el-table :data="tableData" style="width: 100%;" border>
        <el-table-column prop="id" label="会员ID" width="90">
        </el-table-column>
        <el-table-column prop="date" label="会员" min-width="180">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image
                  v-if="scope.row.headPortrait"
                  :src="scope.row.headPortrait"
                  fit="fill"
                ></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.nickName }}</div>
                <div>
                  {{ scope.row.accountNumber }} 【{{ scope.row.userName }}】
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="所属机构" min-width="180">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image
                  v-if="scope.row.referrerHeadPortrait"
                  :src="scope.row.referrerHeadPortrait"
                  fit="fill"
                ></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.referrerNickName }} <span>({{ scope.row.referrerId}})</span> </div>
                <div>
                  {{ scope.row.referrerAccountNumber }} 【{{
                  scope.row.referrerUserName
                  }}】
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="所属项目" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.projectName || '-' }}
          </template>
        </el-table-column>
<!--        <el-table-column prop="date" label="实际所属机构" min-width="150">-->
<!--          <template slot-scope="scope">-->
<!--            <div class="span_all">-->
<!--              <div class="span_left">-->
<!--                <el-image-->
<!--                  v-if="scope.row.realReferrerHeadPortrait"-->
<!--                  :src="scope.row.realReferrerHeadPortrait"-->
<!--                  fit="fill"-->
<!--                ></el-image>-->
<!--                <img-->
<!--                  v-else-->
<!--                  src="@/assets/images/member.png"-->
<!--                  alt="donate"-->
<!--                  width="100%"-->
<!--                />-->
<!--              </div>-->
<!--              <div class="span_right">-->
<!--                <div>{{ scope.row.realReferrerNickName }}</div>-->
<!--                <div>-->
<!--                  {{ scope.row.realReferrerAccountNumber }} 【{{-->
<!--                  scope.row.realReferrerUserName-->
<!--                  }}】-->
<!--                </div>-->
<!--              </div>-->
<!--            </div>-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column prop="roleName" label="角色" width="110">
          <template slot-scope="scope">
            <span>{{ scope.row.roleName || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="typeName" label="认证类型" width="110">
          <template slot-scope="scope">
            <span>{{ scope.row.typeName || '-' }}</span>
          </template>
        </el-table-column>
<!--        <el-table-column prop="type" label="类型" min-width="50">-->
<!--          <template slot-scope="scope">-->
<!--            <span v-if="scope.row.type === 0"> 个人会员</span>-->
<!--            <span v-else-if="scope.row.type === 1"> 企业会员</span>-->
<!--            <span v-else>-</span>-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column prop="status" label="状态" width="70">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === 1">
              <span v-if="scope.row.status === 1"> 正常</span></el-tag
            >
            <el-tag v-if="scope.row.status === 2" type="danger">
              <span v-if="scope.row.status === 2"> 禁用</span></el-tag
            >
          </template>
        </el-table-column>

        <el-table-column prop="thirdParty" label="第三方权益" min-width="100">
          <template slot-scope="scope">
            <el-input v-model="scope.row.thirdParty" placeholder="请输入" size="small"
                      oninput="if(value){value=value.replace(/[^\d]/g,1)} if(value<0){value=0}"
                      @blur="e => updateThirdParty(e, scope.row)"></el-input>
          </template>
        </el-table-column>

        <el-table-column prop="subMemberList" label="Ta的会员" width="90">
          <template slot-scope="scope">
            <span @click="showSubMember(scope.row)"
                  :style="{color:(scope.row.subMemberList && scope.row.subMemberList.length ) ?'#1890ff': '', cursor:'pointer' }">
              {{ (scope.row.subMemberList && scope.row.subMemberList.length ) || 0 }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="registTime" label="注册时间" width="110">
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="{ row }">
            <template v-if="roleId != 8">
              <span v-for="(button, index) in visibleButtons(row)" :key="index" >
                <el-button type="text" @click="button.action" style="margin-right: 16px">
                  <span v-if="button.label == '禁用'" style="color: red">{{ button.label }}</span>
                  <span v-else>{{ button.label }}</span>
                </el-button>
              </span>
              <el-dropdown v-if="extraButtons(row).length > 0">
            <span class="el-dropdown-link">
              <span style="font-size: 14px;color: #1890ff">更多</span><i class="el-icon-arrow-down el-icon--right" style="color: #1890ff"></i>
            </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    v-for="(button, index) in extraButtons(row)"
                    :key="index"
                  >
                    <el-button type="text" @click="button.action" style="width: 100%; text-align: left">
                      {{ button.label }}
                    </el-button>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="form.pageNum"
        :limit.sync="form.pageSize"
        @pagination="handlePaginationChange"
      />
    </div>
    <member-dialog ref="member" @getData="getNoChangeSizeData"></member-dialog>
    <SonMember ref="sonMember" :table-data="sonTableData"></SonMember>
<!--    <selectModal ref="selectModal" @selectType="selectType"></selectModal>-->
    <userDialog ref="userDialog" @getData="getNoChangeSizeData"></userDialog>

    <authDialog ref="authDialog" @getData="getNoChangeSizeData"></authDialog>

    <opened ref="openedRef" @getData="getNoChangeSizeData"></opened>

    <import-dialog
      :open.sync="upload.open"
      :title="upload.title"
      :actionUrl="upload.actionUrl"
      :downloadUrl="upload.downloadUrl"
      :onFinish="handleImportFinish"
    />
  </div>
</template>
<script>
  import MemberDialog from "./memberDialog.vue";
  import SonMember from "./sonMember.vue";
  import {queryAll, edit, editThirdParty, updateStatus, getRole} from "@/api/basis/basis.js";
  import store from "@/store";
  // import selectModal from "./selectModal.vue";
  import userDialog from "./userDialog.vue";
  import authDialog from "./authDialog.vue";
  import opened from "./opened.vue";
  import ImportDialog from '@/components/ImportDialog/index.vue'

  export default {
    name: "MemberCenter",
    components: {
      ImportDialog,
      MemberDialog,
      SonMember,
      // selectModal,
      userDialog,
      authDialog,
      opened
    },
    activated() {
      console.log('activated...')
      if(this.$route.query.id){
        this.form.keyword = this.$route.query.id;
        setTimeout(
          ()=>{
            this.$router.push({ query: {} })
          }
        )
      }
      if(!this.loadData){
        this.initData();
      }
    },
    created() {
    },
    data() {
      return {
        form: {
          pageNum: 1,
          pageSize: 10,
          id: "",
          keyword: "",
          referrerKeyword: "",
          realReferrerKeyword: "",
          projectName: "",
          registEndTime: "",
          registStartTime: "",
          roleId: "",
          status: "",
          type: undefined
        },

        total: 0,
        time: "",
        roleData: [],
        resetForm: {
          pageNum: 1,
          pageSize: 10,
        },
        tableData: [],
        src:
          "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
        sonTableData: [],
        roleId: store.getters.roleId,
        loadData: false,
        upload: {
          open: false,
          title: "第三方权益导入",
          actionUrl: process.env.VUE_APP_BASE_API + "/eazyMermberCenter/import/thirdParty",
          downloadUrl: 'https://cdn-newteapai.eazytec-cloud.com/thirdParty.xlsx',
          downloadName: '第三方权益导入模版'
        }
      };
    },
    mounted() {
      if(this.$route.query.id){
        this.form.keyword = this.$route.query.id;
        setTimeout(
          ()=>{
            this.$router.push({ query: {} })
          }
        )
      }
      if(!this.loadData){
        this.initData();
      }
    },
    methods: {
      initData(){
        this.loadData = true
        this.resetForm = {
          ...this.form,
          keyword: "",
        };
        this.getData();
        this.getRole();
        setTimeout(()=>{
          this.loadData = false
        },1500)

      },
      visibleButtons(row) {
        if(this.getButtons(row).length <= 3)
          return this.getButtons(row);
        return this.getButtons(row).slice(0, 2);
      },
      // 更多
      extraButtons(row) {
        if(this.getButtons(row).length <= 3)
          return [];
        return this.getButtons(row).slice(2);
      },
      getButtons(row) {
        return [
          { label: '编辑', action: () => this.editMember(row) },
          {
            label: row.status === 1 ? '禁用' : '启用',
            action: () => this.disableMember(row)
          },
          {
            label: row.userType == 0 ? '认证' : '重新认证',
            action: () => this.authMember(row, row.userType == 0 ? '认证' : '重新认证')
          },
          {
            label: row.mchtApproveStatus == 'S' ? '查看' : '开通',
            action: () => this.opened(row),
            condition: (['企业会员'].includes(row.roleName) || [2, 3].includes(row.roleId)) && row.userType != 0
          }
        ].filter(button => button.condition === undefined || button.condition);
      },
      // selectType(type){
      //   if(type === 'company'){
      //     this.$refs.userDialog.init();
      //   }else if(type == 'user'){
      //     this.$refs.member.init();
      //   }
      // },
      // 认证
      authMember(row,title){
        this.$refs.authDialog.init(row,title);
      },
      // 开通
      opened(row){
        this.$refs.openedRef.init(row);
      },
      // 变更
      alter(row){

        this.$refs.openedRef.init(row, true, true);
      },
      showSubMember(row) {
        if (row?.subMemberList?.length > 0) {
          this.$refs.sonMember.dialogVisible = true;
          this.$refs.sonMember.title = row.accountNumber + '【' + row.userName + '】' + ' 会员列表';
          this.sonTableData = row.subMemberList;
        }
      },

      // 处理查询参数
      handleParams() {
        let assignObj = {}
        if(this.form.type !== undefined){
          if(this.form.type === 0){
            assignObj = {
              userType: 0
            }
          }else if(this.form.type === 1){
            assignObj = {
              userType: 1,
              type: 0
            }
          }else if(this.form.type === 2){
            assignObj = {
              userType: 1,
              type: 1
            }
          } else if (this.form.type === 3){
            assignObj = {
              userType: 1,
              type: 2
            }
          }
        }
        return {
          ...this.form,
          ...assignObj
        };
      },

      handleList(list) {
        return list.map((item) => {
          item.thirdParty = item.thirdParty < 0 ? 0 : item.thirdParty
          return item
        })
      },

      // 获取数据
      getData(refresh) {
        if (refresh) {
          this.form.pageNum = 1;
        }
        queryAll(this.handleParams()).then((res) => {
          this.tableData = this.handleList(res.data.records);
          this.total = res.data.total;
        });
      },

      // 获取数据 不改变页码
      getNoChangeSizeData() {
        this.getData()
      },

      handlePaginationChange(pagination) {
        const { page, limit } = pagination;
        this.form.pageNum = page;
        this.form.pageSize = limit;
        this.getData();
      },

      getRole() {
        getRole().then((res) => {
          this.roleData = res.data;
        });
      },

      // 重置
      reset() {
        this.form = this.$options.data().form;
        this.time = "";
        this.getData();
      },

      // 禁用
      disableMember(row) {
        if (row.status === 1) {
          this.$confirm("是否确认禁用该条记录?", "禁用确认", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              const params = {
                id: row.id,
                status: 2,
              };
              updateStatus(params).then((res) => {
                this.$message({
                  type: "success",
                  message: "禁用成功!",
                });
                this.getData();
              });
            })
            .catch(() => {
            });
        } else {
          this.$confirm("是否确认启用该记录?", "启用确认", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              const params = {
                id: row.id,
                status: 1,
              };
              updateStatus(params).then((res) => {
                this.$message({
                  type: "success",
                  message: "启用成功!",
                });
                this.getData();
              });
            })
            .catch(() => {
            });
        }
      },

      // 新增会员
      addMember() {
        // this.$refs.selectModal.open()

        this.$refs.member.init();
      },

      // 编辑会员
      editMember(row) {
        if(row.type == 1 || row.type == 2){
          this.$refs.userDialog.init(row);
          return
        }

        this.$refs.member.init(row);
      },

      // 导出
      exportFile() {
        this.download(
          "/eazyMermberCenter/export",
          this.handleParams(),
          `会员中心.xlsx`
        );
      },

      // 改变申请日期
      changeTime(time) {
        this.form.registStartTime = this.$dayjs(this.time[0]).format(
          "YYYY-MM-DD 00:00:00"
        );
        this.form.registEndTime = this.$dayjs(this.time[1]).format(
          "YYYY-MM-DD 23:59:59"
        );
      },

      handleImportFinish(res) {
        this.getData(res.data)
      },

      updateThirdParty(e, row) {
        if (!!e.target.value) {
          if (!/^\d+$/.test(e.target.value)) {
            this.$message.warning('只可输入整数');
            row.thirdParty = ''
            return
          }
          editThirdParty({
            id: row.id,
            thirdParty: Number(e.target.value),
          }).then((res) => {
            this.$message.success('编辑成功');
            this.getData()
          });
        } else {
          this.$message.warning('第三方权益不可为空');
        }
      },
    },
  };
</script>
<style scoped lang="scss">
  .addRight {
    float: right;
    margin: 10px 0;
  }
</style>
