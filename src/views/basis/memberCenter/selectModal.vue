<template>
  <el-dialog
    title="选择身份"
    :visible.sync="dialogVisible"
    width="30%"
    :before-close="handleClose">
    <div class="modal-content">
      <div class="item" @click="selectModal('company')">
        <i style="font-size: 42px" class="el-icon-office-building"></i>
        <span class="item-text">企业会员</span>
      </div>
      <div class="item"  @click="selectModal('user')">
        <i style="font-size: 42px" class="el-icon-user"></i>
        <span class="item-text">个人会员</span>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "selectModal",
  data() {
    return {
      dialogVisible: false,
    };
  },
  methods: {
    handleClose() {
      this.dialogVisible = false;
    },
    open() {
      this.dialogVisible = true;
    },
    selectModal(type){
      this.$emit('selectType', type)
      this.dialogVisible = false;
    }
  },
}
</script>

<style scoped lang="scss">
  .modal-content {
    display: flex;
    gap: 24px;
    .item {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
      gap: 12px;
      border: 1px solid #ebeef5;
      padding: 24px 0;
      cursor: pointer;
      .item-text {
        font-size: 18px;
        font-weight: 500;
      }
    }
  }
</style>
