<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="720"
    :close-on-click-modal="false"
    :before-close="cleanForm"
    :modal-append-to-body="false"
    :modal="false"
    destroy-on-close
    >
    <el-table :data="tableData" style="width: 100%;" border max-height="680">
      <el-table-column prop="id" label="会员ID" min-width="60">
      </el-table-column>
      <el-table-column prop="date" label="会员" min-width="150">
        <template slot-scope="scope">
          <div class="span_all">
            <div class="span_left">
              <el-image
                v-if="scope.row.headPortrait"
                :src="scope.row.headPortrait"
                fit="fill"
              ></el-image>
              <img
                v-else
                src="@/assets/images/member.png"
                alt="donate"
                width="100%"
              />
            </div>
            <div class="span_right">
              <div>{{ scope.row.nickName }}</div>
              <div>
                {{ scope.row.accountNumber }} 【{{ scope.row.userName }}】
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="roleName" label="角色" min-width="50">
      </el-table-column>
      <el-table-column prop="status" label="状态" min-width="50">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 1">正常</el-tag>
          <el-tag v-if="scope.row.status === 2" type="danger">禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column :resizable="false" prop="registTime" label="注册时间" min-width="120">
      </el-table-column>
    </el-table>
  </el-dialog>

</template>

<script>
export default {
  name: "sonMember",
  props: {
    tableData: {
      type: Array,
      default: [],
    },
    sonAccountNumber: {
      type: String,
      default: "",
    },
    sonUserName: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      dialogVisible: false,
      title: ''
    };
  },
  methods: {
    cleanForm() {
      this.dialogVisible = false;
    },
  },
}
</script>

<style scoped lang="less">

</style>
