<!--
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-03-25 08:34:09
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-18 11:00:46
-->
<template>
  <!--  <el-dialog-->
  <!--    :title="title"-->
  <!--    :visible.sync="dialogVisible"-->
  <!--    width="60%"-->
  <!--    center-->
  <!--    :modal-append-to-body="false"-->
  <!--    :before-close="cleanForm"-->
  <!--    :modal="false"-->
  <!--    :close-on-click-modal="false"-->
  <!--    destroy-on-close-->
  <!--  >-->
  <a-drawer
    :title="title"
    width="560"
    :closable="true"
    @close="cleanForm"
    :visible="dialogVisible"
    :maskClosable="false"
    class="drawer-default"
  >
    <div class="dialogStyle">
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <div class="fatherTitle">基本信息</div>
        <el-row style="margin-top: 10px;" :gutter="24">
          <el-col :span="12">
            <el-form-item label="会员ID" label-width="100px" prop="id">
              <el-input placeholder="请输入" v-model="form.id" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="会员头像" label-width="100px" prop="headPortrait">
              <el-upload
                class="avatar-uploader"
                :action="upLoadUrl"
                :show-file-list="false"
                type="small"
                :on-success="handleAvatarSuccessFirst"
                :before-upload="beforeAvatarUpload"
              >
                <img
                  v-if="form.headPortrait"
                  :src="form.headPortrait"
                  class="avatar"
                />
                <template v-else>
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                  <div class="avatar-uploader-text">
                    上传照片
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="角色" label-width="100px" prop="roleId">
              <el-select
                v-model="form.roleId"
                placeholder="请选择角色"
                style="width: 100%;"
              >
                <el-option
                  v-for="dict in roleData"
                  :key="dict.id"
                  :label="dict.roleName"
                  :value="dict.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号" label-width="100px" prop="accountNumber">
              <el-input placeholder="请输入" v-model="form.accountNumber"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="昵称" label-width="100px" prop="nickName">
              <el-input placeholder="请输入" v-model="form.nickName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司名称" label-width="100px" prop="userName">
              <el-input placeholder="请输入" v-model="form.userName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="统一社会信用代码" label-width="100px" prop="businessLicense">
              <el-input placeholder="请输入" v-model="form.businessLicense"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.id" label="登录密码" label-width="100px">
              <el-input
                placeholder="请输入密码"
                v-model="form.loginPassword"
                show-password
              ></el-input>
            </el-form-item>
            <el-form-item
              v-if="!form.id"
              label="登录密码"
              label-width="100px"
              prop="loginPassword"
            >
              <el-input
                placeholder="请输入密码"
                minlength="6"
                v-model="form.loginPassword"
                show-password
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              v-if="!form.id"
              label="交易密码"
              label-width="100px"
              prop="transactionPassword"
            >
              <el-input
                placeholder="请输入密码"
                minlength="6"
                v-model="form.transactionPassword"
                show-password
              ></el-input>
            </el-form-item>
            <el-form-item v-if="form.id" label="交易密码" label-width="100px">
              <el-input
                placeholder="请输入密码"
                v-model="form.transactionPassword"
                show-password
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="所属机构姓名"
              label-width="100px"
              prop="referrerUserName"
            >
              <el-input :readonly="true" placeholder="请输入" v-model="form.referrerUserName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="所属机构"
              label-width="100px"
              prop="referrerId"
            >
              <el-select
                v-model="form.referrerId"
                placeholder="请按照手机/ID/昵称/姓名搜索"
                style="width: 100%;"
                filterable
                @clear="saveUserList = userList"
                :filter-method="filterMethod"
                @change="changeReferrers"
                @blur="selectReferredBlur"
              >
                <el-option
                  v-for="item in saveUserList"
                  :key="item.userId"
                  :label="item.userName"
                  :value="item.userId"
                >
                  <span> [{{ item.userId }}]{{ item.userName }}</span>
                </el-option>
              </el-select>
<!--              <el-input-->
<!--                placeholder="请输入"-->
<!--                v-model="form.referrerId"-->
<!--                oninput="value=value.replace(/[^0-9.]/g,'')"-->
<!--                maxlength="6"-->
<!--                @blur="onReferrer"-->
<!--              >-->
<!--              </el-input>-->
            </el-form-item>
          </el-col>
<!--          <el-col :span="12">
            <el-form-item
              label="营业执照"
              label-width="110px"
              prop="businessLicensePic"
            >
              <el-upload
                class="avatar-uploader"
                :action="upLoadUrl"
                :show-file-list="false"
                :on-success="handleAvatarSuccessThird"
                :before-upload="beforeAvatarUpload"
              >
                <img
                  v-if="form.businessLicensePic"
                  :src="form.businessLicensePic"
                  class="avatar"
                />
                <template v-else>
                  <i class="el-icon-tickets avatar-uploader-icon"></i>
                  <div class="avatar-uploader-text">
                    暂无
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>-->
        </el-row>
<!--        <el-row :gutter="24">
            <el-col :span="8">
                <el-form-item
                  label="证件生效日期"
                  label-width="110px"
                  prop="validPeriodStart"
                >
                  <el-date-picker
                    style="width: 100%"
                    v-model="form.validPeriodStart"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择证件生效日期">
                  </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="证件失效日期"
                label-width="110px"
                prop="validPeriodEnd"
              >
                <el-date-picker
                  v-if="this.form.validPeriodEnd != '长期'"
                  style="width: 100%"
                  v-model="form.validPeriodEnd"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择证件失效日期">
                </el-date-picker>
                <span v-else>长期</span>
              </el-form-item>
          </el-col>
          <el-col :span="8">
              <el-form-item
                label="是否长期"
                label-width="110px"
                prop="isLongTerm"
              >
                <el-radio-group v-model="isLongTerm" @change="longTermChange">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>-->
        <div class="fatherTitle" v-if="form.bankAccountList && form.bankAccountList.length >  0">
          银行账户
<!--          <el-button type="text" @click="addBankAccount" style="margin-left: 16px">-->
<!--            新增银行账户-->
<!--          </el-button>-->
        </div>
        <el-tabs v-model="editableTabsValue"  type="card"  @edit="handleTabsEdit">
          <el-tab-pane
            :key="item.name"
            v-for="(item, index) in form.bankAccountList"
            :label="`银行账户${index + 1}`"
            :name="index+''"
            :closable="false"
          >
            <div  :key="index">
              <el-row style="margin-top: 10px;" :gutter="24">
                <el-col :span="12">
                  <el-form-item
                    label="开户行"
                    label-width="100px"
                    prop="bankAddress"
                  >
                    <el-input v-model="item.bankAddress" placeholder="请输入"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="开户支行"
                    label-width="100px"
                    prop="bankBranchAddress"
                  >
                    <el-input v-model="item.bankBranchAddress" placeholder="请输入"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item
                    label="银行账户名"
                    label-width="100px"
                    prop="bankAccountName"
                  >
                    <el-input v-model="item.bankAccountName" placeholder="请输入"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="银行账号"
                    label-width="100px"
                    prop="bankAccount"
                  >
                    <el-input v-model="item.bankAccount" placeholder="请输入"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item
                    label="联行号"
                    label-width="100px"
                    prop="bankNo"
                  >
                    <el-input v-model="item.bankNo" placeholder="请输入"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="账户类型"
                    label-width="100px"
                    prop="type"
                  >
                   <el-select v-model="item.type" placeholder="请选择" style="width: 100%;">
                      <el-option label="个人账户" :value="1"></el-option>
                      <el-option label="对公账户" :value="2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-divider></el-divider>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-form>
    </div>

    <!--    <span slot="footer" class="dialog-footer">-->
    <div class="drawer-bootom-button" :class="[this.form.id ? 'flex justify-between' : '']">
      <a-popconfirm v-if="this.form.id" title="确认注销吗？" @confirm="saveForm(3)">
        <a-button type="danger">注 销</a-button>
      </a-popconfirm>
      <div>
        <a-button type="primary"  style="margin-right: 8px" @click="saveForm">提 交</a-button>
        <a-button  @click="cleanForm">取 消</a-button>
      </div>
    </div>
    <!--    </span>-->
    <!--  </el-dialog>-->
  </a-drawer>
</template>
<script>
import {add, edit, getRole, getUserName, queryReferrer} from "@/api/basis/basis.js";
import {valiPassword, valiPassword1, valiPhone} from "@/utils/validate";
import {isDeal} from "@/utils/utils";
import moment from 'moment'

export default {
  name: "UserDialog",
  data() {

    return {
      upLoadUrl: process.env.VUE_APP_BASE_API + "/pic/upload",
      dialogVisible: false,
      title: "新增企业会员",
      saveFlag: false,
      roleData: [],
      isLongTerm: undefined,
      form: {
        id: "",
        type: undefined,
        accountNumber: "",
        roleId: "",
        userName: "",
        businessLicense: "",
        headPortrait: "",
        nickName: "",
        loginPassword: "",
        transactionPassword: "",
        referrerId: "",
        referrerUserName: "",
        businessLicensePic: "",
        validPeriodStart: '',
        validPeriodEnd: '',
        bankAccountList: [
          // {
          //   bankAccount: "",
          //   bankAccountName: "",
          //   bankAddress: "",
          // },
        ],
      },
      rules: {
        accountNumber: [
          { required: true, message: "请输入正确手机号", trigger: "change" },
          { validator: valiPhone, trigger: "blur" },
        ],
        userName: [
          { required: true, message: "请填写公司名称", trigger: "change" },
        ],
        businessLicense: [
          { required: true, message: "请输入统一社会信用代码", trigger: "change" },
        ],
        headPortrait: [
          { required: false, message: "请添加会员头像", trigger: "change" },
        ],
        nickName: [
          { required: true, message: "请填写昵称", trigger: "change" },
        ],
        referrerId: [
          { required: true, message: "请选择所属机构", trigger: "change" },
        ],
        loginPassword: [
          { required: true, message: "请填写登录密码", trigger: "change" },
          {
            trigger: "blur",
            validator: valiPassword,
          },
        ],
        transactionPassword: [
          { required: true, message: "请填写交易密码", trigger: "change" },
          {
            trigger: "blur",
            validator: valiPassword1,
          },
        ],
        businessLicensePic: [
          { required: true, message: "请添加营业执照", trigger: "change" },
        ],
        roleId: [{ required: true, message: "请选择角色", trigger: "change" }],
        // validPeriodStart: [
        //   { required: true, message: "请选择证件生效日期", trigger: "change" },
        // ],
        // validPeriodEnd: [
        //   { required: true, message: "请选择证件失效日期", trigger: "change" },
        // ],
      },
      editableTabsValue: '0',
      saveUserList: [],
      userList:[]
    };
  },

  created() {
    this.getRole();
  },

  methods: {
    isDeal,
    longTermChange(value) {
      // console.log('???????', value)
      this.isLongTerm = value;
      if (value == 1) {
        this.form.validPeriodEnd = '长期';
      } else {
        this.form.validPeriodEnd = '';
      }
    },
    selectReferredBlur(e){
      setTimeout(()=>{
        if(!this.form.referrerId){
          this.form.referrerUserName = '';
          this.saveUserList = this.userList;
        }
      },300)
    },
    changeReferrers(referrerId){
      console.log(referrerId);
      if(!referrerId){
        this.form.referrerUserName = '';
        return
      }

      this.userList.forEach(item => {
        if(item.userId === referrerId){
          this.form.referrerUserName = item.userName;
        }
      })

    },
    filterMethod(query) {
      console.log(query,this.userList);
      if(!query){
        this.saveUserList = this.userList;
      }else {
        this.saveUserList = this.userList.filter((item) => {
          return (
            (item.nickName+'').indexOf(query) > -1 ||
            (item.accountNumber+'').indexOf(query) > -1 ||
            (item.userId+'').indexOf(query) > -1 ||
            (item.userName+'').indexOf(query) > -1
          );
        });
      }
    },
    async init(row) {
      this.dialogVisible = true;
      this.$refs?.form?.clearValidate();
      console.log('row....', row.bankAccountList);
      await this.getUserList()
      if (row) {
        this.title = "编辑企业会员";
        this.isLongTerm = row.validPeriodEnd && row.validPeriodEnd == '长期' ? 1 : 0;
        this.form = {
          ...row,
          validPeriodStart: !row.validPeriodStart ? '' : new Date(moment(row.validPeriodStart)),
          validPeriodEnd: !row.validPeriodEnd ? '' : (row.validPeriodEnd == '长期' ? '长期' : new Date(moment(row.validPeriodEnd))),
          loginPassword: "",
          transactionPassword: "",
          bankAccountList: row.bankAccountList || [],
          headPortrait: row?.headPortrait || require('@/assets/images/member.png')
        };
      }
    },

    async getUserList() {
      await queryReferrer().then((res) => {
        if (res.code === 200) {
          this.userList = res.data;
          this.saveUserList = res.data;
        }
      });
    },

    getRole() {
      getRole().then((res) => {
        this.roleData = res.data;
      });
    },

    // 新增银行账户
    addBankAccount() {
      this.form.bankAccountList.push({
        bankAccount: "",
        bankAccountName: "",
        bankAddress: "",
      });
    },

    // 删除银行账户
    deleteBankAccount(index) {
      this.form.bankAccountList.splice(index, 1);
      this.editableTabsValue = '0';
    },
    handleTabsEdit(targetName, action) {
      if (action === 'remove') {
        this.deleteBankAccount(targetName);
      }
    },

    handleAvatarSuccessFirst(res, file) {
      this.form.headPortrait = file.response.name;
    },

    handleAvatarSuccessThird(res, file) {
      this.form.businessLicensePic = file.response.name;
    },
    beforeAvatarUpload(file) {
      const type = ["image/jpeg", "image/jpg", "image/png", "image/svg"];
      const isJPG = type.includes(file.type);
      const isLt2M = file.size / 1024 / 1024 < 10;

      if (!isJPG) {
        this.$message.error(`图片格式错误!`);
        return false;
      }
      if (!isLt2M) {
        this.$message.error("上传会员头像图片大小不能超过 10MB!");
        return false;
      }
      return isJPG && isLt2M;
    },

    cleanForm() {
      this.dialogVisible = false;
      this.saveFlag = false;
      this.title = "新增企业会员";
      this.isLongTerm = '';
      this.form = this.$options.data().form;
      this.editableTabsValue = '0';
      this.$emit("getData");
    },

    saveForm(status) {
      if (this.saveFlag) {
        return;
      }
      this.saveFlag = true;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const { id, referrerId, roleId, headPortrait, bankAddress, bankAccountName, bankAccount, validPeriodStart, validPeriodEnd, ...rest } = this.form
          const params = {
            ...rest,
            id: id ? parseInt(id) : undefined,
            roleId: parseInt(roleId),
            headPortrait: headPortrait.includes('data:image/png') ? '': headPortrait,
            status: typeof status === 'number' ? status : undefined,
            validPeriodStart: !this.form.validPeriodStart ? "" : (this.form.validPeriodStart == '长期' ? this.form.validPeriodStart : moment(this.form.validPeriodStart).format('YYYY-MM-DD')),
            validPeriodEnd: !this.form.validPeriodEnd ? "" : (this.form.validPeriodEnd == '长期' ? this.form.validPeriodEnd : moment(this.form.validPeriodEnd).format('YYYY-MM-DD')),
          };
          if (referrerId && String(referrerId).indexOf('*') === -1) {
            Object.assign(params, {
              referrerId: parseInt(referrerId),
            })
          }
          if (id) {
            edit(params)
              .then((res) => {
                this.$message.success(status === 3 ? '注销成功' : "编辑成功");
                this.cleanForm();
              })
              .finally(() => {
                this.saveFlag = false;
              });
          } else {
            add({
              ...params
            })
              .then((res) => {
                this.$message.success("新增成功");
                this.cleanForm();
              })
              .finally(() => {
                this.saveFlag = false;
              });
          }
        } else {
          this.saveFlag = false;
        }
      });
    },
    // 获取买方姓名
    onReferrer() {
      if (this.form.referrerId !== "") {
        const params = {
          memberId: this.form.referrerId,
        };
        getUserName(params).then((res) => {
          if (res.code === 200) {
            this.form.referrerUserName = res.data.userName;
          } else {
            uni.showToast({
              title: res.msg || "服务响应异常",
              icon: "error",
            });
          }
        });
      }
    },
  },
};
</script>

<style>
.fatherTitle {
  font-size: 18px;
  font-weight: bolder;
}

.sonTitle {
  font-weight: bold;
  line-height: 22px;
  margin-top: 8px;
}

.dialogStyle {
  //height: 450px;
  //padding: 10px;
  max-height: calc(100vh - 140px);
  overflow: auto;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>

<style lang="less" scoped>
.avatar-uploader-icon {
  width: 110px;
  height: 110px;
  line-height: 80px;
}
.avatar-uploader-text {
  position: absolute;
  left: 49%;
  transform: translate(-50%,0);
  top: 55px;
  color: rgba(0,0,0,0.88);
}
.avatar {
  width: 110px;
  height: 110px;
}
::v-deep .el-form-item__label {
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
}

.el-row{
  margin-right: 0!important;
}

.del-btn {
  margin-left: 30px;
  color: #F5222D;
}
.el-divider--horizontal {
  margin: 0 0 24px 0;
}


</style>
