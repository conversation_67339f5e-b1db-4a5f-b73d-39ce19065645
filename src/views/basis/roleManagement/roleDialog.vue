<!--
 * @Description: 
 * @version: 
 * @Author: 孙姜2307
 * @Date: 2024-03-25 08:34:09
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-03-28 16:22:06
-->
<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="30%"
    center
    :modal-append-to-body="false"
    :before-close="cleanForm"
    :modal="false"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <el-form ref="form" :model="form" :rules="rules">
      <el-row style="margin-top: 10px;">
        <el-col :span="24">
          <el-form-item label="角色名" label-width="90px" prop="roleName">
            <el-input v-model="form.roleName" maxlength="10" show-word-limit>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否默认" label-width="90px" prop="isDefault">
            <el-radio-group v-model="form.isDefault">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="2">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="排序" label-width="90px" prop="sequence">
            <el-input v-model="form.sequence"> </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" size="small" @click="saveForm">确 定</el-button>
      <el-button size="small" @click="cleanForm">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { addRole, editRole } from "@/api/basis/basis.js";
export default {
  name: "RoleDialog",
  data() {
    return {
      dialogVisible: false,
      title: "新增角色",
      saveFlag: false,

      form: {
        id: "",
        roleName: "",
        isDefault: "",
        sequence: "",
      },
      rules: {
        roleName: [
          { required: true, message: "请填写角色名", trigger: "change" },
        ],
        isDefault: [
          { required: true, message: "请选择是否默认", trigger: "change" },
        ],
        sequence: [
          { required: true, message: "请填写排序", trigger: "change" },
        ],
      },
    };
  },

  methods: {
    init(row) {
      this.dialogVisible = true;
      if (row) {
        this.title = "编辑角色";
        this.form = {
          ...row,
        };
      }
    },

    cleanForm() {
      this.dialogVisible = false;
      this.saveFlag = false;
      this.form = {
        id: "",
        roleName: "",
        isDefault: "",
        sequence: "",
      };
    },

    saveForm() {
      if (this.saveFlag) {
        return;
      }
      this.saveFlag = true;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const params = {
            ...this.form,
            id: this.form.id ? parseInt(this.form.id) : undefined,
          };
          if (this.form.id) {
            editRole(params)
              .then((res) => {
                this.$message.success("编辑成功");
                this.cleanForm();
                this.$emit("getData");
              })
              .finally(() => {
                this.saveFlag = false;
              });
          } else {
            addRole(params)
              .then((res) => {
                this.$message.success("新增成功");
                this.cleanForm();
                this.$emit("getData");
              })
              .finally(() => {
                this.saveFlag = false;
              });
          }
        } else {
          this.saveFlag = false;
        }
      });
    },
  },
};
</script>

<style></style>
