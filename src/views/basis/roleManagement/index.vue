<!--
 * @Description: 角色管理
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-03-22 13:13:22
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-14 19:54:47
-->
<template>
  <div class="whole">
    <div>
      <div class="addRight">
        <el-button type="primary" @click="addMember()">新建</el-button>
        <el-button @click="exportFile">导出</el-button>
      </div>
      <el-table :data="tableData" style="width: 100%;" border>
        <el-table-column prop="sequence" label="显示顺序" min-width="100">
        </el-table-column>
        <el-table-column prop="roleName" label="角色" min-width="50">
        </el-table-column>

        <el-table-column prop="roleStatus" label="状态" min-width="50">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.roleStatus === 1">
              <span v-if="scope.row.roleStatus === 1"> 正常</span></el-tag
            >
            <el-tag v-if="scope.row.roleStatus === 2" type="danger">
              <span v-if="scope.row.roleStatus === 2"> 禁用</span></el-tag
            >
          </template>
        </el-table-column>

        <el-table-column prop="isDefault" label="设为默认" min-width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.isDefault === 1"> 是</span>
            <span v-if="scope.row.isDefault === 2"> 否</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="操作" min-width="100">
          <template slot-scope="{ row }">
            <el-button type="text" @click="editMember(row)">
              编辑
            </el-button>

            <el-button
              type="text"
              @click="disableMember(row)"
              v-if="row.roleStatus === 1"
            >
              <span style="color: red;"> 禁用 </span>
            </el-button>
            <el-button
              type="text"
              @click="disableMember(row)"
              v-if="row.roleStatus === 2"
            >
              启用
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="form.pageNum"
        :limit.sync="form.pageSize"
        @pagination="getList"
      />
    </div>
    <role-dialog ref="role" @getData="getData"></role-dialog>
  </div>
</template>
<script>
import { queryAllRole, editRole } from "@/api/basis/basis.js";
import RoleDialog from "./roleDialog.vue";
export default {
  name: "MemberCenter",
  components: {
    RoleDialog,
  },
  data() {
    return {
      form: {
        pageNum: 1,
        pageSize: 10,
      },

      tableData: [],
      total: 0,
      src:
        "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    // 获取数据
    getData() {
      const params = {
        ...this.form,
        pageNum: 1,
        pageSize: 10,
      };
      queryAllRole(params).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
        this.form.pageNum = 1;
      });
    },

    // 分页
    getList() {
      const params = {
        ...this.form,
      };
      queryAllRole(params).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
      });
    },

    // 新增会员
    addMember() {
      this.$refs.role.init();
    },

    // 编辑会员
    editMember(row) {
      this.$refs.role.init(row);
    },

    // 禁用
    disableMember(row) {
      if (row.roleStatus === 1) {
        this.$confirm("是否确认禁用该条记录?", "禁用确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            const params = {
              ...row,
              roleStatus: 2,
            };
            editRole(params).then((res) => {
              this.$message({
                type: "success",
                message: "禁用成功!",
              });
              this.getData();
            });
          })
          .catch(() => {});
      } else {
        this.$confirm("是否确认启用该记录?", "启用确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            const params = {
              ...row,
              roleStatus: 1,
            };
            editRole(params).then((res) => {
              this.$message({
                type: "success",
                message: "启用成功!",
              });
              this.getData();
            });
          })
          .catch(() => {});
      }
    },

    // 导出
    exportFile() {
      const params = {
        ...this.form,
      };
      this.download(
        "/eazyRole/export-role-list",
        { ...params },
        `角色管理.xlsx`
      );
    },
  },
};
</script>
<style scoped lang="scss">
.addRight {
  float: right;
  margin: 10px 0;
}
</style>
