<!--
 * @Description:
 * @version:
 * @Author:   注册协议
 * @Date:
 * @LastEditors:
 * @LastEditTime:
-->
<template>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px" class="demo-ruleForm formbox p-24">
        <el-form-item label="服务协议" prop="registText" class="hightedit">
            <editor
                v-model="ruleForm.registText"
                ref="editor"
                :min-height="192"
            />
        </el-form-item>

        <el-form-item label="隐私协议" prop="agreementDesc" class="hightedit">
            <editor
                v-model="ruleForm.agreementDesc"
                ref="editor"
                :min-height="192"
            />
        </el-form-item>

        <el-form-item label="交易规则" prop="transactionRule" class="hightedit">
            <editor
                v-model="ruleForm.transactionRule"
                ref="editor"
                :min-height="192"
            />
        </el-form-item>

        <el-form-item label="风险提示" prop="riskWarning" class="hightedit">
            <editor
                v-model="ruleForm.riskWarning"
                ref="editor"
                :min-height="192"
            />
        </el-form-item>

        <el-form-item label="合规交易承诺书" prop="complianceTransaction" class="hightedit">
            <editor
                v-model="ruleForm.complianceTransaction"
                ref="editor"
                :min-height="192"
            />
        </el-form-item>

        <el-form-item>
            <el-button @click="submitForm('ruleForm')">保存</el-button>
        </el-form-item>
    </el-form>
</template>

<script>
import { registrationport, agreementDetail } from "@/api/message/message.js"
export default {
    name: "registrationProtocol",
    data() {
        return {
            ruleForm: {
                registText: '',
                agreementDesc: '',
                transactionRule: '',
                riskWarning: '',
                complianceTransaction: ''
            },
            rules: {
                registText: [{ required: true, message: '服务协议不能为空', trigger: 'blur' }],
            },
        };
    },
    created() {
        this.getList();
    },
    methods: {
        getList() {
            agreementDetail().then((res) => {
                this.ruleForm = res || {}
            })
        },

        submitForm() {
            this.$refs["ruleForm"].validate((valid) => {
                if (valid) {
                    registrationport(this.ruleForm).then(response => {
                        if (response.code == 200) {
                            this.$message({
                                message: response.msg,
                                type: 'success'
                            });
                            this.getList()
                        }
                    });
                }
            });
        },
    },
};
</script>
