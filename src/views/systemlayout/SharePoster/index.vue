<!--
 * @Description:
 * @version:
 * @Author:   分享海报
 * @Date:
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-18 11:03:34
-->
<template>
  <div class="whole">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="10" :xs="24">
        <div class="lefeBox">
          <!-- <span class="lefepbox">左边</span> -->
          <div class="lefebox1">
            <img class="lefeimg" :src="ruleForm.poster" alt="" />

            <img
              v-show="ruleForm.checkboxGroup.includes('二维码')"
              class="leftlogimg"
              :src="ruleForm.strCode"
              alt=""
            />
            <div class="leftglot">
              <div class="leftgbig">
                <div class="lefttou">
                  <img
                    v-show="ruleForm.checkboxGroup.includes('头像')"
                    src="@/assets/share/avatar.jpg"
                    alt=""
                  />
                  <p v-show="ruleForm.checkboxGroup.includes('昵称')">昵称</p>
                </div>
                <!-- <p> 邀请您加入卓易紫砂街</p> -->
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="10" :xs="24">
        <div class="rightbox">
          <el-form
            :model="ruleForm"
            ref="ruleForm"
            label-width="100px"
            class="demo-ruleForm"
          >
            <!-- <img src="https://digital-transaction-dev.oss-cn-shanghai.aliyuncs.com/images/2024/04/12/17128984060809467.jpg" -->
            <!-- alt=""> -->
            <el-form-item label="图标" prop="imageUrl">
              <el-upload
                class="avatar-uploader"
                :action="upLoadUrl"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
              >
                <img
                  v-if="ruleForm.poster"
                  :src="ruleForm.poster"
                  class="avatar"
                />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </el-form-item>

            <el-form-item label="海报元素">
              <el-checkbox-group v-model="ruleForm.checkboxGroup">
                <el-checkbox-button
                  @change="pitchOnbtn(item)"
                  v-for="item in cities"
                  :label="item"
                  :key="item"
                  >{{ item }}</el-checkbox-button
                >
              </el-checkbox-group>
            </el-form-item>

            <el-form-item label="二维码地址">
              <el-input v-model="ruleForm.strCode" placeholder=""></el-input>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="submitForm('ruleForm')"
                >保存</el-button
              >
              <!-- <el-button @click="resetForm('ruleForm')">重置</el-button> -->
            </el-form-item>
          </el-form>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { posteradd, SharePosterDetail } from "@/api/message/message.js";
export default {
  name: "MemberCenter",
  data() {
    return {
      upLoadUrl: process.env.VUE_APP_BASE_API + "/pic/upload",
      cities: ["头像", "昵称", "二维码"],
      profilePhoto: false,
      nickname: false,
      qrcode: false,
      ruleForm: {
        poster: "",
        checkboxGroup: [],
        name: "", // 昵称
        pic: "", // 头像
        strCode: "", // 二维码
      },

      queryForm: {},
      resetForm: {},
      tableData: [],
      total: 4,
      src:
        "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
    };
  },

  created() {
    this.getList();
  },
  methods: {
    getList() {
      // this.loading = false;
      SharePosterDetail().then((res) => {
        if (res) {
          this.ruleForm = res;
        }
      });
    },

    handleAvatarSuccess(res, file) {
      this.ruleForm.poster = file.response.name;
    },
    beforeAvatarUpload(file) {
      const type = ["image/jpeg", "image/jpg", "image/png", "image/svg"];
      const isJPG = type.includes(file.type);
      const isLt2M = file.size / 1024 / 1024 < 10;

      if (!isJPG) {
        this.$message.error(`图片格式错误!`);
        return false;
      }
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 10MB!");
        return false;
      }
      return isJPG && isLt2M;
    },

    submitForm() {
      this.$refs["ruleForm"].validate((valid) => {
        const params = {
          ...this.ruleForm,
        };
        posteradd(params).then((res) => {
          this.$message({
            type: "success",
            message: "提交成功",
          });
          this.getList();
        });
      });
    },

    // 选中展示
    pitchOnbtn(row) {
      console.log(row);
    },
  },
};
</script>
<style scoped lang="scss">
.rightbox {
  border: 1px solid;
  height: 500px;
  padding-top: 10px;
  width: 400px;
}

.avatar-uploader .el-upload {
  border: 1px solid black;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  border: 1px solid;
}

.avatar {
  width: 80px;
  height: 80px;
  display: block;
}

.lefeBox {
  display: flex;
  justify-content: space-between;
  // background-color: aqua;
  height: 500px;
  width: 500px;
  position: relative;

  .lefebox1 {
    width: 100%;
    position: relative;
  }

  .leftlogimg {
    width: 100px;
    height: 100px;
    // margin-right: 40px;
    position: absolute;
    top: 320px;
    right: 0;
    bottom: 0;
    left: 300px;
    margin: auto;
  }
}

.lefttou {
  display: flex;
}

.lefttou {
  display: flex;
  // justify-content: space-between;
  align-items: center;

  img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin: 5px;
  }

  p {
    margin: 0;
    padding: 0;
  }
}

.leftgbig {
}

.leftglot {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  bottom: 30%;
}

.lefeimg {
  width: 100%;
  height: 100%;
}

.addRight {
  float: right;
  margin: 10px 0;
}

.toClear {
  clear: both;
}

.numberStyle {
  background: #e7f8ff;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  border: 1px solid #e3f4ff;
  margin-bottom: 10px;
}
</style>
