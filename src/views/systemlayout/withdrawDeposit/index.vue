<!--
 * @Description:
 * @version:
 * @Author:   提现配置
 * @Date:
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-08 21:19:45
-->
<template>
  <div class="whole">
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="120px"
      class="demo-ruleForm"
    >
      <el-form-item label="提现手续费(%)" prop="withdrawalCommission">
        <el-input-number
          :max="100"
          :min="0.01"
          :precision="2"
          v-model="ruleForm.withdrawalCommission"
        ></el-input-number>
        <div class="text-black-35">提现手续费百分比，手续费范围0.01-100</div>
      </el-form-item>
      <el-form-item label="满多少提现" prop="withdrawalLimit">
        <el-input-number v-model="ruleForm.withdrawalLimit" :min="1" :precision="2"></el-input-number>
        <div class="text-black-35">余额满n，可以提现，单笔提现金额最小为1</div>
      </el-form-item>
      <el-form-item label="提现方式" prop="withdrawalMethod">
        <!-- <el-checkbox-group v-model="ruleForm.withdrawalMethod">
                    <el-checkbox :label="1">银行卡</el-checkbox>
                    <el-checkbox :label="2">微信</el-checkbox>
                    <el-checkbox :label="3">支付宝</el-checkbox>
                </el-checkbox-group> -->
        <el-checkbox v-model="ruleForm.withdrawalMethod" :label="1" disabled
          >银行卡</el-checkbox
        >
      </el-form-item>
      <el-form-item label="开启方式" prop="withdrawalOpen">
        <el-radio-group v-model="ruleForm.withdrawalOpen">
          <el-radio :label="2">关闭</el-radio>
          <el-radio :label="1">开启</el-radio>
        </el-radio-group>
      </el-form-item>

      <div class="mb-16">
        <span class="font-bold text-606266">手续费最大值</span>
        <span class="text-black-35">（单笔提现手续费最大值）</span>
      </div>
      <div class="flex">
        <el-form-item label="普通用户" prop="maxCharge">
          <el-input-number
            :min="0"
            :precision="2"
            v-model="ruleForm.maxCharge"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="服务中心" prop="serviceMaxCharge">
          <el-input-number
            :min="0"
            :precision="2"
            v-model="ruleForm.serviceMaxCharge"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="商家" prop="upbeatMaxCharge">
          <el-input-number
            :min="0"
            :precision="2"
            v-model="ruleForm.upbeatMaxCharge"
          ></el-input-number>
        </el-form-item>
      </div>

      <div class="mb-16">
        <span class="font-bold text-606266">单笔最大</span>
        <span class="text-black-35">（单笔提现金额最大值）</span>
      </div>
      <div class="flex">
        <el-form-item label="普通用户" prop="singleMaxCount">
          <el-input-number v-model="ruleForm.singleMaxCount" :min="0" :precision="2"></el-input-number>
        </el-form-item>
        <el-form-item label="服务中心" prop="serviceSingleMaxCount">
          <el-input-number v-model="ruleForm.serviceSingleMaxCount" :min="0" :precision="2"></el-input-number>
        </el-form-item>
        <el-form-item label="商家" prop="upbeatSingleMaxCount">
          <el-input-number v-model="ruleForm.upbeatSingleMaxCount" :min="0" :precision="2"></el-input-number>
        </el-form-item>
      </div>

      <div class="mb-16">
        <span class="font-bold text-606266">当天最大</span>
        <span class="text-black-35">（当天提现金额最大值）</span>
      </div>
      <div class="flex">
        <el-form-item label="普通用户" prop="todayMaxCount">
          <el-input-number v-model="ruleForm.todayMaxCount" :min="0" :precision="2"></el-input-number>
        </el-form-item>
        <el-form-item label="服务中心" prop="serviceTodayMaxCount">
          <el-input-number v-model="ruleForm.serviceTodayMaxCount" :min="0" :precision="2"></el-input-number>
        </el-form-item>
        <el-form-item label="商家" prop="upbeatTodayMaxCount">
          <el-input-number v-model="ruleForm.upbeatTodayMaxCount" :min="0" :precision="2"></el-input-number>
        </el-form-item>
      </div>
      <div class="mb-16">
        <span class="font-bold text-606266">免手续费会员账户</span>
      </div>
      <div class="flex">
        <el-form-item label="单笔最大" prop="freeCommissionSingleMaxCount">
          <el-input-number v-model="ruleForm.freeCommissionSingleMaxCount" :min="0" :precision="2"></el-input-number>
        </el-form-item>
        <el-form-item label="当天最大" prop="freeCommissionTodayMaxCount">
          <el-input-number v-model="ruleForm.freeCommissionTodayMaxCount" :min="0" :precision="2"></el-input-number>
        </el-form-item>
      </div>

      <!-- 选择会员账户功能 -->
      <el-form-item label="会员账户" prop="serviceUsers">
        <el-button type="text" @click="openServiceDialog">选择</el-button>
      </el-form-item>

      <!-- 会员账户列表表格，设置 height 以固定表头 -->
      <el-table :data="ruleForm.serviceUsers" class="tableStyle" border height="400">
        <el-table-column prop="userId" label="会员ID" min-width="200">
        </el-table-column>
        <el-table-column label="会员" min-width="200">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image
                  v-if="scope.row.headPortrait"
                  :src="scope.row.headPortrait"
                  fit="fill"
                ></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.nickName }}</div>
                <div>
                  {{ scope.row.accountNumber }} 【{{ scope.row.userName }}】
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column min-width="200" label="操作">
          <template slot-scope="{ row }">
            <el-button
              type="text"
              style="color: red;"
              @click="deleteServiceUser(row)"
              >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-form-item class="mt-16">
        <el-button type="primary" @click="submitForm('ruleForm')"
          >保存</el-button
        >
        <!-- <el-button @click="resetForm('ruleForm')">重置</el-button> -->
      </el-form-item>
    </el-form>

    <!-- 会员账户选择对话框 -->
    <service-center-dialog
      ref="serviceCenterDialog"
      @changeServiceUsers="changeServiceUsers"
    >
    </service-center-dialog>
  </div>
</template>

<script>
import { Allocationadd, withdrawDetail } from "@/api/message/message.js";
import ServiceCenterDialog from "./serviceCenterDialog.vue";

export default {
  components: {
    ServiceCenterDialog,
  },
  data() {
    return {
      ruleForm: {
        id: 0,
        withdrawalCommission: undefined, // 提现手续费
        maxCharge: undefined, // 普通用户最大手续费
        serviceMaxCharge: undefined, // 服务中心最大手续费
        upbeatMaxCharge: undefined, // 商家最大手续费
        withdrawalLimit: undefined, // 满多少提现
        withdrawalMethod: true, //提现方式 1:银行卡 2:微信 3:支付宝
        withdrawalOpen: undefined, //开启提现1:开启 2:关闭
        singleMaxCount: undefined, // 普通用户单笔最大
        serviceSingleMaxCount: undefined, // 服务中心单笔最大
        upbeatSingleMaxCount: undefined, // 商家单笔最大
        todayMaxCount: undefined, // 普通用户当天最大
        serviceTodayMaxCount: undefined, // 服务中心当天最大
        upbeatTodayMaxCount: undefined, // 商家当天最大
        freeCommissionSingleMaxCount: undefined, // 免手续费会员账户单笔最大
        freeCommissionTodayMaxCount: undefined, // 免手续费会员账户当天最大
        serviceUsers: [], // 选择的服务中心用户列表
      },
      rules: {
        withdrawalCommission: [
          { required: true, message: "请输入提现手续费", trigger: "blur" },
          // { min: 3, max: 5, message: '长度在 3 到 5 个字符', trigger: 'blur' }
        ],
        withdrawalLimit: [
          { required: true, message: "请输入满多少提现", trigger: "change" },
        ],
        withdrawalMethod: [
          {
            required: true,
            message: "请至少选择一个提现方式",
            trigger: "change",
          },
        ],
        withdrawalOpen: [
          { required: true, message: "请选择是否开启", trigger: "change" },
        ],
        singleMaxCount: [
          { required: true, message: "请输入普通用户单笔最大", trigger: "change" },
        ],
        serviceSingleMaxCount: [
          { required: true, message: "请输入服务中心单笔最大", trigger: "change" },
        ],
        upbeatSingleMaxCount: [
          { required: true, message: "请输入商家单笔最大", trigger: "change" },
        ],
        todayMaxCount: [
          { required: true, message: "请输入当天最大", trigger: "change" },
        ],
        serviceTodayMaxCount: [
          { required: true, message: "请输入服务中心当天最大", trigger: "change" },
        ],
        upbeatTodayMaxCount: [
          { required: true, message: "请输入商家当天最大", trigger: "change" },
        ],
        freeCommissionSingleMaxCount: [
          { required: true, message: "请输入单笔最大", trigger: "change" },
        ],
        freeCommissionTodayMaxCount: [
          { required: true, message: "请输入当天最大", trigger: "change" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },

  methods: {
    getList() {
      withdrawDetail().then((res) => {
        if (res) {
          const { withdrawalMethod, maxCharge, users = [], ...rest } = res
          this.ruleForm = {
            withdrawalMethod: withdrawalMethod === 1,
            ...(maxCharge !== null ? { maxCharge } : {}),
            ...rest,
            serviceUsers: users,
          };
        }
      });
    },

    submitForm(ruleForm) {
      this.$refs[ruleForm].validate((valid) => {
        if (valid) {
          const { withdrawalMethod, maxCharge } = this.ruleForm
          // 构造提交参数：将前端的 serviceUsers 字段转换为后端所需的 users
          const { serviceUsers, ...restForm } = this.ruleForm;
          const params = {
            ...restForm,
            users: serviceUsers || [],
            withdrawalMethod: withdrawalMethod ? 1 : 0,
            maxCharge: maxCharge >= 0 ? maxCharge : null,
          };
          Allocationadd(params).then((res) => {
            this.$message({
              message: "提交成功",
              type: "success",
            });
            this.getList();
            // this.$refs[ruleForm].resetFields();
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // resetForm(ruleForm) {
    //     this.$refs[ruleForm].resetFields();
    // }

    // 打开会员账户选择对话框
    openServiceDialog() {
      this.$refs.serviceCenterDialog.init(this.ruleForm.serviceUsers);
    },

    // 更新选择的会员账户用户
    changeServiceUsers(data) {
      console.log('接收到的服务中心用户数据:', data);
      this.ruleForm.serviceUsers = data || [];
      console.log('更新后的 serviceUsers:', this.ruleForm.serviceUsers);
      // 强制更新视图
      this.$forceUpdate();
    },

    // 删除会员账户用户
    deleteServiceUser(item) {
      this.ruleForm.serviceUsers = this.ruleForm.serviceUsers.filter(
        (val) => val.userId !== item.userId
      );
    },
  },
};
</script>
<style scoped>
.tableStyle {
  width: 600px;
  margin-left: 30px;
}

.span_all {
  display: flex;
  align-items: center;
}

.span_left {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}

.span_left img,
.span_left .el-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.span_right {
  flex: 1;
}

.span_right div {
  line-height: 1.4;
}
</style>
