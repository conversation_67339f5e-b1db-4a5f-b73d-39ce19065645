<!--
 * @Description: 服务中心选择对话框
 * @version:
 * @Author:
 * @Date:
 * @LastEditors:
 * @LastEditTime:
-->
<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="60%"
    center
    :modal-append-to-body="false"
    :before-close="cleanForm"
    :modal="false"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div class="divStyle">
      <el-form :model="form" ref="form"  @submit.native.prevent>
        <el-row>
          <el-col :span="12">
            <el-form-item label="会员ID/昵称/手机号/姓名" label-width="180px" prop="lotCode">
              <el-input v-model="form.keyword" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="" label-width="43px">
              <el-button @click="reset">重置</el-button>
              <el-button type="primary" @click="getData">查询</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table
        :data="tableData"
        class="tableStyle"
        ref="multipleTable"
        border
        :row-key="getKeys"
        height="450"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" :reserve-selection="true">
        </el-table-column>
        <el-table-column prop="userId" label="会员ID" min-width="200">
        </el-table-column>
        <el-table-column label="会员" min-width="200">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image
                  v-if="scope.row.headPortrait"
                  :src="scope.row.headPortrait"
                  fit="fill"
                ></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.nickName }}</div>
                <div>
                  {{ scope.row.accountNumber }} 【{{ scope.row.userName }}】
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="form.current"
        :limit.sync="form.pageSize"
        @pagination="getList"
      />
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" size="small" @click="save">确 定</el-button>
      <el-button size="small" @click="cleanForm">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { getServiceUsers } from "@/api/basis/basis.js";
import Pagination from "@/components/Pagination";

export default {
  name: "ServiceCenterDialog",
  components: {
    Pagination,
  },
  data() {
    return {
      dialogVisible: false,
      title: "选择服务中心",
      tableData: [],
      preSelectedIds: [], // 预选中的用户ID列表
      selectedUsers: [], // 跨页已选中的完整用户对象列表
      form: {
        current: 1,
        pageSize: 10,
        keyword: '',
      },
      total: 0,
    };
  },

  methods: {
    reset() {
      this.form = {
        ...this.$options.data().form,
      };
      this.getList();
    },
    init(row) {
      this.dialogVisible = true;
      // 记录历史选中并去重
      const map = new Map();
      (row || []).forEach(u => map.set(u.userId, u));
      this.selectedUsers = Array.from(map.values());
      this.preSelectedIds = Array.from(map.keys());
      this.getData();
    },

    getKeys(row) {
      return row.userId;
    },

    // 分页获取数据
    getData() {
      const params = {
        ...this.form,
        current: 1,
        pageSize: 10,
      };
      getServiceUsers(params).then((res) => {
        this.tableData = res.data.records;
        this.total = res.data.total;
        this.preselectRows();
      });
    },

    // 分页接口
    getList() {
      const params = {
        ...this.form,
      };
      getServiceUsers(params).then((res) => {
        this.tableData = res.data.records;
        this.total = res.data.total;
        this.preselectRows();
      });
    },

    // 选中状态变化，维护跨页选中缓存
    handleSelectionChange(selection) {
      // 当前页所有用户 ID
      const currentIds = this.tableData.map(item => item.userId);

      // 过滤掉当前页数据，准备合并新选择
      const withoutCurrent = this.selectedUsers.filter(u => !currentIds.includes(u.userId));

      // 合并并去重
      const merged = [...withoutCurrent, ...selection];
      // 使用 Map 按 userId 去重
      const map = new Map();
      merged.forEach(u => {
        map.set(u.userId, u);
      });
      this.selectedUsers = Array.from(map.values());

      // 同步 ID 列表
      this.preSelectedIds = Array.from(map.keys());
    },

    preselectRows() {
      this.$nextTick(() => {
        this.preSelectedIds.forEach(id => {
          const row = this.tableData.find(item => item.userId === id);
          if (row) {
            this.$refs.multipleTable.toggleRowSelection(row, true);
          }
        });
      });
    },

    cleanForm() {
      this.form = {
        ...this.$options.data().form,
      };
      this.dialogVisible = false;
      this.tableData = [];
      this.preSelectedIds = [];
      this.selectedUsers = [];
      this.form.current = 1
    },
    save() {
      // 最终上报前再去重一次，双保险
      const map = new Map();
      this.selectedUsers.forEach(u => map.set(u.userId, u));
      this.$emit("changeServiceUsers", Array.from(map.values()));
      this.cleanForm();
    },
  },
};
</script>
<style scoped lang="scss">
.divStyle {
  height: 580px;
  overflow: auto;
}

.span_all {
  display: flex;
  align-items: center;
}

.span_left {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}

.span_left img,
.span_left .el-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.span_right {
  flex: 1;
}

.span_right div {
  line-height: 1.4;
}
</style>
