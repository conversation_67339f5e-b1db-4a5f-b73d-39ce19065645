<!--
 * @Description:
 * @version:
 * @Author:   提货配置
 * @Date:
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-08 21:19:45
-->
<template>
  <div class="take-goods-setting">
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="180px"
      class="demo-ruleForm"
    >
      <el-form-item label="提货金费率(%)" prop="deliveryFee">
        <el-input-number
          :min="0"
          :max="100"
          class="numberWidth"
          :precision="2"
          v-model="ruleForm.deliveryFee"
        ></el-input-number>
        <div class="text-black-35">提货金=实物商品价格*提货金费率n%</div>
      </el-form-item>
      <el-form-item label="提货保证金最大额度" prop="margeMaxLevel">
        <el-input-number  class="numberWidth" v-model="ruleForm.margeMaxLevel" :min="0" :precision="2"></el-input-number>
        <div class="text-black-35">当提货保证金大于最大额度时，不允许提现</div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm('ruleForm')"
        >保存</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { deliveryadd, deliveryDetail } from "@/api/message/message.js";
export default {
  data() {
    return {
      ruleForm: {
        id: undefined,
        deliveryFee: undefined, // 提货金费率
        margeMaxLevel: undefined, // 提货保证金最大额度
      },
      rules: {
        deliveryFee: [
          { required: true, message: "请输入提货金费率", trigger: "change" },
        ],
        margeMaxLevel: [
          { required: true, message: "请输入满多少提现", trigger: "change" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },

  methods: {
    getList() {
      deliveryDetail().then((res) => {
        console.log('res...',res);
        if (res.code == 200) {
          this.ruleForm = res.data || {
            id: undefined,
            deliveryFee: undefined, // 提货金费率
            margeMaxLevel: undefined, // 提货保证金最大额度
          };
        }
      });
    },

    submitForm(ruleForm) {
      this.$refs[ruleForm].validate((valid) => {
        if (valid) {
          const params = {
            ...this.ruleForm,
          };
          deliveryadd(params).then((res) => {
            this.$message({
              message: "提交成功",
              type: "success",
            });
            this.getList();
          });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.take-goods-setting {
  padding-top: 24px;
  .numberWidth {
    width: 240px;
  }
}

</style>
