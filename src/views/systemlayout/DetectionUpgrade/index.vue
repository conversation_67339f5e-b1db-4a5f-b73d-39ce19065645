<!--
 * @Description:
 * @version:
 * @Author:   检测升级
 * @Date:
 * @LastEditors:
 * @LastEditTime:
-->
<template>
    <div class="whole">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="150px" class="demo-ruleForm">
            <el-form-item label="app强制更新" label-width="110px">
                <el-switch v-model="ruleForm.constraintUpdate" active-text="开启后app必须完成更新才可进行使用"> </el-switch>
            </el-form-item>
            <el-form-item label="最新版本号" prop="newestVersion">
                <el-input v-model="ruleForm.newestVersion" placeholder="请输入"></el-input>
                <!-- <p style="color: #ccc; margin: 0;padding: 0;"> 开启后app必须完成更新才可进行使用</p> -->
            </el-form-item>
            <el-form-item label="IOS最新版本号" prop="iosVersion">
                <el-input v-model="ruleForm.iosVersion" placeholder="请输入"></el-input>
                <!-- <p style="color: #ccc; margin: 0;padding: 0;"> 开启后app必须完成更新才可进行使用</p> -->
            </el-form-item>
            <el-form-item label="h5最新版本号" prop="h5Version">
              <el-input v-model="ruleForm.h5Version" placeholder="请输入"></el-input>
              <!-- <p style="color: #ccc; margin: 0;padding: 0;"> 开启后app必须完成更新才可进行使用</p> -->
            </el-form-item>
            <el-form-item label="更新描述" prop="updateDesc">
                <el-input type="textarea" v-model="ruleForm.updateDesc"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button @click="submitForm('ruleForm')">保存</el-button>
                <el-button @click="resetForm('ruleForm')">重置</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import { upgradeport, UpdateDetail } from "@/api/message/message.js"
export default {
    name: "MemberCenter",

    data() {
        return {

            ruleForm: {
                constraintUpdate: false,// app 是否强制更新
                newestVersion: '', // 最新版本号
                iosVersion: '', // ios最新版本号
                h5Version: '', // h5最新版本号
                updateDesc: '' // 更新描述

            },
            rules: {
                newestVersion: [{ required: true, message: '版本号不能为空', trigger: 'blur' },],
                iosVersion: [{ required: true, message: 'ios版本号不能为空', trigger: 'blur' },],
                h5Version: [{ required: true, message: 'h5版本号不能为空', trigger: 'blur' },],
                updateDesc: [
                    { required: true, message: '描述不能为空', trigger: 'blur' }
                ],
            },

            src:
                "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
        };
    },
    mounted() {
    },
    created() {
        this.getList();

    },
    methods: {

        // UpdateDetail

        /** 查询系统详情 */
        getList() {
            // this.loading = false;
            UpdateDetail().then((res) => {
                if (res) {
                    console.log(res, 2414212)
                    this.ruleForm = res
                    this.ruleForm.constraintUpdate = res.constraintUpdate == 1 ? true : false
                    // this.loading = false;
                } else {
                    this.ruleForm = {
                        constraintUpdate: false,// app 是否强制更新
                        newestVersion: '', // 最新版本号
                        iosVersion: '', // 最新版本号
                        h5Version: '', // 最新版本号
                        updateDesc: '' // 更新描述
                    }
                }

            })
        },


        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.ruleForm.constraintUpdate = this.ruleForm.constraintUpdate == true ? 1 : 0
                    upgradeport(this.ruleForm).then((res) => {
                        this.getList()

                    })
                } else {
                    console.log('error submit!!');
                    // return false;
                }
            });
        },
        // 重置
        // resetForm(formName) {
        //     this.ruleForm = {
        //         constraintUpdate: false,// app 是否强制更新
        //         newest_version: undefined, // 最新版本号
        //         updateDesc: undefined // 更新描述
        //
        //     // this.$refs[formName].resetFields();
        // },

        handleAvatarSuccess(res, file) {
            // this.imageUrl = URL.createObjectURL(file.raw);
            // console.log(this.imageUrl)
        },

        beforeAvatarUpload(file) {
            console.log(file)
            const isJPG = file.type === 'image/jpeg';
            const isLt2M = file.size / 1024 / 1024 < 2;
            if (!isJPG) {
                this.$message.error('上传头像图片只能是 JPG 格式!');
            }
            if (!isLt2M) {
                this.$message.error('上传头像图片大小不能超过 2MB!');
            }
            return isJPG && isLt2M;
        },

        // 分页 直接按queryForm 开始查询
        // getList() { },

        // 导出
        exportFile() { },
    },
};
</script>
<style scoped lang="scss">
.avatar-uploader .el-upload {
    border: 1px solid black;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
    border: 1px solid;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

.addRight {
    float: right;
    margin: 10px 0;
}

.toClear {
    clear: both;
}

.numberStyle {
    background: #e7f8ff;
    height: 40px;
    line-height: 40px;
    padding-left: 10px;
    border: 1px solid #e3f4ff;
    margin-bottom: 10px;
}
</style>
