<!--
 * @Description:
 * @version:
 * @Author:   用户列表
 * @Date:
 * @LastEditors:
 * @LastEditTime:
-->
<template>
  <div class="whole">
    <el-form :model="form" ref="form" :inline="true">
      <el-row>

        <!-- <el-form-item label="注册时间">
            <el-date-picker style="width: 100%;" v-model="dateRange" type="datetimerange"
                start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
        </el-form-item> -->

        <el-form-item label="注册时间">
          <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
                          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>

        <el-form-item label="用户名">
          <el-input v-model="queryParams.userName" @keyup.enter.native="handleQuery"></el-input>
        </el-form-item>
        <el-form-item label="账号">
          <el-input v-model="queryParams.phonenumber" @keyup.enter.native="handleQuery"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <!-- <el-input v-model="queryParams.status" @keyup.enter.native="handleQuery"></el-input> -->
          <el-select v-model="queryParams.status" placeholder="角色状态" clearable style="width: 240px">
            <el-option v-for=" (item, index) of typearr" :key="item.value" :label="item.label"
                       :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="" label-width="25px">
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button class="ml-8" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-row>
    </el-form>

    <div>
      <div class="action-flex-end">
        <el-button type="primary" @click="newAddFile">新建</el-button>
        <el-button @click="exportFile">导出</el-button>
      </div>

      <el-table :data="tableData" style="width: 100%;" border>
        <el-table-column prop="userName" label="用户名" min-width="100">
        </el-table-column>
        <el-table-column prop="phonenumber" label="账号" min-width="100">
        </el-table-column>
        <el-table-column prop="roleName" label="角色" min-width="100">
        </el-table-column>
        <el-table-column prop="status" label="状态" min-width="100">
          <template slot-scope="scope">
            <span> {{ scope.row.status == 0 ? '正常' : '禁用' }}</span>
          </template>

        </el-table-column>
        <el-table-column prop="createTime" label="注册时间" min-width="100">
        </el-table-column>
        <el-table-column prop="name" label="操作" width="120" fixed="right">
          <template slot-scope="{ row }">
            <el-button type="text" @click="handleUpdate(row)"
                       v-hasPermi="['system:post:edit']">编辑
            </el-button>
            <el-button
              type="text"
              @click="disableMember(row)"
              v-if="row.status === '0'"
            >
              <span style="color: red;">禁用</span>
            </el-button>
            <el-button
              type="text"
              @click="disableMember(row)"
              v-if="row.status === '1'"
            >
              启用
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                  @pagination="p => getList(p.page, p.limit)"/>
    </div>

    <!-- 添加或修改岗位对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户名" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入用户名"/>
        </el-form-item>
        <el-form-item label="手机号" prop="phonenumber">
          <el-input v-model="form.phonenumber" placeholder="请输入手机号"/>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input type="password" show-password v-model="form.password" autocomplete="new-password"></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="enterPassword">
          <el-input type="password" show-password v-model="form.enterPassword" autocomplete="new-password"></el-input>
        </el-form-item>
        <el-form-item label="角色" prop="roleIds">
          <template>
            <el-select v-model="form.roleIds" multiple placeholder="请选择">
              <el-option v-for="item in options" :key="item.roleId" :label="item.roleName"
                         :value="item.roleId">
              </el-option>
            </el-select>
          </template>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import {rolelistAddport, rolelistEditport, rolelistport, userlistport} from "@/api/message/message.js"
import {changeUserStatus} from "@/api/system/user";

export default {
  name: "UsersList",
  dicts: ['sys_normal_disable'],
  data() {
    const validatePass = (rule, value, callback) => {
      if ((!this.form.userId || this.form.enterPassword) && !value) {
        callback(new Error('请输入密码'));
      } else {
        callback();
        this.$refs.form.validateField('enterPassword');
      }
    };
    const validatePass2 = (rule, value, callback) => {
      if ((!this.form.userId || this.form.password) && !value) {
        callback(new Error('请再次输入密码'));
      } else if (value !== this.form.password) {
        callback(new Error('两次输入密码不一致!'));
        if (!this.form.password) this.$refs.form.validateField('password');
      } else {
        callback();
        if (!this.form.password) this.$refs.form.clearValidate('password');
      }
    };
    return {
      typearr: [
        {value: 0, label: '正常'},
        {value: 1, label: '禁用'},
      ],
      options: [],
      rules: {
        password: [
          {required: true, validator: validatePass, trigger: 'blur'}
        ],
        enterPassword: [
          {required: true, validator: validatePass2, trigger: 'blur'}
        ],
        phonenumber: [{required: true, message: '手机号不能为空', trigger: 'blur'}
        ],
        roleIds: [
          {required: true, message: '角色不能为空', trigger: 'blur'}
        ],
        userName: [
          {required: true, message: '用户名不能为空', trigger: 'blur'}
        ]
      },
      open: false,
      title: "",
      form: {
        userId: '',
        password: '',
        enterPassword: '',
        age: '',
        userName: '',
        phonenumber: '',
        roleIds: [],
      },
      dateRange: [],

      queryParams: {
        pageNum: 1,
        pageSize: 10,
        roleName: undefined,
        roleKey: undefined,
        status: undefined, // 状态
        userName: undefined,// 用户名
        phonenumber: undefined,// 账号
        startTime: undefined, // 开始时间
        endTime: undefined, // 结束时间
      },
      tableData: [],
      total: 4,
      src:
        "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
    };
  },
  created() {
    // console.log('jkjkh')
    this.getList();
    rolelistport().then((res) => {
      this.options = res.rows
    })
  },
  mounted() {
  },

  methods: {
    disableMember(row) {
      if (row.status === '0') {
        this.$confirm("是否确认禁用该条记录?", "禁用确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            changeUserStatus(row.userId, '1').then((res) => {
              this.$message({
                type: "success",
                message: "禁用成功!",
              });
              this.getList();
            });
          })
          .catch(() => {});
      } else {
        this.$confirm("是否确认启用该记录?", "启用确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            changeUserStatus(row.userId, '0').then((res) => {
              this.$message({
                type: "success",
                message: "启用成功!",
              });
              this.getList();
            });
          })
          .catch(() => {});
      }
    },
    /** 查询角色列表 */
    getList(pageNum, pageSize) {
      if (pageNum) {
        this.queryParams.pageNum = pageNum;
      }
      if (pageSize) {
        this.queryParams.pageSize = pageSize;
      }
      this.loading = true;
      if (this.dateRange == null) {
        userlistport(this.addDateRange(this.queryParams, this.queryParams.startTime = undefined, this.queryParams.endTime = undefined)).then(response => {
          console.log(response)
          this.tableData = response.records;
          this.total = response.total;
          this.loading = false;
        }).catch((err) => {
          console.log(err)
        });
      } else {
        userlistport(this.addDateRange(this.queryParams, this.queryParams.startTime = this.dateRange[0], this.queryParams.endTime = this.dateRange[1])).then(response => {
          console.log(response)
          this.tableData = response.records;
          this.total = response.total;
          this.loading = false;
        }).catch((err) => {
          console.log(err)
        });
      }
    },

    // 查询
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 重置
    resetForm() {
      this.form = this.$options.data().form;
      this.$refs.form.resetFields();
      // console.log('form', this.form);
    },

    // 重置查询参数
    resetQuery() {
      this.queryParams = this.$options.data().queryParams;
      this.dateRange = [];
      this.handleQuery();
    },

    // 导出
    exportFile() {
      this.download("/system/user/export",
        {
          ...this.form,
        },
        `用户数据.xlsx`
      );
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.resetForm();
    },
    // 新建
    // newAddFile() { },
    /** 新增按钮操作 */
    newAddFile() {
      this.rules.password[0].required = true
      this.rules.enterPassword[0].required = true
      this.open = true;
      this.title = "添加用户";
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      console.log(row)
      this.rules.password[0].required = false
      this.rules.enterPassword[0].required = false
      this.open = true;
      const postId = row.userId
      console.log(postId)
      if (postId) {
        this.form = {
          ...this.form,
          ...row,
          password: '',
          enterPassword: ''
        };
        // this.from.roleId = row.userId
        this.open = true;
        this.title = "修改用户";
      }
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.userId) {
            console.log(this.form, 64564556)
            rolelistEditport(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.resetForm();
              this.getList();
            });
          } else {
            console.log(this.form, 111111)
            rolelistAddport(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.resetForm();
              this.getList();
            });
          }
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">

.addRight {
  float: right;
  margin: 10px 0;
}

.toClear {
  clear: both;
}

.numberStyle {
  background: #e7f8ff;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  border: 1px solid #e3f4ff;
  margin-bottom: 10px;
}
</style>
