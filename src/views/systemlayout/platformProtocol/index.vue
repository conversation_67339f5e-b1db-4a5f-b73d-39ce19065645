<template>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px" class="demo-ruleForm formbox p-24">
        <el-form-item label="平台收费明细" prop="feeDetail" class="hightedit">
            <editor
                v-model="ruleForm.feeDetail"
                ref="editor"
                :min-height="192"
            />
        </el-form-item>

        <el-form-item>
            <el-button @click="submitForm('ruleForm')">保存</el-button>
        </el-form-item>
    </el-form>
</template>

<script>
import { registrationport, agreementDetail } from "@/api/message/message.js"
export default {
    name: "platformProtocol",
    data() {
        return {
            ruleForm: {
              feeDetail: '',
            },
            rules: {
              feeDetail: [{ required: true, message: '平台收费明细不能为空', trigger: 'blur' }],
            },
        };
    },
    created() {
        this.getList();
    },
    methods: {
        getList() {
            agreementDetail().then((res) => {
                this.ruleForm = res || {}
            })
        },

        submitForm() {
            this.$refs["ruleForm"].validate((valid) => {
                if (valid) {
                    registrationport(this.ruleForm).then(response => {
                        if (response.code == 200) {
                            this.$message({
                                message: response.msg,
                                type: 'success'
                            });
                            this.getList()
                        }
                    });
                }
            });
        },
    },
};
</script>
