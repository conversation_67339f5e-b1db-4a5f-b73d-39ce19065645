<!--
 * @Description:
 * @version:
 * @Author:   操作记录
 * @Date:
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-08 16:45:18
-->
<template>
  <div class="whole">
    <el-form :model="form" ref="form" size="small" :inline="true">
      <el-form-item label="发生时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px;"
          value-format="yyyy-MM-dd"
          type="daterange"
          :clearable="false"
          @change="changeTime"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="关键词" label-width="110px">
        <el-input style="width: 180px;" v-model="form.title"></el-input>
      </el-form-item>

      <el-form-item label="账号" label-width="110px">
        <el-input style="width: 180px;" v-model="form.operName"></el-input>
      </el-form-item>
      <el-form-item label="" label-width="25px">
        <el-button type="primary" @click="getData">查询</el-button>
        <el-button @click="reset"> 重置 </el-button>
      </el-form-item>
    </el-form>
    <div>
      <div class="toClear"></div>
      <el-table :data="tableData" style="width: 100%;" border>
        <el-table-column prop="operTime" label="日期" min-width="100">
        </el-table-column>
        <el-table-column prop="operName" label="账号" min-width="100">
        </el-table-column>
        <el-table-column prop="title" label="操作内容" min-width="300">
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="form.pageNum"
        :limit.sync="form.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>
<script>
import { loglistport } from "@/api/message/message.js";
export default {
  name: "OperatingRecord",
  data() {
    return {
      form: {
        pageNum: 1,
        pageSize: 10,
        title: "",
        operName: "",
        startTime: "",
        endTime: "",
      },
      // 日期范围
      dateRange: [],
      resetForm: {},
      tableData: [],
      total: 0,
      src:
        "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
    };
  },
  created() {
    this.getData();
    this.resetForm = {
      ...this.form,
    };
  },
  methods: {
    getData() {
      const params = {
        ...this.form,
        pageNum: 1,
        pageSize: 10,
      };
      loglistport(params)
        .then((response) => {
          this.tableData = response.records;
          this.total = response.total;
        })
        .catch((err) => {
          console.log(err);
        });
    },

    getList() {
      const params = {
        ...this.form,
      };
      loglistport(params)
        .then((response) => {
          this.tableData = response.records;
          this.total = response.total;
        })
        .catch((err) => {
          console.log(err);
        });
    },

    // 改变申请日期
    changeTime(time) {
      this.form.startTime = this.$dayjs(time[0]).format("YYYY-MM-DD 00:00:00");

      this.form.endTime = this.$dayjs(time[1]).format("YYYY-MM-DD 23:59:59");
    },

    // 重置
    reset() {
      this.form = {
        ...this.resetForm,
      };
      this.dateRange = [];
      this.getData();
    },
  },
};
</script>
<style scoped lang="scss">

.addRight {
  float: right;
  margin: 10px 0;
}

.toClear {
  clear: both;
}

.numberStyle {
  background: #e7f8ff;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  border: 1px solid #e3f4ff;
  margin-bottom: 10px;
}
</style>
