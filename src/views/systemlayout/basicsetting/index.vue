<!--
 * @Description:
 * @version:
 * @Author:   基本设置
 * @Date:
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-08 19:34:26
-->
<template>
  <div class="whole">
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="demo-ruleForm"
    >
      <el-form-item label="app名称" prop="appName">
        <el-input v-model="ruleForm.appName"></el-input>
        <p style="color: #ccc; margin: 0; padding: 0;">
          用于app登录页欢迎语"欢迎来到xxx",建议4-6个汉字
        </p>
      </el-form-item>

      <el-form-item label="图标" prop="appPic">
        <ImageUpload v-model="ruleForm.appPic" :limit="1" />
        <p style="color: #ccc; margin: 0; padding: 0;">
          app登录页图标，最多可添加一张，建议尺寸32*32,16*16px大小不超过10kb
        </p>
      </el-form-item>

      <el-form-item label="首页轮播图">
        <el-table :data="ruleForm.imageList" style="width: 100%;">
          <el-table-column prop="sorted" label="排序" width="120">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.sorted" size="small" :precision="0" controls-position="right" placeholder="请输入" style="width: 100% !important;"></el-input-number>
            </template>
          </el-table-column>
          <el-table-column prop="image" label="轮播图" width="120">
            <template slot-scope="scope">
              <ImageUpload v-model="scope.row.image" :limit="1" :isShowTip="false" class="table-upload-img" />
            </template>
          </el-table-column>
          <el-table-column prop="innerPic" label="内页介绍图" width="120">
            <template slot-scope="scope">
              <ImageUpload v-model="scope.row.innerPic" :limit="1" :isShowTip="false" class="table-upload-img" />
            </template>
          </el-table-column>
          <el-table-column prop="url" label="跳转地址" min-width="150">
            <template slot-scope="scope">
              <el-input :maxlength="300" placeholder="请输入" size="small" v-model="scope.row.url"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="action" label="操作" width="110" fixed="right">
            <template slot-scope="scope">
              <a @click="handleAdd('2')">新增</a>
              <a @click="handleDelete(scope, '2')">删除</a>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

      <el-form-item label="平台客服" prop="contactPic">
        <el-input v-model="ruleForm.contactPic"></el-input>
<!--        <ImageUpload v-model="ruleForm.contactPic" :limit="1" :isShowTip="false" />-->
        <p style="color: #ccc; margin: 0; padding: 0;">
          app平台客服地址
        </p>
      </el-form-item>

      <el-form-item label="默认客服ID" prop="defaultMerchantId">
        <el-input v-model="ruleForm.defaultMerchantId"></el-input>
        <p style="color: #ccc; margin: 0; padding: 0;">
          设置默认商家客服企业ID
        </p>
      </el-form-item>

      <el-form-item label="默认客服链接" prop="defaultCustomerServiceLink">
        <el-input v-model="ruleForm.defaultCustomerServiceLink"></el-input>
        <p style="color: #ccc; margin: 0; padding: 0;">
          设置默认商家客服链接地址
        </p>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm('ruleForm')">保存</el-button>
        <!-- <el-button @click="resetForm('ruleForm')">重置</el-button> -->
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import {
  uploadfun,
  basicsport,
  systemgetDetail,
  getCarouselList
} from "@/api/message/message.js";
import { pick } from "lodash"
import {isHttpUrl} from "@/utils/tool";
export default {
  name: "MemberCenter",

  data() {
    return {
      dialogImageUrl: "",
      dialogVisible: false,
      imageUrl: "", // 存储图片预览的URL,
      ruleForm: {
        id: 0,
        appName: "",
        appPic: "",
        imageList: [{sorted: 1, time: new Date().getTime() + 1}],
        contactPic: "",
        defaultMerchantId: "",
        defaultCustomerServiceLink: ""
      },
      rules: {
        appName: [
          { required: true, message: "请输入app名称", trigger: "blur", whitespace: true },
          { min: 3, max: 6, message: "长度在 3 到 6 个字符", trigger: "blur" },
        ],
        appPic: [{ required: true, message: "请选择图标", trigger: "change" }],
      },
    };
  },
  mounted() {},
  created() {
    this.getList();
    this.getImageList();
    // console.log('5623232')
  },
  methods: {
    /** 查询系统详情 */
    async getList() {
      // this.loading = false;
      await systemgetDetail().then((res) => {
        Object.keys(pick(res, ["id", "appName", "appPic", "contactPic", "defaultMerchantId", "defaultCustomerServiceLink"])).forEach((key) => {
          this.ruleForm[key] = res[key];
        });
        // this.loading = false;
      });
    },

    // 获取轮播图列表
    getImageList() {
      getCarouselList().then((res) => {
        this.ruleForm.imageList = (res.data || []).length > 0 ? res.data.map(v => {
          if (v.url === null) {
            v.url = undefined
          }
          return v
        }) : [{sorted: 1, time: new Date().getTime()}];
      }).catch(() => {
      });
    },

    // systemgetDetail
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          console.log(this.ruleForm);
          const imageListErr = this.ruleForm.imageList.some((item, index) => {
            const { url, image } = item
            if (!image) {
              this.$message.error('请上传轮播图')
              return true
            }
            // if (url && !isHttpUrl(url)) {
            //   this.$message.error('请输入正确的跳转地址')
            //   return true
            // }
          })
          if (imageListErr) return
          const params = {
            ...this.ruleForm,
            login_image: this.ruleForm.appPic,
            appName: this.ruleForm.appName ? this.ruleForm.appName.trim() : null
          }
          basicsport(params).then((res) => {
            this.$message({
              message: "提交成功",
              type: "success",
            });
            // this.$refs[formName].resetFields();
          });
        } else {
          return false;
        }
      });
    },

    handleAdd(type) {
      const maxIndex = this.ruleForm.imageList.map(item => item.sorted || 1).sort((a, b) => a - b).pop()
      this.ruleForm.imageList.push({sorted: maxIndex + 1, type, time: new Date().getTime()})
    },

    handleDelete(scope, type) {
      // if (this.ruleForm.imageList.length === 1) {
      //   this.$message.error('至少保留一条')
      //   return
      // }

      this.ruleForm.imageList = this.ruleForm.imageList.filter((item) => item.id !== undefined ? item.id !== scope.row.id : item.time !== scope.row.time)
    },
  },
};
</script>
<style scoped lang="scss">

.addRight {
  float: right;
  margin: 10px 0;
}

.toClear {
  clear: both;
}

.numberStyle {
  background: #e7f8ff;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  border: 1px solid #e3f4ff;
  margin-bottom: 10px;
}
</style>
