<template>
  <div class="bank-config-add">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="省份" prop="province">
        <el-select
          v-model="formData.province"
          placeholder="请选择省份"
          @change="handleProvinceChange"
        >
          <el-option
            v-for="item in provinceOptions"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="市" prop="city">
        <el-select
          v-model="formData.city"
          placeholder="请选择市"
          :disabled="!formData.province"
          @change="handleCityChange"
        >
          <el-option
            v-for="item in cityOptions"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="区县" prop="address">
        <el-select
          v-model="formData.address"
          placeholder="请选择区县"
          :disabled="!formData.city"
        >
          <el-option
            v-for="item in addressOptions"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="总行名称" prop="category">
        <el-input v-model="formData.category" placeholder="请输入总行名称" />
      </el-form-item>

      <el-form-item label="开户行名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入开户行名称" />
      </el-form-item>

      <el-form-item label="联行号" prop="code">
        <el-input v-model="formData.code" placeholder="请输入联行号" />
      </el-form-item>

      <el-form-item>
        <el-button @click="handleReset">重置</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {getAddress, getProvince, getCity, saveBank} from '@/api/basis/basis.js'

export default {
  name: 'BankConfigAdd',
  data() {
    return {
      formData: {
        code: '',
        category: '',
        name: '',
        province: '',
        city: '',
        address: ''
      },
      rules: {
        code: [{ required: true, message: '请输入联行号', trigger: 'blur' }],
        category: [{ required: true, message: '请输入总行名称', trigger: 'blur' }],
        name: [{ required: true, message: '请输入开户行名称', trigger: 'blur' }],
        province: [{ required: true, message: '请选择省份', trigger: 'change' }],
        city: [{ required: true, message: '请选择区县', trigger: 'change' }],
        address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }]
      },
      provinceOptions: [],
      cityOptions: [],
      addressOptions: [],
      submitting: false
    }
  },

  created() {
    this.getProvince()
  },

  methods: {
    // 获取省份列表
    getProvince() {
      getProvince().then((res) => {
        this.provinceOptions = res.data
      })
    },

    // 获取城市列表
    getCity() {
      getCity({ province: this.formData.province }).then((res) => {
        this.cityOptions = res.data
      })
    },

    // 获取区县列表
    getAddress() {
      getAddress({
        city: this.formData.city
      }).then(res => {
        this.addressOptions = res.data
      })
    },

    // 省份改变
    handleProvinceChange(value) {
      this.formData.province = value
      this.formData.city = ''
      this.formData.address = ''
      this.cityOptions = []
      this.addressOptions = []
      if (value) this.getCity()
    },

    // 城市改变
    handleCityChange(value) {
      this.formData.city = value
      this.formData.address = ''
      this.addressOptions = []
      if (value) this.getAddress()
    },

    handleSubmit() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          this.submitting = true
          console.log('表单数据:', this.formData)

          try {
            await saveBank(this.formData)
            this.$message.success('保存成功')
          } catch (e) {

          }
          this.submitting = false
          // 保存成功后返回列表页
          // this.$router.push('/systemLayout/bankConfig/index')
        }
      })
    },

    handleReset() {
      this.$refs.formRef.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.bank-config-add {
  padding: 20px;

  .el-form {
    max-width: 600px;
  }
}
</style>
