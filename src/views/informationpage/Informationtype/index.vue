<template>
  <div class="app-container">
    <el-row :gutter="10" class="action-flex-end">
      <el-col :span="1.5">
        <el-button type="primary" @click="handleAdd">新增</el-button>
      </el-col>
    </el-row>
    <el-table v-loading="loading" :data="postList">
      <el-table-column label="显示顺序" prop="sort">
      </el-table-column>
      <el-table-column label="资讯类型" prop="type">
        <template slot-scope="scope">
          <span> {{ scope.row.type }}</span>
          <!-- <span> {{ scope.row.type == 1 ? '公告' : scope.row.type == 2 ? '通知' : '其他' }}</span> -->
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status">
        <template slot-scope="scope">
          <span> {{ scope.row.status == 1 ? '正常' : '禁用' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="设置默认" prop="isDefault">
        <template slot-scope="scope">
          <span> {{ scope.row.isDefault == 1 ? '是' : '否' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" class-name="small-padding fixed-width" width="140" fixed="right">
        <template slot-scope="scope">
          <el-button v-if="scope.row.type !== '公告'" type="text" @click="handleUpdate(scope.row)">编辑</el-button>
          <el-button v-if="scope.row.type !== '公告' && scope.row.status == 1" type="text" @click="handleDelete(scope.row)">禁用</el-button>
          <el-button v-if="scope.row.type !== '公告' && scope.row.status == 0" type="text" @click="liftABan(scope.row)">解禁</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改岗位对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form class="fromclass" ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="资讯类型" prop="type">
          <el-input v-model="form.type" placeholder="请输入资讯类型"></el-input>
        </el-form-item>
        <el-form-item class="ladyebox" label="是否默认" prop="isDefault">
          <el-select v-model="form.isDefault" placeholder="是否默认" clearable>
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>

        </el-form-item>
        <el-form-item class="ladyebox" label="排序" prop="sort">
          <el-input-number v-model="form.sort" controls-position="right" :min="0" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listPost, getPost, delPost, addPost, updatePost } from "@/api/system/post";
import { messagepage, messagelist, messageamend, informationCategory, messagedisable, messageliftABan } from "@/api/message/message.js"
export default {
  name: "Post",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        postCode: undefined,
        postName: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        type: [
          { required: true, message: "资讯类型不能为空", trigger: "blur" }
        ],
        isDefault: [
          { required: true, message: "是否默认不能为空", trigger: "blur" }
        ],
        sort: [
          { required: true, message: "顺序不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询岗位列表 */
    getList() {
      this.loading = true;
      messagepage(this.addDateRange(this.queryParams, this.dateRange)).then((res) => {
        this.postList = res.data.records;
        this.loading = false;
      })
      messagelist().then((res) => {
        // this.postList = res.data;
        let number = 0
        res.data.forEach((item) => {
          number++
          return number
        })
        this.total = number;
        this.loading = false;
      })
      this.loading = false;
    },
    blurbtn() {
      console.log('blur事件')
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        isDefault: "",
        type: undefined,
      };
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新建";
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.form = row;
      this.open = true;
      this.title = "编辑";
    },

    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        console.log(this.form)
        // this.open = false;
        if (valid) {
          if (this.form.id != undefined) {
            messageamend(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            informationCategory(
              this.form
            ).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 禁用按钮操作 */
    handleDelete(row) {
      console.log(row)
      this.$modal.confirm('是否确认禁用当前数据').then(function () {
        // row.status = 0
        return messagedisable({ id: row.id });
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("禁用成功");
        this.getList();
      }).catch(() => { });
    },

    liftABan(row) {
      console.log(row)
      this.$modal.confirm('是否确认禁用当前数据').then(function () {
        // row.status = 0
        return messageliftABan({ id: row.id });
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("解禁成功");
        this.getList();
      }).catch(() => { });
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('system/post/export', {
        ...this.queryParams
      }, `post.xlsx`)
    }
  }
};
</script>
<style lang="scss" scoped>
.ladyebox ::v-deep {
  .el-select {
    width: 100%;
  }

  // el-select
  .el-input-number {
    width: 100%;
  }
}
</style>
