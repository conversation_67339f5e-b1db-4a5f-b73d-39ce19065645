<!--
 * @Description:
 * @version:
 * @Author:   资讯发布
 * @Date:
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-08 18:49:44
-->
<template>
  <el-dialog :title="title" :visible.sync="open" v-if="open" width="900px" append-to-body :close-on-click-modal="false">
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm formbox">
      <el-form-item label="资讯标题" prop="title">
        <el-input v-model="ruleForm.title" placeholder="请输入"></el-input>
      </el-form-item>

      <el-form-item class="ladyebox" label="资讯类型" prop="channelId">
        <el-select v-model="ruleForm.channelId" placeholder="请选择" filterable>
          <el-option v-for="v in typeList" :key="v.type" :label="v.type" :value="v.id"></el-option>
        </el-select>
      </el-form-item>
      <!-- el-input  -->

      <!-- <el-form-item label="排序" prop="sort"> -->
      <el-form-item label="排序" class="ladyebox">
        <el-input-number class="textleft" v-model="ruleForm.sort" controls-position="right" :min="0"
          placeholder="请输入" />
      </el-form-item>

      <el-form-item label="项目名称" class="ladyebox">
        <el-select v-model="ruleForm.projectName" placeholder="请选择" filterable>
          <el-option v-for="v in projectList" :key="v.name" :label="v.name" :value="v.name"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="发布时间" class="ladyebox" prop="publishTime">
        <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="ruleForm.publishTime" type="datetime" placeholder="选择日期时间"></el-date-picker>
      </el-form-item>

      <el-form-item label="详情" prop="content" class="hightedit">
        <editor v-model="ruleForm.content" ref="editor" :height="250" :min-height="192" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm(0)">保存</el-button>
        <el-button type="primary" @click="submitForm(1)">保存并上架</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { pick } from "lodash";
import {projectList, releaseMessage} from "@/api/message/message.js";
import { messagelist2 } from "@/api/message/message.js";
import { inquiryinforlist } from "@/api/message/message.js";
import moment from "moment";
export default {
  name: "releaseInfor",
  data() {
    return {
      typeList: [],
      projectList: [],
      moren: undefined,
      model: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 弹框内容
      ruleForm: {
        title: "",
        channelId: "",
        content: "",
        sort: undefined,
        projectName: undefined,
        publishTime: undefined,
      },
      // 表单校验
      rules: {
        title: [
          { required: true, message: "请输入资讯标题", trigger: "blur", whitespace: true },
        ],
        channelId: [
          { required: true, message: "请选择资讯类型", trigger: "change" },
        ],
        content: [
          { required: true, message: "请输入详情", trigger: "blur" },
        ],
        publishTime: [
          { required: true, message: "请选择发布时间", trigger: "change" },
        ],
      },
    };
  },
  created() {
    this.typefun();
    this.getProjectList();
  },
  methods: {
    edit(row) {
      this.open = true;
      this.model = row
      this.$nextTick(() => {
        this.ruleForm = {
          title: "",
          channelId: this.moren,
          content: "",
          sort: undefined,
          projectName: undefined,
          publishTime: undefined,
        }
        this.$refs.ruleForm.resetFields();
        if (row.id) {
          this.title = "编辑";
          inquiryinforlist({ id: row.id }).then((res) => {
            this.ruleForm = pick(res.data, 'title', 'channelId', 'sort', 'content', 'projectName')
            this.$set(this.ruleForm, 'publishTime', moment(res.data.publishTime).format('YYYY-MM-DD HH:mm:ss'))
          });
        } else {
          this.title = "新增";
        }
      })
    },
    // 获取数据类型
    typefun() {
      messagelist2().then((res) => {
        this.typeList = res.data;
        // console.log(this.typeList);
        const moren = res.data.filter(item => item.isDefault == 1)
        if (moren && moren.length > 0) {
          this.moren = moren[0].id
        }
      });
    },
    async getProjectList() {
      const res = await projectList()
      this.projectList = res.data.filter(v => v.type === '1')
    },
    submitForm(status) {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          const params = {
            ...this.ruleForm,
            status,
            publishTime: moment(this.ruleForm.publishTime).format('YYYY-MM-DD HH:mm:ss')
          };
          if (this.model.id) {
            params.id = this.model.id;
          }
          releaseMessage(params).then((res) => {
            if (res.code == 200) {
              this.open = false;
              this.$message.success("保存成功");
              this.$emit("getData");
            }
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
// textleft

.textleft ::v-deep {

  // text-align: left;
  .el-input--medium {
    .el-input__inner {
      text-align: left;
    }
  }
}

.formbox ::v-deep {
  .hightedit {
    height: 300px;
  }
}

.ladyebox ::v-deep {
  .el-select {
    width: 100%;
  }

  .el-input-number {
    width: 100%;
  }
}

.fromclass ::v-deep {
  .el-form-item__content {
    display: flex;
  }
}
</style>
