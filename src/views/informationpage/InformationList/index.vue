<template>
  <div class="app-container">
    <el-row
      :gutter="10"
      class="action-flex-end"
    >
      <el-col :span="1.5">
        <el-button
          type="primary"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="显示顺序" prop="sort">
      </el-table-column>
      <el-table-column label="标题" prop="title" />
      <el-table-column label="类型" prop="channelType" />
      <el-table-column label="状态">
        <template slot-scope="scope">
          <span> {{ scope.row.enable == 1 ? "已上架" : "已下架" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="发布时间"
        prop="publishTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.publishTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="上架时间"
        prop="submitedTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.submitedTime) || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        class-name="small-padding fixed-width"
        width="180"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="handleUpdate(scope.row)"
            >编辑</el-button
          >
          <el-button
            v-show="scope.row.enable == 0"
            type="text"
            @click="putawaybtn(scope.row)"
            >上架</el-button
          >
          <el-button
            v-show="scope.row.enable == 1"
            type="text"
            @click="undercarriagebtn(scope.row)"
            >下架</el-button
          >
          <el-button type="text" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <releaseInfor
      ref="releaseInfor"
      @getData="getList"
    ></releaseInfor>
  </div>
</template>

<script>
import {
  inforlistpage,
  alterinforlist,
  deleinforlist,
} from "@/api/message/message.js";
import releaseInfor from "./releaseInfor.vue";

export default {
  name: "Post",
  dicts: ["sys_normal_disable"],
  components: {
    releaseInfor,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        postCode: undefined,
        postName: undefined,
        status: undefined,
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询岗位列表 */
    getList() {
      this.loading = false;
      inforlistpage(this.addDateRange(this.queryParams, this.dateRange)).then(
        (response) => {
          console.log(response.data.records);
          if (response.data.records.length == 0) {
            console.log(this.queryParams.pageNum);
            if (this.queryParams.pageNum != 1) {
              this.queryParams.pageNum = this.queryParams.pageNum - 1;
              inforlistpage(
                this.addDateRange(this.queryParams, this.dateRange)
              ).then((response) => {
                this.postList = response.data.records;
                this.total = response.data.total;
                this.open = false;
                this.loading = false;
              });
            }
          } else {
            this.postList = response.data.records;
            this.total = response.data.total;
            this.open = false;
            this.loading = false;
          }
        }
      );
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.releaseInfor.edit({})
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.$refs.releaseInfor.edit(row)
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      // console.log(row.id)
      const postIds = row.id;
      this.$modal
        .confirm("是否确认删除数据")
        .then(function () {
          return deleinforlist({ id: postIds });
          // .then((res) => {
          //     console.log(res)
          //     console.log('我删除了')
          // })
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },

    // 上架
    putawaybtn(row) {
      let temp = { ...row };
      temp.enable = 1;
      this.$modal
        .confirm("是否确认上架")
        .then(function () {
          return alterinforlist(temp);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("上架成功");
        })
        .catch(() => {});
    },
    // 下架
    undercarriagebtn(row) {
      let temp = { ...row };
      temp.enable = 0;
      this.$modal
        .confirm("是否确认下架")
        .then(function () {
          return alterinforlist(temp);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("下架成功");
        })
        .catch(() => {});
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post.xlsx`
      );
    },
  },
};
</script>
