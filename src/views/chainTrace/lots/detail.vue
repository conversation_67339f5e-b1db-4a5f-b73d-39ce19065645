<template>
  <el-dialog
    title="溯源"
    :visible.sync="dialogVisible"
    width="720"
    :close-on-click-modal="false"
    :before-close="cleanForm"
    :modal-append-to-body="false"
    :modal="false"
    destroy-on-close
  >
    <div>
      <div class="contentLabel">基本信息</div>
      <el-row>
        <el-col :span="12">凭证代码：{{ info.lotCode }}</el-col>
      </el-row>
      <el-row style="margin-bottom: 24px">
        <el-col :span="12">凭证名称：{{ info.lotName }}</el-col>
      </el-row>
      <div class="contentLabel">溯源信息</div>
      <el-table :data="tableData" style="width: 100%;" border max-height="680">
        <el-table-column prop="tradeTime" label="发生时间">
          <template slot-scope="scope">
            <div>{{ scope.row.tradeTime || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="fromUser" label="出让方">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image v-if="scope.row.fromHeadPortrait" :src="scope.row.fromHeadPortrait" fit="fill"></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div v-if="!scope.row.fromNickName && !scope.row.fromAccountNumber && !scope.row.fromUsername">
                -
              </div>
              <div v-else class="span_right">
                <div>{{ scope.row.fromNickName }}</div>
                <div>{{ scope.row.fromAccountNumber }} 【{{ scope.row.fromUsername }}】</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="toUser" label="接收方">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image v-if="scope.row.toHeadPortrait" :src="scope.row.toHeadPortrait" fit="fill"></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div v-if="!scope.row.toNickName && !scope.row.toAccountNumber && !scope.row.toUsername">
                -
              </div>
              <div v-else class="span_right">
                <div>{{ scope.row.toNickName }}</div>
                <div>{{ scope.row.toAccountNumber }} 【{{ scope.row.toUsername }}】</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="tokenHash" label="哈希值"></el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="current"
        :limit.sync="pageSize"
        @pagination="p => getList(p.page, p.limit)"
      />
    </div>
  </el-dialog>

</template>

<script>
import {lotLogPage} from "@/api/chainTrace.js";

export default {
  name: "ChainTraceLotsDeatil",
  data() {
    return {
      dialogVisible: false,
      current: 1,
      pageSize: 10,
      total: 0,
      tableData: [],
      info: {},
    };
  },
  watch: {
    info() {
      if (this.info.lotId) {
        this.getList()
      }
    }
  },
  methods: {
    cleanForm() {
      this.dialogVisible = false;
    },
    getList(pageNum, pageSize) {
      if (pageNum) {
        this.current = pageNum;
      }
      if (pageSize) {
        this.pageSize = pageSize;
      }
      const params = {
        token: this.info.token,
        lotId: this.info.lotId,
        current: this.current,
        pageSize: this.pageSize,
      };
      lotLogPage(params).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
      });
    },
  },
}
</script>

<style scoped lang="less">
.contentLabel {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
}
</style>
