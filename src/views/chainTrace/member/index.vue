<template>
  <div class="goods-list p-24">
    <div class="query-box">
      <el-form :model="form" label-suffix="：" ref="form" size="small" :inline="true">
        <el-form-item label="会员">
          <el-input style="width: 240px" v-model="form.keyword" placeholder="请输入会员id/昵称/手机号"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="form.approveStatus"
            placeholder="请选择"
            size="small"
          >
            <el-option label="待审核" :value="1"></el-option>
            <el-option label="审核成功" :value="2"></el-option>
            <el-option label="审核拒绝" :value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="reset">重置</el-button>
          <el-button type="primary" @click="getList(1)">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-card class="list-card-default mt-16" shadow="never">
      <div slot="header" class="flex a-center justify-between">
        <span class="fs-16 text-default">列表</span>
      </div>
      <el-table :data="tableData" style="width: 100%;">
        <!--        <el-table-column prop="lotId" label="会员" min-width="100"></el-table-column>-->
        <el-table-column prop="idCard" label="会员" min-width="200">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image
                  v-if="scope.row.headPortrait"
                  :src="scope.row.headPortrait"
                  fit="fill"
                ></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div class="span_right">
                <div>{{ scope.row.nickName }}</div>
                <div>{{ scope.row.accountNumber }} 【{{ scope.row.userName }}】</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="approveStatus" label="状态">
          <template slot-scope="scope">
            <span v-if="scope.row.approveStatus === 1">待审核</span>
            <span v-if="scope.row.approveStatus === 2">审核成功</span>
            <span v-if="scope.row.approveStatus === 3">审核拒绝</span>
          </template>
        </el-table-column>
        <el-table-column prop="approveTime" label="上链时间"></el-table-column>
        <el-table-column prop="encAddress" label="链上地址"></el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="current"
        :limit.sync="pageSize"
        @pagination="p => getList(p.page, p.limit)"
      />
    </el-card>
    <Detail ref="detail"></Detail>
  </div>
</template>
<script>
import {queryPage} from "@/api/chainTrace.js";
import {standardAmount} from "@/utils/index.js";

export default {
  name: "ChainTraceMember",
  data() {
    return {
      form: {
        keyword: "",
        approveStatus: '',
      },
      current: 1,
      pageSize: 10,
      total: 0,
      resetForm: {},
      tableData: [],
      detailTableData: []
    };
  },
  mounted() {
    this.reset()
  },
  methods: {
    standardAmount,
    // 分页接口
    getList(pageNum, pageSize) {
      if (pageNum) {
        this.current = pageNum;
      }
      if (pageSize) {
        this.pageSize = pageSize;
      }
      const params = {
        ...this.form,
        current: this.current,
        pageSize: this.pageSize,
      };
      queryPage(params).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
      });
    },
    // 重置
    reset() {
      this.form = {
        keyword: "",
        approveStatus: '',
      };
      this.getList(1);
    },
  },
};
</script>
<style lang="scss" scoped>
.goods-list {
  min-height: calc(100vh - 84px);
  background-color: #F0F2F5;

  .query-box {
    background: #FFFFFF;
    border-radius: 2px;
    padding: 24px 24px 0 24px;

    .el-button {
      height: 32px;
      line-height: 32px;
      font-size: 14px;
    }

    .el-form-item__label {
      width: auto !important;
      padding-bottom: 0;
      line-height: 32px;
    }

    .el-form-item {
      margin-right: 24px;
      margin-bottom: 24px;
    }

    .el-form-item__content {
      width: 306px;
    }

    .el-button {
      width: 65px;
    }
  }
}
</style>
