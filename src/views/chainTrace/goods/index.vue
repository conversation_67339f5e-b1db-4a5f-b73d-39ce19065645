<template>
  <div class="goods-list p-24">
    <div class="query-box">
      <el-form :model="form" label-suffix="：" ref="form" size="small" :inline="true">
<!--        <el-form-item label="商品代码">-->
<!--          <el-input style="width: 240px" v-model="form.lotId" placeholder="请输入"></el-input>-->
<!--        </el-form-item>-->
        <el-form-item label="商品标题">
          <el-input style="width: 240px" v-model="form.lotName" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="上链时间">
          <el-date-picker
            style="width: 100%;"
            v-model="dateRange"
            type="daterange"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="changeTime"
          >
          </el-date-picker>
        </el-form-item>
<!--        <el-form-item label="状态">-->
<!--          <el-select-->
<!--            v-model="form.status"-->
<!--            placeholder="请选择"-->
<!--            size="small"-->
<!--          >-->
<!--            <el-option label="正常交易 " :value="1"></el-option>-->
<!--            <el-option label="已提货" :value="2"></el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
        <el-form-item>
          <el-button @click="reset">重置</el-button>
          <el-button type="primary" @click="getList(1)">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-card class="list-card-default mt-16" shadow="never">
      <div slot="header" class="flex a-center justify-between">
        <span class="fs-16 text-default">列表</span>
      </div>
      <el-table :data="tableData" style="width: 100%;">
<!--        <el-table-column prop="lotId" label="商品编号" min-width="90"></el-table-column>-->
        <el-table-column prop="date" label="商品" min-width="110">
          <template slot-scope="scope">
            <div class="span_all">
              <div class="span_left">
                <el-image
                  v-if="scope.row.lotImg"
                  :src="scope.row.lotImg"
                  fit="fill"
                ></el-image>
                <img
                  v-else
                  src="@/assets/images/member.png"
                  alt="donate"
                  width="100%"
                />
              </div>
              <div style="line-height: 48px">{{ scope.row.lotName || '-' }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="token" label="链上地址" min-width="180"></el-table-column>
        <el-table-column prop="tradeTime" label="交易时间" min-width="120"></el-table-column>
        <el-table-column prop="status" label="状态" min-width="60">
          <template slot-scope="scope">
            <span v-if="scope.row.status === '1'">正常交易</span>
            <span v-if="scope.row.status === '2'">已提货</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="{ row }">
            <a @click="goToDetail(row)">溯源</a>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="current"
        :limit.sync="pageSize"
        @pagination="p => getList(p.page, p.limit)"
      />
    </el-card>
    <Detail ref="detail"></Detail>
  </div>
</template>
<script>
import {queryAll} from "@/api/chainTrace.js";
import {standardAmount} from "@/utils/index.js";
import Detail from "./detail.vue";

export default {
  name: "ChainTraceGoods",
  components: {
    Detail
  },
  data() {
    return {
      form: {
        type: 1,
        lotId: "",
        lotName: "",
        status: '',
        publishEndTime: '',
        publishStartTime: ''
      },
      current: 1,
      pageSize: 10,
      total: 0,
      dateRange: [],
      resetForm: {},
      tableData: [],
      detailTableData: []
    };
  },
  mounted() {
    this.reset()
  },
  methods: {
    standardAmount,
    // 分页接口
    getList(pageNum, pageSize) {
      if (pageNum) {
        this.current = pageNum;
      }
      if (pageSize) {
        this.pageSize = pageSize;
      }
      const params = {
        ...this.form,
        current: this.current,
        pageSize: this.pageSize,
        type: 1 // 类型：0-凭证，1-商品
      };
      queryAll(params).then((res) => {
        this.tableData = res.records;
        this.total = res.total;
      });
    },
    // 重置
    reset() {
      this.form = {
        type: 1,
        lotId: "",
        lotName: "",
        status: '',
        publishEndTime: '',
        publishStartTime: ''
      };
      this.dateRange = []
      this.getList(1);
    },
    // 改变申请日期
    changeTime() {
      if(this.dateRange){
        this.form.publishStartTime = this.$dayjs(this.dateRange[0]).format(
          "YYYY-MM-DD 00:00:00"
        );
        this.form.publishEndTime = this.$dayjs(this.dateRange[1]).format(
          "YYYY-MM-DD 23:59:59"
        );
      }else {
        this.form.publishStartTime = '';
        this.form.publishEndTime = '';
      }

    },
    goToDetail(row) {
      this.$refs.detail.info = row;
      this.$refs.detail.dialogVisible = true
    }
  },
};
</script>
<style lang="scss" scoped>
.goods-list {
  min-height: calc(100vh - 84px);
  background-color: #F0F2F5;

  .query-box {
    background: #FFFFFF;
    border-radius: 2px;
    padding: 24px 24px 0 24px;

    .el-button {
      height: 32px;
      line-height: 32px;
      font-size: 14px;
    }

    .el-form-item__label {
      width: auto !important;
      padding-bottom: 0;
      line-height: 32px;
    }

    .el-form-item {
      margin-right: 24px;
      margin-bottom: 24px;
    }

    .el-form-item__content {
      width: 306px;
    }

    .el-button {
      width: 65px;
    }
  }
}
</style>
