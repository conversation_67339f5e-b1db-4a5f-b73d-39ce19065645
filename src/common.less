.text-gray-2 {
  color: rgba(51, 66, 92, 0.85);
}
.mb-8 {
  margin-bottom: 8px;
}

.w-full {
  width: 100%;
}

.drawer-form-default {

  .ant-radio,
  .ant-radio-wrapper,
  .ant-checkbox-wrapper {
    .text-gray-2
  }

  .ant-form {

    .ant-form-explain {
      margin-top: 2px;
    }

    input:not(textarea):not(.ant-calendar-range-picker-input),
      // .ant-select-selection__rendered,
    .ant-select-selection:not(.ant-select-selection--multiple),
    .ant-select-selection--single,
    .ant-input:not(textarea):not(.ant-calendar-picker-input),
    .ant-input-number,
    .ant-input-number-input,
      // .ant-select-selection-selected-value,
    .ant-calendar-picker,
    .ant-time-picker,
    .ant-time-picker-input {
      width: 100%;
      height: 32px;
      line-height: 32px;
      border-radius: 2px;
    }

    .ant-select-search__field {
      height: 100% !important;
    }

    .ant-radio-wrapper {
      height: 22px;
      line-height: 22px;
      margin-right: 16px;
    }

    textarea {
      width: 100%;
      margin-bottom: 0;
    }

    .ant-form-item-label {
      margin-bottom: 8px;
      padding: 0;
      line-height: 22px;
    }

    .ant-form-item {
      margin-bottom: 16px;
      padding-bottom: 0;
      .text-gray-2;

      &.mb-8 {
        margin-bottom: 8px;
      }

      &.mb-0 {
        margin-bottom: 0;
      }
    }

    .ant-checkbox+span {
      padding: 0 8px;
    }

    .ant-table {
      .ant-form-item {
        margin-bottom: 0;
      }
    }

    .ant-radio-wrapper,
    .ant-checkbox-wrapper {
      .mb-8
    }

    .ant-radio-group,
    .ant-checkbox-group {
      margin-bottom: -8px;
      color: #33425C;
    }

    .ant-checkbox-group-item {
      line-height: 22px;
      margin-right: 16px;
    }

    .ant-table {
      .ant-form-item {
        margin-bottom: 0;
      }

      .ant-radio-wrapper,
      .ant-checkbox-wrapper {
        margin-bottom: 0;
      }

      .ant-radio-group,
      .ant-checkbox-group {
        margin-bottom: 0;
      }
    }
  }

  .label-required {
    .ant-form-item-label label::before {
      display: inline-block;
      margin-right: 4px;
      color: #f5222d;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: '*';
    }
  }

  .search-form-wrap {

    //margin: -24px;
    .ant-row-flex>.ant-col:last-child .ant-form-item {
      margin-bottom: 0;
    }

    &.ant-form-inline {
      .ant-form-item-label {
        margin-bottom: 0;
        padding-right: 8px;
        line-height: 32px;
      }

      .ant-form-item {
        margin-bottom: 0;
      }
    }

    &.ant-form-horizontal {
      .ant-form-item {
        margin-bottom: 0;
      }
    }

    &.ant-form-vertical {
      .ant-radio-wrapper {
        height: initial;
        line-height: initial;
      }
    }

    //.ant-form-inline .ant-form-item > .ant-form-item-label {
    //  flex: 1;
    //  text-align: left;
    //}
  }
}
.common-app-drawer,
.drawer-default {
  &:has(.common_drawer_form) .ant-drawer-body {
    padding: 24px 0;
    .common_drawer_form {
      padding: 0 24px;
    }
  }

  .ant-drawer-content-wrapper {
    //top: 60px;
  }

  &.drawer-submit {
    .ant-drawer-body {
      max-height: calc(100% - 170px);
      overflow-y: auto;
    }
  }

  .drawer-bootom-button,
  .drawer-bottom-button {
    position: absolute;
    z-index: 2;
    //bottom: 60px;
    bottom: 0;
    width: 100%;
    padding: 12px 24px;
    text-align: right;
    left: 0;
    background: #ffffff;
    border-top: 1px solid #EBEEF5;
  }

  .control-item-title {
    height: 24px;
    font-size: 16px;
    font-weight: 500;
    color: #33425C;
    line-height: 24px;
    margin: 24px 0 16px 0;
  }

  .control-item-title:first-child {
    margin-top: 0;
  }

  .control-item-title-sub {
    font-size: 14px;
    font-weight: 400;
    color: #33425C;
    line-height: 22px;
    margin: 8px 0;
  }

  .drawer-form-default;

  .search-form-wrap {
    .ant-card-body {
      padding: 0;
    }
  }

  .ant-space-horizontal {
    .ant-form-item {
      margin-bottom: 0;
    }
  }

  .mul-img-wrap {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr)); // 防止放大
    gap: 10px 10px;
  }
}

.drawer-bootom-button {
    position: absolute;
    bottom: 60px;
    width: 100%;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: right;
    left: 0;
    background: #fff;
    border-radius: 0 0 2px 2px;
}

.title-statistics {
  .statistics-style {
    font-size: 16px;
    color: #33425C;
    line-height: 24px;
    font-weight: 400;
  }
}

// 自定义弹出框样式
.custom-popover {
  max-height: 500px;
  overflow-y: auto;
}
