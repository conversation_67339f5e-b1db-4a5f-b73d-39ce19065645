<template>
    <div id="app">
      <a-locale-provider  :locale="locale">
        <router-view />
        <theme-picker />
      </a-locale-provider>
    </div>
</template>

<script>
import ThemePicker from "@/components/ThemePicker";
import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN';

export default {
  name: "App",
  components: { ThemePicker },
  data() {
    return {
      locale: zhCN,
    };
  },
  metaInfo() {
    return {
      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
      titleTemplate: title => {
        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
      }
    }
  }
};
</script>
<style scoped>
#app .theme-picker {
  display: none;
}
</style>
