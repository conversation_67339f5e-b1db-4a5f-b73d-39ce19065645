/*
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-03-27 09:25:40
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-17 08:42:46
 */
import request from '@/utils/request'

// 分页列表
export function queryAll(params) {
    return request({
      url: '/eazyMermberCenter/cancelList',
      method: 'get',
      params: params
    })
  }

// 审核
export function cancelApprove(data) {
  return request({
    url: '/eazyMermberCenter/cancelApprove',
    method: 'post',
    data: data
  })
}
