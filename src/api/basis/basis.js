/*
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-03-27 09:25:40
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-17 08:42:46
 */
import request from '@/utils/request'

// 会员中心新增
export function add(data) {
    return request({
      url: '/eazyMermberCenter/add',
      method: 'post',
      data: data
    })
  }

  // 编辑会员中心
export function edit(data) {
    return request({
      url: '/eazyMermberCenter/edit',
      method: 'post',
      data: data
    })
  }

// 编辑第三方权益
export function editThirdParty(data) {
  return request({
    url: '/eazyMermberCenter/updateThirdParty',
    method: 'post',
    data: data
  })
}

// 会员中心 分页列表
export function queryAll(params) {
    return request({
      url: '/eazyMermberCenter/queryAll/',
      method: 'get',
      params: params
    })
  }

// 会员中心 所属机构列表
export function queryReferrer(params) {
    return request({
      url: '/eazyMermberCenter/getReferrerMember',
      method: 'get',
      params: params
    })
  }

// 会员中心不分页(凭证管理使用)
export function queryList(params) {
    return request({
      url: 'eazyMermberCenter/user-list',
      method: 'get',
      params: params
    }).then(res => {
      res.data.forEach(v => v.userName = v.userName || v.nickName)
      return res
    })
  }

// 家统计委托收购统计分页接口
export function getTotalEntrustPage(params) {
  return request({
    url: '/statistics-upbeat-center/total-entrust-page',
    method: 'post',
    data: params
  })
}
// /statistics-upbeat-center/total-pre-page
// 商家统计预约销售统计分页接口
export function getTotalPrePage(params) {
  return request({
    url: '/statistics-upbeat-center/total-pre-page',
    method: 'post',
    data: params
  })
}

// 商家统计自主销售统计分页接口
export function getTotalSelfPage(params) {
  return request({
    url: '/statistics-upbeat-center/total-self-page',
    method: 'post',
    data: params
  })
}

// 服务中心统计预约销售统计分页接口
export function getServicePrePage(params) {
  return request({
    url: '/statistics-service-center/total-pre-page',
    method: 'post',
    data: params
  })
}

// 服务中心统计预约销售统计接口
export function getServicePreTotal(params) {
  return request({
    url: '/statistics-service-center/total-pre-total',
    method: 'post',
    data: params
  })
}

// 自主销售统计预约销售统计接口
export function getServiceSelfTotal(params) {
  return request({
    url: '/statistics-service-center/total-self-total',
    method: 'post',
    data: params
  })
}

//委托收购统计预约销售统计接口
export function getServiceEntrustTotal(params) {
  return request({
    url: '/statistics-service-center/total-entrust-total',
    method: 'post',
    data: params
  })
}


// 服务中心统计自主销售统计分页接口
export function getServiceSelfPage(params) {
  return request({
    url: '/statistics-service-center/total-self-page',
    method: 'post',
    data: params
  })
}

// 服务中心统计委托收购统计分页接口
export function getServiceEntrustPage(params) {
  return request({
    url: '/statistics-service-center/total-entrust-page',
    method: 'post',
    data: params
  })
}
  // 角色新增
export function addRole(data) {
  return request({
    url: '/eazyRole/add',
    method: 'post',
    data: data
  })
}

  // 角色编辑
  export function editRole(data) {
    return request({
      url: '/eazyRole/edit',
      method: 'post',
      data: data
    })
  }

  // 角色分页列表
export function queryAllRole(params) {
  return request({
    url: '/eazyRole/queryAll',
    method: 'get',
    params: params
  })
}

  // 提货信息列表
  export function queryAllDelivery(params) {
    return request({
      url: '/eazyDeliveryInformation/queryAll',
      method: 'get',
      params: params
    })
  }

    // 提货总数
    export function queryTotal(params) {
      return request({
        url: '/eazyDeliveryInformation/queryTotal',
        method: 'get',
        params: params
      })
    }

  // 提货申请编辑
  export function editDelivery(data) {
    return request({
      url: '/eazyDeliveryInformation/edit',
      method: 'post',
      data: data
    })
  }

    // 角色
    export function getRole(params) {
      return request({
        url: '/eazyRole/getRole',
        method: 'get',
        params: params
      })
    }

   // 禁用 启用
  export function updateStatus(data) {
    return request({
      url: '/eazyMermberCenter/updateStatus',
      method: 'post',
      data: data
    })
  }

  // 编辑会员中心
  export function getUserName(params) {
    return request({
      url: '/eazyMermberCenter/getByMemberId',
      method: 'get',
      params: params
    })
  }

  // 通过id 查询姓名
  export function getByMemberId(params) {
    return request({
      url: '/front/user/getByMemberId',
      method: 'get',
      params: params
    })
  }


  // 实名认证
  export function userRealname(data) {
    return request({
      url: '/eazyMermberCenter/userRealname',
      method: 'post',
      data: data
    })
  }

  // 企业认证
  export function comRealName(data) {
    return request({
      url: '/eazyMermberCenter/comRealName',
      method: 'post',
      data: data
    })
  }


   // 会员中心 分页列表
   export function getServiceUsers(data) {
    return request({
      url: '/eazyMermberCenter/getServiceUsers',
      method: 'post',
      data: data
    }).then(res => {
      res.data.records.forEach(v => v.userName = v.userName || v.nickName)
      return res
    })
  }


  //省市区级联数据
  export function getEazyCityList(params) {
  return request({
      url: '/eazyCity/list',
      method: 'get',
      params: params
    })
  }

  // 获取总行数据
  export function getCategory(params) {
    return request({
      url: '/eazy-bank-number/category',
      method: 'get',
      params: params
    })
  }

  // 获取省份数据
  export function getProvince(params) {
    return request({
      url: '/eazy-bank-number/province',
      method: 'get',
      params: params
    })
  }

  // 获取市数据
  export function getCity(params) {
    return request({
      url: '/eazy-bank-number/city',
      method: 'get',
      params: params
    })
  }

  // 获取所在地
  export function getAddress(params) {
    return request({
      url: '/eazy-bank-number/address',
      method: 'get',
      params: params
    })
  }

  // 保存银行
  export function saveBank(data) {
    return request({
      url: '/eazy-bank-number/save',
      method: 'post',
      data: data
    })
  }

  export function getBankNumber(params) {
    return request({
      url: '/eazy-bank-number/name',
      method: 'get',
      params: params
    })
  }

  export function approveAdd(data) {
    return request({
      url: '/eazyMemberApprove/add',
      method: 'post',
      data: data
    })
  }

export function approveUpdate(data) {
  return request({
    url: '/eazyMemberApprove/update',
    method: 'post',
    data: data
  })
}

export function approveDetail(params) {
  return request({
    url: '/eazyMemberApprove/detail',
    method: 'get',
    params: params
  })
}

//身份证识别
export function identifyIdCard(params) {
  return request({
    url: '/eazyMermberCenter/showIdCardInfo',
    method: 'get',
    params: params
  })
}

//身份证识别
export function identifyBusinessLicense(params) {
  return request({
    url: '/eazyMermberCenter/showBusinessLicenseInfo',
    method: 'get',
    params: params
  })
}

// 获取二维码
export function getQrCodeImg(params) {
  return request({
    url: '/wx/getQrcode',
    method: 'get',
    params: params
  })
}


// 平台数据统计 /statistics-platform-center/total-platform-total
export function getTotalPlatformTotal(params) {
  return request({
    url: '/statistics-platform-center/total-platform-total',
    method: 'post',
    data: params
  })
}

// 平台交易趋势 /statistics-platform-center/total-platform-trend
export function getTotalPlatformTrend(params) {
  return request({
    url: '/statistics-platform-center/total-platform-trend',
    method: 'post',
    data: params
  })
}

// /statistics-platform-center/total-platform-page
export function getTotalPlatformPage(params) {
  return request({
    url: '/statistics-platform-center/total-platform-page',
    method: 'post',
    data: params
  })
}

// /statistics-upbeat-center/total-upbeat-deal 交易市场数据
export function getTotalUpbeatDeal(params) {
  return request({
    url: '/statistics-upbeat-center/total-upbeat-deal',
    method: 'get',
    params: params
  })
}

// /statistics-upbeat-center/total-sale-page 服务中心销售统计分页接口
export function getTotalSalePage(params) {
  return request({
    url: '/statistics-upbeat-center/total-sale-page',
    method: 'post',
    data: params
  })
}

// /statistics-upbeat-center/total-upbeat-total 商家数据统计
export function getTotalUpbeatTotal(params) {
  return request({
    url: '/statistics-upbeat-center/total-upbeat-total',
    method: 'post',
    data: params
  })
}

// /statistics-service-center/total-service-total 服务中心数据统计
export function getTotalServiceTotal(params) {
  return request({
    url: '/statistics-service-center/total-service-total',
    method: 'post',
    data: params
  })
}

// /statistics-service-center/total-sort-page 服务中心销售统计分页接口
export function getTotalSortPage(params) {
  return request({
    url: '/statistics-service-center/total-sort-page',
    method: 'post',
    data: params
  })
}

// /statistics-upbeat-center/total-upbeat-withdrawal 商家可提现总额
export function getTotalUpbeatWithdrawal(params) {
  return request({
    url: '/statistics-upbeat-center/total-upbeat-withdrawal',
    method: 'post',
    data: params
  })
}

//商城订单分页接口
export function getStatisticsGoodsPage(params) {
  return request({
    url: '/statistics-upbeat-center/goods-page',
    method: 'post',
    data: params
  })
}

//商城订单轮次
export function getStatisticsGoodsTimes(params) {
  return request({
    url: '/statistics-upbeat-center/goods-times',
    method: 'get',
    params: params
  })
}

//清除认证信息
export function clearCertification(params) {
  return request({
    url: '/eazyMermberCenter/clearCertification',
    method: 'post',
    data: params
  })
}
