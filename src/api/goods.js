import request from '@/utils/request'

// 发布商品（新增）
export function add(data) {
  return request({
    url: '/goods/publish/save',
    method: 'post',
    data: data
  })
}

// 编辑会员中心
export function edit(data) {
  return request({
    url: '/goods/publish/update',
    method: 'post',
    data: data
  })
}

// 分页
export function queryAll(data) {
  return request({
    url: '/goods/publish/page',
    method: 'post',
    data: data
  })
}

// 修改顺序
export function updateSeq(params) {
  return request({
    url: '/goods/publish/update/seq',
    method: 'post',
    params: params
  })
}

// 修改注水销量
export function updateWaterSales(params) {
  return request({
    url: '/goods/publish/update/waterSales',
    method: 'post',
    params: params
  })
}

// 商品详情
export function goodsDetail(params) {
  return request({
    url: `/goods/publish/detail/${params.goodsId}`,
    method: 'get',
  })
}

// 修改上下架状态
export function updateStatus(params) {
  return request({
    url: `/goods/publish/update/status/${params.goodsId}`,
    method: 'get',
  })
}

// 请购分页
export function applyList(data) {
  return request({
    url: '/goods/apply/page',
    method: 'post',
    data
  })
}
// 修改状态和备注
export function applyEdit(data) {
  return request({
    url: '/goods/apply/update',
    method: 'post',
    data
  })
}

// 状态枚举
export function statusList() {
  return request({
    url: '/goods/apply/status',
    method: 'get',
  })
}
