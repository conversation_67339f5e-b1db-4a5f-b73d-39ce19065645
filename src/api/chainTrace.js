import request from '@/utils/request'

// 分页
export function queryAll(data) {
  return request({
    url: '/lotPurple/lot-page',
    method: 'post',
    data: data
  })
}

// 溯源
export function lotLogPage(data) {
  return request({
    url: '/lotPurple/lot-log-page',
    method: 'post',
    data: data
  })
}

// 会员
export function queryPage(data) {
  return request({
    url: '/lotPurple/member-page',
    method: 'post',
    data: data
  })
}
