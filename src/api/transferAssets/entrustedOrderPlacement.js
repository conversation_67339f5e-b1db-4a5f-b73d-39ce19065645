/*
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-04-16 13:14:56
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-04-16 13:20:50
 */
import request from '@/utils/request'

// 委托挂单
export function entrustPageList(data) {
    return request({
      url: '/lot/entrust-page-list',
      method: 'post',
      data: data
    })
  }

  // 一键采购
export function oneTouchPageList(data) {
    return request({
      url: '/entrust/purchase/page',
      method: 'post',
      data: data
    })
  }

  // 删除委托挂单
export function deleteEntrust(params) {
    return request({
        url: '/lot/delete-entrust/' + params,
        method: 'get',
    })
  }
