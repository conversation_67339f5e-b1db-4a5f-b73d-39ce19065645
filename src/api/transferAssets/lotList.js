/*
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-04-09 16:06:42
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-04-11 09:53:13
 */
import request from '@/utils/request'

// 新增凭证
export function save(data) {
    return request({
      url: '/lot/save',
      method: 'post',
      data: data
    })
  }

  // 编辑凭证
export function update(data) {
    return request({
      url: '/lot/update',
      method: 'post',
      data: data
    })
  }
// 更新凭证时间
export function updateTradeTime(data){
  return request({
    url: '/lot/updateTradeTime',
    method: 'post',
    data: data
  })
}

// 平台账户可用余额
export function getPlatformDetail() {
  return request({
    url: '/platformAccount/getPlatformDetail',
    method: 'get',
  })
}

// 平台账户提现 /platformAccount/topUp
export function accountTopUp(data) {
  return request({
    url: '/platformAccount/topUp',
    method: 'post',
    data: data
  })
}

 // 凭证分页
  export function list(data) {
    return request({
      url: '/lot/list',
      method: 'post',
      data: data
    })
  }

// 凭证详情
export function info(params) {
  return request({
      url: '/lot/info/' + params,
      method: 'get',
  })
}

 // 凭证修改状态
 export function updateState(data) {
  return request({
    url: '/lot/update-state',
    method: 'post',
    data: data
  })
}

// 涨价
export function increase(params) {
  return request({
      url: '/lot/increase/v2/' + params,
      method: 'get',
  })
}

 // 更新转存期
 export function uploadingDay(data) {
  return request({
    url: '/lot/uploading-day',
    method: 'post',
    data: data
  })
}

 // 除权操作
 export function exclusion(data) {
  return request({
    url: '/lot/exclusion',
    method: 'post',
    data: data
  })
}

 // 更新除权操作
 export function updateExclusion(data) {
  return request({
    url: '/lot/update-exclusion',
    method: 'post',
    data: data
  })
}

export function issuance(data) {
  return request({
    url: '/lot/publish/' + data,
    method: 'get',
    // params: data
  })
}

// 关联凭证
export function relateLot(data) {
  return request({
    url: '/zishajie/goods/lot/bind',
    method: 'post',
    data: data
  })
}

// 禁止会员挂单列表
export function getLotForbid(lotId) {
  return request({
    url: `/lot/list-registration/${lotId}`,
    method: 'get',
  })
}
