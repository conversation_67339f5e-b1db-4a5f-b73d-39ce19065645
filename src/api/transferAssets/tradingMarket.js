/*
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-04-11 13:08:14
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-04-16 10:44:34
 */
import request from '@/utils/request'

// 交易市场列表
export function pageList(data) {
    return request({
      url: '/statistical/page-list',
      method: 'post',
      data: data
    })
  }


  // 交易市场订单
export function orderCount(params) {
  return request({
    url: '/statistical/order-count',
    method: 'get',
    params: params
  })
}
// 交易市场订单统计
export function orderCountParams(params) {
  return request({
    url: '/statistical/order-count',
    method: 'post',
    data: params
  })
}

// 凭证订单取消
export function lotOrderCancel(params) {
  return request({
    url: '/statistical/lot/cancelOrder',
    method: 'get',
    params: params
  })
}

// 实物订单取消
export function goodsOrderCancel(data) {
  return request({
    url: '/zishajie/trade/cancel',
    method: "POST",
    data
  })
}

