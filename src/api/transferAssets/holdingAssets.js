/*
 * @Description: 持仓资产
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-04-11 13:08:14
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-05-18 14:22:20
 */
import request from '@/utils/request'


// 持仓资产分页
export function logPageListAll(data) {
  return request({
    url: '/lot/log-page-list-all',
    method: 'post',
    data: data
  })
}

export function logPageListAllV2(data) {
  return request({
    url: '/lot/log-page-list-all/v2',
    method: 'post',
    data: data
  })
}

// 日志分页列表
export function logPageList(data) {
  return request({
    url: '/lot/log-page-list',
    method: 'post',
    data: data
  })
}

export function logPageListV2(data) {
  return request({
    url: '/lot/log-page-list/v2',
    method: 'post',
    data: data
  })
}

// 日志分页列表
export function logPageCount(data) {
  return request({
    url: '/lot/log-page-list/count/v2',
    method: 'post',
    data: data
  })
}

    // 配货
 export function distribution(data) {
  return request({
     url: '/lot/distribution',
     method: 'post',
     data: data
   })
 }
