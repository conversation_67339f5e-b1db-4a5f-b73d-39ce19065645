/*
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-04-11 13:08:14
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-04-12 09:45:39
 */
import request from '@/utils/request'

// 统计
export function statistic(params) {
    return request({
      url: '/statistical/statistic',
      method: 'get',
      params: params
    })
  }

  // 新版统计

export function logStatistic(data){
    return request({
        url: '/statistics-center/total-log',
        method: 'post',
       data: data
    })
}

// 凭证总览统计
export function totalLotTotal(data){
  return request({
    url: '/statistics-center/total-lot-total',
    method: 'post',
    data: data
  })
}

//凭证总览分页接口
export function totalLotPage(data){
  return request({
    url: '/statistics-center/total-lot-page',
    method: 'post',
    data: data
  })
}

// 平台收益统计
export function totalPlatformTotal(data){
  return request({
    url: '/statistics-center/total-platform-total',
    method: 'post',
    data: data
  })
}

//平台收益分页接口
export function totalPlatformPage(data){
  return request({
    url: '/statistics-center/total-platform-page',
    method: 'post',
    data: data
  })
}


// 商家统计
export function totalUpbeatTotal(data){
  return request({
    url: '/statistics-center/total-upbeat-total',
    method: 'post',
    data: data
  })
}

//商家分页接口
export function totalUpbeatPage(data){
  return request({
    url: '/statistics-center/total-upbeat-page',
    method: 'post',
    data: data
  })
}



// 服务中心统计
export function totalServiceTotal(data){
  return request({
    url: '/statistics-center/total-service-total',
    method: 'post',
    data: data
  })
}

// 商城订单统计
export function totalGoodsTotal(data){
  return request({
    url: '/statistics-center/total-goods-total',
    method: 'post',
    data: data
  })
}

//服务中心分页接口
export function totalServicePage(data){
  return request({
    url: '/statistics-center/total-service-page',
    method: 'post',
    data: data
  })
}

// 商城订单分页接口
export function totalGoodsPage(data){
  return request({
    url: '/statistics-center/total-goods-page',
    method: 'post',
    data: data
  })
}

// 交易所统计
export function totalTradeTotal(data){
  return request({
    url: '/statistics-center/total-trade-total',
    method: 'post',
    data: data
  })
}

// 交易所分页接口
export function totalTradePage(data){
  return request({
    url: '/statistics-center/total-trade-page',
    method: 'post',
    data: data
  })
}

  // 统计总揽
export function statisticalList(data) {
  return request({
    url: '/statistical/statistical-list',
    method: 'post',
    data: data
  })
}
