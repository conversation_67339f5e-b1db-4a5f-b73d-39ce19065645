import request from '@/utils/request';

// 商品发行
export function goodsPublish(goodsId) {
  return request(`/zishajie/goods/${goodsId}/publish`, {
  });
}

//商品标签 start
export function loadGoodsLabelList(data) {
  return request(`/zishajie/goodslabel/list`, {
    data,
    method: 'post',
  });
}

export function addGoodsLabel(data) {
  return request(`/zishajie/goodslabel/add`, {
    data,
    method: 'post',
  });
}

export function modifyGoodsLabel(data) {
  return request(`/zishajie/goodslabel/modify`, {
    data,
    method: 'post',
  });
}

export function modifyGoodsLabelVisible(data) {
  return request(`/zishajie/goodslabel/modify-visible`, {
    data,
    method: 'post',
  });
}

export function deleteGoodsLabel(goodsLabelId) {
  return request(`/zishajie/goodslabel/delete/${goodsLabelId}`, {
  });
}

//商品标签 end

// 价格区间  start
export function loadPriceRangePage(data) {
  return request(`/zishajie/goods/priceRange/page`, {
    data,
    method: 'post',
  });
}

export function addPriceRange(data) {
  return request(`/zishajie/goods/priceRange/add`, {
    data,
    method: 'post',
  });
}

export function editPriceRange(data) {
  return request(`/zishajie/goods/priceRange/edit`, {
    data,
    method: 'post',
  });
}

export function deletePriceRange(rangeId) {
  return request(`/zishajie/goods/priceRange/delete/${rangeId}`, {
  });
}

export function getPriceRangeMax() {
  return request(`/zishajie/goods/priceRange/max`, {});
}

// 价格区间  end

export function addGoods(data) {
  return request(`/zishajie/goods/add/spu`, {
    data,
    method: 'post',
  });
}

export function getGoodsDetail(goodsId) {
  return request(`/zishajie/goods/spu/${goodsId}`, {});
}

export function editGoods(data) {
  return request(`/zishajie/goods/spu/edit`, {
    data,
    method: 'post',
  });
}
export function editGoodsShamSaleNum(data) {
  return request(`/zishajie/goods/spu/shamSaleNum/update`, {
    data,
    method: 'post',
  });
}

export function getGoodsSpuPage(data) {
  return request(`/zishajie/goods/spuPage`, {
    data,
    method: 'post',
  });
}

///eazyDeliveryVoucher/queryAll 提货券
export function queryPickUpVouchersAll(params) {
  return request(`/eazyDeliveryVoucher/queryAll`, {
    params,
    method: 'get',
  });
}

export function getGoodsImportPage(data) {
  return request(`/zishajie/goods/importPage`, {
    data,
    method: 'post',
  });
}
export function getGoodsImportDetail(appGoodsId) {
  return request(`/zishajie/goods/import/${appGoodsId}`, {
    method: 'get',
  });
}

export function getGoodsSkuList(goodsId) {
  return request(`/zishajie/goods/skuList/${goodsId}`, {});
}
export function offSpu(id) {
  return request(`/zishajie/goods/off/spu/${id}`, {});
}
export function onSpu(id) {
  return request(`/zishajie/goods/on/spu/${id}`, {});
}

export function getGoodsSpu(id) {
  return request(`/zishajie/goods/spu/${id}`, {});
}

export function loadGoodsBrandParamInPage(data) {
  return request(`/zishajie/goods/goodsbrand/loadParamInPage`, {
    data,
    method: 'post',
  });
}

export function getGoodsCateList() {
  return request(`/zishajie/goods/goodscate/list`, {});
}

export function deleteGoodsSpu(id) {
  return request(`/zishajie/goods/delete/spu/${id}`, {});
}

export function getGoodsProProp(cateId) {
  return request(`/zishajie/goods/goodsproperty/prop/${cateId}`, {});
}

export function loadFreightTempList(data) {
  return request(`/zishajie/goods/freightTemp/list`, {
    data,
  });
}

// /zishajie/goods/detail/trade/config
export function getGoodsDetailTradeConfig(params) {
  return request(`/zishajie/goods/detail/trade/config`, {
      params,
      method: 'get',
  }
  );
}

export function hotSpu(id) {
  return request(`/zishajie/goods/hot/spu/${id}`, {});
}


export function saveOrUpdateGoodsTradeConfig(data) {
  return request(`/zishajie/goods/saveOrUpdate/trade/config`, {
      data,
      method: 'post',
  }
  );
}
