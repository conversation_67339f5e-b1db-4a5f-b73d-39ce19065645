import { stringify } from 'qs';
import request from '@/utils/request';
import { restfulUrlReplace } from '@/utils/utils';

/** 省份列表* */
export function loadProvince ( query ) {
  return request(`/zishajie/trade/tool/getProvince`)
}

/** 城市* */
export function loadCity ( param ) {
  return request(restfulUrlReplace(`/zishajie/trade/tool/getCity/{addrId}`, param))
}

/** 区县* */
export function loadDistrict ( param ) {
  return request(restfulUrlReplace(`/zishajie/trade/tool/getDistrict/{addrId}`, param))
}

/** 街道* */
export function loadStreet ( param ) {
  return request(restfulUrlReplace(`/zishajie/trade/tool/getStreet/{addrId}`, param))
}

/** 列表查询* */
export function loadListByIds ( data ) {
  return request(`/zishajie/trade/tool/listPlatformAddress`, {
    data,
    method: 'post'
  })
}

/** 省市区树级* */
export function loadPlatformAddressTree ( query ) {
  return request([`/zishajie/trade/tool/addressTree`, stringify(query)].filter(item => item).join('?'))
}
