import request from '@/utils/request'


// 分页查询资讯接口
export function messagepage(params) {
    return request({
        url: '/informationCategory/page',
        method: 'get',
        params: params
    })
}

// 条件查询资讯类型
export function messagelist(params) {
    return request({
        url: '/informationCategory/list',
        method: 'get',
        params: params
    })
}


export function messagelist2(params) {
    return request({
        url: '/informationCategory/list2',
        method: 'get',
        params: params
    })
}



// //  根据 id 修改修改资讯类型信息接口
export function messageamend(data) {
    return request({
        url: '/informationCategory',
        method: 'put',
        data: data
    })
}



//  新增资讯类型接口
export function informationCategory(data) {
    return request({
        url: '/informationCategory',
        method: 'post',
        data: data
    })
}


// //  根据 id 修改修改资讯类型信息接口
export function messagedisable(params) {
    return request({
        url: '/informationCategory/disable',
        method: 'put',
        params: params
    })
}
//  根据 id 修改修改资讯类型信息接口  解禁
export function messageliftABan(params) {
    return request({
        url: '/informationCategory/enable',
        method: 'put',
        params: params
    })
}

// /news/submit  发布资讯
export function releaseMessage(data) {
    return request({
        url: '/news/submit',
        method: 'post',

        data: data
    })
}



// /news/submit  图片上传
export function uploadfun(data) {
    return request({
        url: '/pic/upload',
        method: 'post',
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        data: data
    })
}

// 资讯列表
//   资讯列表 分页请求
export function inforlistpage(params) {
    return request({
        url: '/news/page',
        method: 'get',
        params: params
    })
}

// 根据 id 修改资讯信息接口
export function alterinforlist(data) {
    return request({
        url: '/news',
        method: 'put',
        data: data
    })
}
// 根据 id 删除资讯接口
export function deleinforlist(params) {
    return request({
        url: '/news',
        method: 'delete',
        params: params
    })
}

// 根据 id 查询资讯接口
export function inquiryinforlist(params) {
    return request({
        url: '/news',
        method: 'get',
        params: params
    })
}


// 财务管理
// 财务提现申请 分页列表
// /financialManagement/queryAll
export function financialListPage(params) {
    return request({
        url: '/financialManagement/queryAll',
        method: 'get',
        params: params
    })
}


//   财务管理-->会员账户 ->余额明细列表接口    memberaccount
export function memberaccount(params) {
    return request({
        url: '/memberAccount/queryBalanceDetail',
        method: 'get',
        params: params
    })
}

//   财务管理-->会员账户 ->会员余额列表接口    memberaccount
export function queryMemberBalanceport(params) {
    return request({
        url: '/memberAccount/queryMemberBalance',
        method: 'get',
        params: params
    })
}

//   财务管理-->会员账户 ->会员充值    memberaccount
export function topUpMemberBalance(data) {
    return request({
        url: '/memberAccount/topUpMemberBalance',
        method: 'post',
        data: data
    })
}






//   财务管理-->会员账户 ->合规经营保证金   memberaccount
export function queryRetainageport(params) {
    return request({
        url: '/memberAccount/queryRetainage',
        method: 'get',
        params: params
    })
}

//   财务管理-->  提现管理账户
export function queryAllport(params) {
    return request({
        url: '/withdrawDeposit/queryAll',
        method: 'get',
        params: params
    })
}

//   财务管理-->  账户管理账户
// /platformAccount/queryAll
export function queryAllaccountport(params) {
    return request({
        url: '/platformAccount/queryAll',
        method: 'get',
        params: params
    })
}


// /withdrawDeposit/pass/{id} 提现审核 通过
export function withdrawdepositport(params) {
    return request({
        url: '/withdrawDeposit/pass/' + params,
        method: 'get',
        // params: params
    })
}

// /withdrawDeposit/reject   申请驳回
export function withdrawrejectport(data) {
    return request({
        url: '/withdrawDeposit/reject',
        method: 'post',
        data: data
    })
}

// /withdrawDeposit/updatePaymentErrorMsg  修改打款失败原因
export function withdrawUpdatePaymentErrorMsg(data) {
  return request({
    url: '/withdrawDeposit/updatePaymentErrorMsg',
    method: 'post',
    data: data
  })
}

// /withdrawDeposit/transfer / { id }  申请打款
export function withdrawtransferport(id, receiptNo) {
    return request({
        url: '/withdrawDeposit/transfer/' + id,
        method: 'get',
        ...receiptNo ? { params: { receiptNo } } : {}
    })
}

export function withdrawtransferportV2(id, receiptNo) {
    return request({
        url: '/withdrawDeposit/transfer/v2/' + id,
        method: 'get',
        ...receiptNo ? { params: { receiptNo } } : {}
    })
}

// /withdrawDeposit/transfer/v2/batch 批量打款
export function withdrawtransferBatchport(ids) {
    return request({
        url: '/withdrawDeposit/transfer/v2/batch',
        method: 'post',
        data: { ids }
    })
}

// 余额明细-统计所有会员的可用余额
// /memberAccount/queryBalanceDetailTotal
export function queryBalanceDetailTotal(params) {
    return request({
        url: '/memberAccount/queryBalanceDetailTotal',
        method: 'get',
        params: params
    })
}

// 余额明细-统计所有会员的合规经营保证金总额
// /memberAccount/queryRetainageTotal
export function queryRetainageTotal(params) {
    return request({
        url: '/memberAccount/queryRetainageTotal',
        method: 'get',
        params: params
    })
}

// 平台账户交易明细总额
// /platformAccount/queryTotal
export function queryTotal(params) {
    return request({
        url: '/platformAccount/queryTotal',
        method: 'get',
        params: params
    })
}

// 提现申请 金额的统计
// /withdrawDeposit/queryTotal
export function withdrawDepositqueryTotal(params) {
    return request({
        url: '/withdrawDeposit/queryTotal',
        method: 'get',
        params: params
    })
}










// 系统配置
// 海报添加
// /eazySharePoster/add
export function posteradd(data) {
    return request({
        url: '/eazySharePoster/add',
        method: 'post',
        data: data
    })
}

//      系统配置 -> 提现配置添加
// /eazyWithdrawalAllocation/add
export function Allocationadd(data) {
    return request({
        url: '/eazyWithdrawalAllocation/add',
        method: 'post',
        data: data
    })
}

// 系统配置 -> 提货配置添加

export function deliveryadd(data) {
    return request({
        url: '/delivery-config/save',
        method: 'post',
        data: data
    })
}

// 系统配置 -> 提货配置详情
export function deliveryDetail(data) {
    return request({
        url: '/delivery-config/detail',
        method: 'GET',
        data: data
    })
}

//     系统配置 -> 注册协议添加
// /eazyRegistAgreement/add
export function registrationport(data) {
    return request({
        url: '/eazyRegistAgreement/add',
        method: 'post',
        data: data
    })
}


// /eazyBasicSystem/add  系统配置 ->   基础配置

export function basicsport(data) {
    return request({
        url: '/eazyBasicSystem/add',
        method: 'post',
        data: data
    })
}



// 系统配置 -> /用户列表
export function userlistport(params) {
    return request({
        url: '/system/user/list',
        method: 'get',
        params: params
    })
}


// /system/role / list   角色列表
export function rolelistport(params) {
    return request({
        url: '/system/role/list',
        method: 'get',
        params: params
    })
}

// /system/user/add 新增
export function rolelistAddport(data) {
    return request({
        url: '/system/user/add',
        method: 'post',
        data: data
    })
}

// /system/user 编辑
export function rolelistEditport(data) {
    return request({
        url: '/system/user',
        method: 'put',
        data: data
    })
}


// 操作日志记录   列表
// /monitor/operlog/list
export function loglistport(params) {
    return request({
        url: '/monitor/operlog/list',
        method: 'get',
        params: params
    })
}




// /eazySystemUpdate/add   检测升级
export function upgradeport(data) {
    return request({
        url: '/eazySystemUpdate/add',
        method: 'post',
        data: data
    })
}


// /eazyBasicSystem/getDetail   基础配置获取详情
export function systemgetDetail(data) {
    return request({
        url: '/eazyBasicSystem/getDetail',
        method: 'GET',
        data: data
    })
}


//   提现获取详情
export function withdrawDetail(data) {
    return request({
        url: '/eazyWithdrawalAllocation/getDetail',
        method: 'GET',
        data: data
    })
}

// /eazySharePoster/getDetail   海报详情
export function SharePosterDetail(data) {
    return request({
        url: '/eazySharePoster/getDetail',
        method: 'GET',
        data: data
    })
}



// /eazySharePoster/getDetail   检测升级详情
export function UpdateDetail(data) {
    return request({
        url: '/eazySystemUpdate/getDetail',
        method: 'GET',
        data: data
    })
}

// /eazyRegistAgreement/getDetail  注册协议 获取详情
export function agreementDetail(data) {
    return request({
        url: '/eazyRegistAgreement/getDetail',
        method: 'GET',
        data: data
    })
}





// /eazyWithdrawalAllocation/getDetail
// /system/menu/treeselect
// export function treeselectport(data) {
//     return request({
//         url: '/system/menu/treeselect',
//         method: 'get',
//         data: data
//     })
// }


// 查询菜单下拉树结构
export function treeselect() {
    return request({
        url: '/system/menu/treeselect',
        method: 'get'
    })
}

// 根据角色ID查询菜单下拉树结构
export function roleMenuTreeselect(roleId) {
    return request({
        url: '/system/menu/roleMenuTreeselect/' + roleId,
        method: 'get'
    })
}


// 修改保存角色
// /system/role
export function rolealter(data) {
    return request({
        url: '/system/role',
        method: 'post',
        data: data
    })
}

// 新增角色
// /system/role/add
export function roleadd(data) {
    return request({
        url: '/system/role/add',
        method: 'post',
        data: data
    })
}

// 删除角色
export function delRole(roleId) {
    return request({
        url: '/system/role/' + roleId,
        method: 'POST'
    })
}

// 所属项目列表
export function projectList() {
    return request({
        url: '/eazyBasicSystem/project/list',
        method: 'get'
    })
}

// 轮播图列表
export function getCarouselList() {
  return request({
    url: '/eazyBasicSystem/system/listImages',
    method: 'get'
  })
}

// 项目管理列表
export function getProjectPage(data) {
  return request({
    url: '/eazyBasicSystem/project/page',
    method: 'post',
    data
  })
}

// 项目管理新增
export function addProject(data) {
  return request({
    url: '/eazyBasicSystem/project/add',
    method: 'post',
    data
  })
}

// 项目管理编辑
export function editProject(data) {
  return request({
    url: '/eazyBasicSystem/project/edit',
    method: 'post',
    data
  })
}

// 项目管理删除
export function delProject(params) {
  return request({
    url: '/eazyBasicSystem/project/delete',
    params
  })
}

///  下面的暂留 参考
// 菜单管理
// /system/menu/list  获取菜单列表
// export function menulistport(data) {
//     return request({
//         url: '/system/menu/list',
//         method: 'get',
//         data: data
//     })
// }

// // 菜单添加
// export function menuaddport(data) {
//     return request({
//         url: '/system/menu',
//         method: 'post',
//         data: data
//     })
// }





