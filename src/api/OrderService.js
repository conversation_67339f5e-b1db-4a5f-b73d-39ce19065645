import { stringify } from 'qs';
import request from '@/utils/request';
import { restfulUrlReplace } from '@/utils/utils';

export const getAllChannels = () => {
  return request(`/zishajie/getAllChannels`)
};

export const getTradePage = (params) => {
  return request(`/zishajie/trade/page`, {
    data: params,
    method: 'post'
  })
};

export const getTradeDetail = (params) => {
  return request(`/zishajie/trade/detail/${params.tradeId}`)
};

export const getRefundTradePage = (params) => {
  return request(`/zishajie/trade/refund/page`, {
    data: params,
    method: 'post'
  })
};

/** 分页加载定制列表* */
export function loadDealOrderCustomPage ( data ) {
  return request(`/zishajie/order/custom/page`, {
    data,
    method: 'post'
  })
}

/** 定制修改* */
export function updateOrderCustom ( data ) {
  return request(`/zishajie/order/custom/update`, {
    data,
    method: 'post'
  })
}

/** 分页加载议价列表* */
export function loadDealOrderBargainPage ( data ) {
  return request(`/zishajie/order/bargain/page`, {
    data,
    method: 'post'
  })
}

/** 议价修改* */
export function modifyOrderBargain ( data ) {
  return request(`/zishajie/order/bargain/modify`, {
    data,
    method: 'post'
  })
}

/** 加载订单配置* */
export function loadTradeSetting ( query ) {
  return request([`/zishajie/trade/setting/list`, stringify(query)].filter(item => item).join('?'))
}

/** 订单配置修改* */
export function modifyTradeSetting ( data ) {
  return request(`/zishajie/trade/setting/modify`, {
    data,
    method: 'post'
  })
}

// 加载所有渠道
export function loadAllChannels ( query ) {
  return request([`/zishajie/channels/all`, stringify(query)].filter(item => item).join('?'))
}

/** 订单分页* */
export function loadTradePage ( data ) {
  return request(`/zishajie/trade/page`, {
    data,
    method: 'post'
  })
}

/** 用户列表* */
export function loadUserBasicsByParam ( data ) {
  return request(`/zishajie/user/query/loadUserBasicsByParam`, {
    data,
    method: 'post'
  })
}

/** 用户地址* */
export function loadUserDeliveryAddressByUserId ( param ) {
  return request([`/zishajie/user/query/loadUserDeliveryAddressByUserId`, stringify(param)].filter(item => item).join('?'))
}

/** 订单详情* */
export function loadTradeDetail ( param ) {
  return request(restfulUrlReplace(`/zishajie/trade/{tradeId}`, param))
}

/** 订单编辑详情* */
export function loadTradeEditDetail ( param ) {
  return request(restfulUrlReplace(`/zishajie/trade/edit/{tradeId}`, param))
}

/** 物流公司* */
export function loadExpressCompanyList ( param ) {
  return request(restfulUrlReplace(`/zishajie/trade/tool/listExpressCompany`, param))
}

/** 订单发货* */
export function loadTradeDelivery ( data ) {
  return request(`/zishajie/trade/delivery`, {
    data,
    method: 'post'
  })
}

/** 订单自提* */
export function loadTradeSelfTake( data ) {
  return request(`/zishajie/trade/selfTake`, {
    data,
    method: 'post'
  })
}

/** 订单发货取消* */
export function loadTradeDeliveryCancel ( data ) {
  return request(`/zishajie/trade/deliveryCancel`, {
    data,
    method: 'post'
  })
}

/** 订单修改* */
export function loadTradeUpdate ( data ) {
  return request(`/zishajie/trade/update`, {
    data,
    method: 'post'
  })
}

/** 代客下单* */
export function loadTradeCommit ( data ) {
  return request(`/zishajie/trade/commit`, {
    data,
    method: 'post'
  })
}

/** 已付款 校验* */
export function loadTradePayCheck ( data ) {
  return request(`/zishajie/trade/pay/check/${data.tradeId}`, {
    data,
    method: 'post'
  })
}

/** 代付款 凭证支付* */
export function loadTradeOfflinePay ( data ) {
  return request(`/zishajie/trade/offline/pay`, {
    data,
    method: 'post'
  })
}

/** 代付款 关联支付单* */
export function loadLinkPayOrder ( data ) {
  return request(`/zishajie/trade/linkPayOrder`, {
    data,
    method: 'post'
  })
}

/** 代付款 二维码* */
export function loadTradePayCode ( data ) {
  return request(`/zishajie/trade/pay`, {
    data,
    method: 'post'
  })
}

/** 退单分页* */
export function loadRefundTradePage ( data ) {
  return request(`/zishajie/refund/page`, {
    data,
    method: 'post'
  })
}

/** 退单审核* */
export function loadRefundTradeAudit ( data ) {
  return request(`/zishajie/trade/refund/audit`, {
    data,
    method: 'post'
  })
}

/** 退款* */
export function loadRefundTradePay ( data ) {
  return request(`/zishajie/trade/refund/pay`, {
    data,
    method: 'post'
  })
}

/** 拒绝退款* */
export function loadRefundRejectPay ( data ) {
  return request(`/zishajie/trade/refund/rejectPay`, {
    data,
    method: 'post'
  })
}

/** 退单详情* */
export function loadRefundDetail ( param ) {
  return request(restfulUrlReplace(`/zishajie/trade/refund/detail/{id}`, param))
}

/** 物流 **/
export function loadExpressByDeliveryInfos ( data ) {
  return request(`/zishajie/trade/tool/deliveryInfos`, {
    data,
    method: 'post'
  })
}
