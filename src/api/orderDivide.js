import request from '@/utils/request';

export const getAllotOrderPage = (params) => {
  return request(`/allotOrder/query/page`, {
    data: params,
    method: 'POST'
  })
};

export const getAllotOrderDetail = (params) => {
  return request(`/allotOrder/query/detail`, {
    params: params,
  })
};

export const bathAllotOrder = (params) => {
  return request(`/allotOrder/createBathOrder`, {
    data: params,
    method: 'post'
  })
};

export const setAllotOrder = (params) => {
  return request(`/allotOrder/calculate`, {
    data: params,
    method: 'post'
  })
};

// 根据分账金额重新与计算订单分账统计
export const reCalcAllotOrder = (params) => {
  return request(`/allotOrder/preCalculate`, {
    data: params,
    method: 'post'
  })
};

// 分页查询分账单
export const getAllotBathOrderPage = (params) => {
  return request(`/allotBathOrder/query/page`, {
    data: params,
    method: 'post'
  })
};

// 确认分账
export const confirmAllotBathOrder = (params) => {
  return request(`/allotBathOrder/allot`, {
    data: params,
    method: 'post'
  })
};

// 取消分账
export const cancelAllotBathOrder = (params) => {
  return request(`/allotBathOrder/cancel`, {
    data: params,
    method: 'post'
  })
};

// 导出分账
export const exportAllotBathOrder = (params) => {
  return request(`/allotBathOrder/export`, {
    data: params,
    method: 'post'
  })
};
