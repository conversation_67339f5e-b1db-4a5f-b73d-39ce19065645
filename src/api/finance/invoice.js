import request from '@/utils/request'

// 开票申请分页接口
export function queryInvoicePage(data) {
    return request({
      url: '/invoice/page',
      method: 'post',
      data: data
    })
  }

  // 保存
export function uploadInvoice(data) {
    return request({
      url: '/invoice/upload',
      method: 'post',
      data: data
    })
  }

// 详情
export function queryInvoiceDetail(params) {
    return request({
      url: '/invoice/detail',
      method: 'get',
      params: params
    })
  }
