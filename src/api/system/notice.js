import request from '@/utils/request'

// 查询公告列表
export function listNotice(query) {
  return request({
    url: '/notice/page',
    method: 'get',
    params: query
  })
}

export function updateEnable(query) {
  return request({
    url: '/notice/updateEnable',
    method: 'get',
    params: query
  })
}

// 查询公告详细
export function getNotice(query) {
  return request({
    url: '/notice',
    method: 'get',
    params: query
  })
}

// 新增公告
export function addNotice(data) {
  return request({
    url: '/notice/save',
    method: 'post',
    data: data
  })
}

// 修改公告
export function updateNotice(data) {
  return request({
    url: '/notice/update',
    method: 'post',
    data: data
  })
}

// 删除公告
export function delNotice(query) {
  return request({
    url: '/notice',
    method: 'delete',
    params: query
  })
}
