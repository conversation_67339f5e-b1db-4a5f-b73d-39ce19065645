import request from '@/utils/request'

// 上下架
export function changeStatus(data) {
    return request({
      url: '/video/changeStatus',
      method: 'post',
      data: data
    })
  }

  // 保存
export function save(data) {
    return request({
      url: '/video/save',
      method: 'post',
      data: data
    })
  }

// 分页
export function queryVideoPage(params) {
    return request({
      url: '/video/page',
      method: 'get',
      params: params
    })
  }

  // 详情
export function loadVideoDetail ( id ) {
  return request({
    url: '/video/' + id,
    method: 'get'
  })
}

// 删除
export function delVideo(id) {
  return request({
    url: '/video/' + id,
    method: 'delete'
  })
}
