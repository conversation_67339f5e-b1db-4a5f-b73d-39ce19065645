@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.mr-16 {
  margin-right: 16px;
}

.my-16 {
  margin-top: 16px;
  margin-bottom: 16px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 24px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

.el-button {
  border-radius: 2px;
  padding: 0 16px;
  height: 32px;
  min-width: 32px;
  //line-height: 0px;
}

.el-button + .el-button {
  margin-left: 8px;
}

.el-button--text {
  padding-left: 0;
  padding-right: 0;
  height: initial;
  line-height: initial;
}

.el-button--text + .el-button--text {
  margin-left: 16px;
}

.el-form-inline {
  .el-form-item {
    margin-bottom: 0;
    display: flex;
    .el-form-item__label {
      flex-shrink: 0;
    }
    .el-input-number {
      width: auto;
    }
  }
}

.el-upload__tip {
  line-height: initial;
}

.list-card-default {
  border-radius: 2px;
  .el-card__header {
    padding: 16px 32px;
    border-bottom: 0;
  }
  .el-card__body {
    padding: 0 32px 32px;
  }
  .el-table {
    /*.el-table__cell {
      font-size: 14px;
      line-height: 1;
      padding: 16px 0;
      border-color: #E8E8E8;
      color: rgba(0,0,0,0.65);
      & > .cell {
        padding: 0 24px;
        line-height: 22px;
      }
    }
    .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
      background-color: #FAFAFA;
      color: rgba(0,0,0,0.85);
      font-weight: 500;
    }*/
  }
}

.status {
  display: flex;
  align-items: center;
  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }
  .status-text {
    font-size: 14px;
    color: rgba(0,0,0,0.88);
    line-height: 22px;
  }
  .status-dot + .status-text {
    margin-left: 8px;
  }
  &.status-blue {
    .status-dot {
      background-color: #1890FF;
    }
  }
  &.status-yellow {
    .status-dot {
      background-color: #FAAD14;
    }
  }
  &.status-green {
    .status-dot {
      background-color: #52C41A;
    }
  }
  &.status-red {
    .status-dot {
      background-color: #F5222D;
    }
  }
  &.status-gray {
    .status-dot {
      background-color: rgba(0,0,0,0.45);
    }
  }
}

a {
  color: #1890FF;
}

a + a {
  margin-left: 16px;
}

.text-14 {
  font-size: 14px;
  line-height: 22px;
}

.fs-16 {
  font-size: 16px;
  line-height: 24px;
}

.text-main {
  color: #1890FF;
}

.text-error {
  color: #F5222D;
}

.text-default {
  color: #000000;
}

.text-green {
  color: #52C41A;
}

.text-yellow {
  color: #FAAD14;
}

.text-red {
  color: #F5222D;
}

// 中性色
.text-black {
  color: rgba(0, 0, 0, 1);
}

.text-black-85 {
  color: rgba(0, 0, 0, 0.85);
}

.text-black-65 {
  color: rgba(0, 0, 0, 0.65);
}

.text-black-35 {
  color: rgba(0, 0, 0, 0.35);
}

.text-black-15 {
  color: rgba(0, 0, 0, 0.15);
}

.text-606266 {
  color: #606266;
}

.font-bold {
  font-weight: bold;
}

.bg-default {
  background-color: #F0F2F5;
}

.bg-white {
  background-color: #FFFFFF;
}

.p-24 {
  padding: 24px;
}

.px-32 {
  padding-left: 32px;
  padding-right: 32px;
}

.py-24 {
  padding-top: 24px;
  padding-bottom: 24px;
}

.ml-8 {
  margin-left: 8px;
}

.ml-32 {
  margin-left: 32px;
}

.ml-auto {
  margin-left: auto;
}

.mx-4 {
  margin-left: 4px;
  margin-right: 4px;
}

.mx-8 {
  margin-left: 8px;
  margin-right: 8px;
}

.mt-16 {
  margin-top: 16px;
}

.mb-16 {
  margin-bottom: 16px;
}

.rounded-2 {
  border-radius: 2px;
}

.rounded-4 {
  border-radius: 4px;
}

.rounded-6 {
  border-radius: 6px;
}

.rounded-8 {
  border-radius: 8px;
}

.flex {
  display: flex;
}

.gap-16 {
  gap: 16px;
}

.a-center {
  align-items: center;
}

.shrink-0 {
  flex-shrink: 0;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}

.justify-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-evenly {
  justify-content: space-evenly;
}

.align-center {
  align-items: center;
}

.flex-none {
  flex: none;
}

.cursor-pointer {
  cursor: pointer;
}

.span_all {
  display: flex;
}

.span_left {
  flex: none;
  width: 48px;
  height: 48px;
  margin-right: 8px;
  border-radius: 2px;

  .el-image, img {
    width: 100%;
    height: 100%;
    border-radius: 2px;
  }
}

.span_right {
  line-height: 22px;

  div + div {
    margin-top: 4px;
  }
}

.whole {
  padding: 10px 20px;
}

.action-flex-end {
  margin-bottom: 8px;
  display: flex;
  justify-content: end;
}

.amount-box {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  height: 40px;
  padding: 8px 16px;
  background: #E6F7FF;
  border: 1px solid #BAE7FF;
  border-radius: 4px;
  color: rgba(0,0,0,0.65);

  > p {
    margin-bottom: 0;
  }

  i:first-child {
    color: #1890FF;
  }

  * + * {
    margin-left: 8px;
  }

  span {
    color: #F5222D
  }
}

.full-content-box {
  padding: 24px;
  min-height: calc(100vh - 84px);
  background-color: #F0F2F5;
}

/* order */
.dialog-footer {
  text-align: right;
}
.tradeStatus {
  margin-right: 8px;
  color: #52c41a;
  font-weight: 400;
  font-size: 18px;
}
.tradeTitle {
  color: #333;
  font-weight: 400;
  font-size: 14px;
}
.tradeValue {
  color: #666;
  font-weight: 400;
  font-size: 14px;
}
.priceTitle {
  color: rgba(0, 0, 0, 0.65);
  font-weight: 400;
  font-size: 14px;
}
.priceValue {
  color: #333;
  font-weight: 500;
  font-size: 18px;
}
.tradeInfo {
  .el-descriptions__row > th,
  .el-descriptions__row > td {
    padding-bottom: 0;
  }
}
.logTitle {
  color: #333;
  font-weight: 500;
  font-size: 16px;
}
.imgBox {
  img {
    border: #f0f0f0 solid 1px;
    width: 60px;
    height: 60px;
  }
}

.rowTitle {
  background: #f5f7fa;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.rowBorderLeft {
  border-left: 3px solid #409eff;
}

.rowBorderBottom {
  border-bottom: 1px solid #ebeef5;
}

.colImg img {
  width: 40px;
  height: 40px;
  border-radius: 5px;
}

.rowBorderRight {
  border-right: 1px solid #ebeef5;
}

.itemBox {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  height: 39px;
  margin-left: 30px;
}

.spanBefore {
  margin-left: 8px;
}

.wrapBefore {
  margin-left: 50px;
}

.order-delivery-head ul {
  list-style: none;
  padding: 0;
}
.order-delivery-head ul li {
  margin: 5px 0;
}
