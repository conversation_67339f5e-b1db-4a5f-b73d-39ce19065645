/*
 * @Description:
 * @version:
 * @Author: 孙姜2307
 * @Date: 2024-04-02 10:05:01
 * @LastEditors: 孙姜2307
 * @LastEditTime: 2024-04-10 17:04:54
 */
import './common.less'
import Vue from 'vue'

import Cookies from 'js-cookie'

import Element, {Message, MessageBox} from 'element-ui'
import '@/setup/dayjs'
import './assets/styles/element-variables.scss'
import CKEditor from '@ckeditor/ckeditor5-build-classic'
import '@/assets/styles/index.scss' // global css
import '@/assets/styles/ruoyi.scss' // ruoyi css
import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // directive
import plugins from './plugins' // plugins
import { download } from '@/utils/request'

import './assets/icons' // icon
import './permission' // permission control
import { getDicts } from "@/api/system/dict/data";
import { getConfigKey } from "@/api/system/config";
import { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, handleTree } from "@/utils/ruoyi";
// 分页组件
import Pagination from "@/components/Pagination";
// 自定义表格工具组件
import RightToolbar from "@/components/RightToolbar"
// 富文本组件
import Editor from "@/components/Editor"
// 文件上传组件
import FileUpload from "@/components/FileUpload"
// 图片上传组件
import ImageUpload from "@/components/ImageUpload"
// 图片预览组件
import ImagePreview from "@/components/ImagePreview"
// 字典标签组件
import DictTag from '@/components/DictTag'
// 头部标签组件
import VueMeta from 'vue-meta'
// 字典数据组件
import DictData from '@/components/DictData'
import CustomElSelect from "@/components/CustomElSelect/index.vue";
import ElTableSearch from "@/components/ElTableSearch/index.vue";

import StatisticSearch from "@/components/ElTableSearch/NewSearch.vue";

import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/antd.less';
import {isProd} from '@/utils/tool'
// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.handleTree = handleTree
Vue.prototype.$isProd = isProd()

// 全局组件挂载
Vue.component('DictTag', DictTag)
Vue.component('Pagination', Pagination)
Vue.component('RightToolbar', RightToolbar)
Vue.component('Editor', Editor)
Vue.component('FileUpload', FileUpload)
Vue.component('ImageUpload', ImageUpload)
Vue.component('ImagePreview', ImagePreview)

Vue.use(Antd)
Vue.use(directive)
Vue.use(plugins)
Vue.use(VueMeta)
// Vue.use(CKEditor)
DictData.install()

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

// 注册自定义组件，覆盖默认的: el-select (加上clearable默认允许清空), el-image (没有图片显示暂无图片)
Vue.component('ElSelect', CustomElSelect);
// Vue.component('ElImage', CustomElImage);
Vue.component('ElTableSearch', ElTableSearch)
Vue.component('StatisticSearch', StatisticSearch)

Vue.config.productionTip = false

// 保存原始的 Message 实例
const originalMessage = Message;

// 创建代理对象，增强功能
const messageProxy = new Proxy(originalMessage, {
  apply(target, thisArg, args) {
    const [options] = args;

    // 如果是错误类型且消息为空，不显示
    if (options && options.type === 'error' && !options.message) {
      return;
    }

    // 调用原始的 Message 函数
    return Reflect.apply(target, thisArg, args);
  },
  get(target, prop) {
    if (prop === 'error') {
      // 自定义 error 方法
      return (msg) => {
        if (!msg) return; // 如果 msg 为空，不显示消息
        target.error(msg); // 调用原始的 error 方法
      };
    }

    // 对于其他方法，直接返回原始行为
    return target[prop];
  },
});

// 将代理对象挂载到 Vue.prototype
Vue.prototype.$message = messageProxy;

// 覆盖 Vue.prototype.$prompt
Vue.prototype.$prompt = (message, title, options = {}) => {
  // 设置全局默认选项
  const defaultOptions = {
    closeOnClickModal: false,  // 禁止点击遮罩层关闭
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  };

  // 合并用户传入的选项与默认选项
  const finalOptions = { ...defaultOptions, ...options };

  // 调用原始的 MessageBox.prompt
  return MessageBox.prompt(message, title, finalOptions);
};


new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
