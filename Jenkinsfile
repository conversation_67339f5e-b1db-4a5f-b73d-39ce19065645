pipeline {
  agent {
   kubernetes {
      inheritFrom 'nodejs base'
      containerTemplate {
        name 'nodejs'
        image 'node:14.19.0'
      }
    }
  }
  stages {
    stage('develop Clone repository') {
      agent none
      when {
        branch 'develop'
      }
      steps {
        git(url: 'https://gitlab.eazytec-cloud.com/new-tea-pi/new-tea-pi-fe', credentialsId: 'xiezhi-gitlab', branch: 'develop', changelog: true, poll: false)
      }
    }
    stage('release Clone repository') {
      agent none
      when {
        branch 'release'
      }
      steps {
        git(url: 'https://gitlab.eazytec-cloud.com/new-tea-pi/new-tea-pi-fe', credentialsId: 'xiezhi-gitlab', branch: 'release', changelog: true, poll: false)
      }
    }


    stage('develop build') {
      agent none
      when {
        branch 'develop'
      }
      steps {
        container('nodejs') {
          withCredentials([string(credentialsId : 'npm-repo-user', variable : 'NPM_TOKEN',)]) {
            sh 'cat default.conf'
            sh 'export NODE_OPTIONS=--max_old_space_size=4096 && npm config set registry=https://packages.aliyun.com/6335523fd4fad3d161e93034/npm/npm-registry/:_authToken "$NPM_TOKEN"  && yarn config set registry=https://packages.aliyun.com/6335523fd4fad3d161e93034/npm/npm-registry/:_authToken "$NPM_TOKEN" && npm config set always-auth true && yarn install --frozen-lockfile && npm run build:stage'
          }
        }
      }
    }

    stage('release build') {
      agent none
      when {
        branch 'release'
      }
      steps {
        container('nodejs') {
          withCredentials([string(credentialsId : 'npm-repo-user', variable : 'NPM_TOKEN',)]) {
            sh 'cat default.conf'
            sh 'export NODE_OPTIONS=--max_old_space_size=4096 && npm config set registry=https://packages.aliyun.com/6335523fd4fad3d161e93034/npm/npm-registry/:_authToken "$NPM_TOKEN"  && yarn config set registry=https://packages.aliyun.com/6335523fd4fad3d161e93034/npm/npm-registry/:_authToken "$NPM_TOKEN" && npm config set always-auth true && yarn install --frozen-lockfile && npm run build:prod'
          }
        }
      }
    }


    stage('develop push') {
      agent none
      when {
        branch 'develop'
      }
      steps {
        container('base') {
          withCredentials([usernamePassword(credentialsId : 'harbor-user', passwordVariable : 'DOCKER_PASSWORD', usernameVariable : 'DOCKER_USERNAME',)]) {
            sh 'docker build -t "$DOCKER_IMAGE" .'
            sh 'docker login -u "$DOCKER_USERNAME" -p "$DOCKER_PASSWORD"  $DOCKER_REPO'
            sh 'docker tag $TARGET:latest $DOCKER_REPO/$DOCKER_PROJECT/$TARGET:latest'
            sh 'docker push $DOCKER_REPO/$DOCKER_PROJECT/$TARGET:latest'
          }
        }
      }
    }

    stage('release push') {
      agent none
      when {
        branch 'release'
      }
      steps {
        container('base') {
          withCredentials([usernamePassword(credentialsId : 'harbor-user', passwordVariable : 'DOCKER_PASSWORD', usernameVariable : 'DOCKER_USERNAME',)]) {
            sh 'docker build -t "$DOCKER_IMAGE_PROD" .'
            sh 'docker login -u "$DOCKER_USERNAME" -p "$DOCKER_PASSWORD"  $DOCKER_REPO'
            sh 'docker tag $TARGET_PROD:latest $DOCKER_REPO/$DOCKER_PROJECT/$TARGET_PROD:latest'
            sh 'docker push $DOCKER_REPO/$DOCKER_PROJECT/$TARGET_PROD:latest'
          }
        }
      }
    }

    stage('deploy to develop') {
        agent {
            node {
            label 'maven'
            }
        }
        when {
            branch 'develop'
        }
        steps {
        container('maven') {
          withCredentials([kubeconfigFile(credentialsId: 'kubeconfig', variable: 'KUBECONFIG')]) {
                      sh 'kubectl rollout restart deployment $DEPLOYMENT_NAME -n $NAMESPACE'
          }
        }
      }
    }
    stage('deploy to release') {
        agent {
            node {
            label 'maven'
            }
        }
        when {
            branch 'release'
        }
        steps {
        container('maven') {
          withCredentials([kubeconfigFile(credentialsId: 'kubeconfig', variable: 'KUBECONFIG')]) {
                      sh 'kubectl rollout restart deployment $DEPLOYMENT_NAME_PROD -n $NAMESPACE'
          }
        }
      }
    }
  }
  environment {
    TARGET = 'test-new-tea-pi-fe'
    TARGET_PROD = 'new-tea-pi-fe-prod'
    DOCKER_IMAGE = 'test-new-tea-pi-fe:latest'
    DOCKER_IMAGE_PROD = 'new-tea-pi-fe-prod:latest'
    DOCKER_PROJECT = 'new-tea-pi'
    DOCKER_REPO = '192.168.0.154:30002'
    DEPLOYMENT_NAME = 'test-new-tea-pi-fe-v1'
    DEPLOYMENT_NAME_PROD = 'new-tea-pi-fe-prod-v1'
    NAMESPACE = 'new-tea-pi'
  }
}
