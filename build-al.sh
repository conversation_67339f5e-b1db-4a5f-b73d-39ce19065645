#!/usr/bin/env bash

# 获取当前分支名
git_branch=$(git rev-parse --abbrev-ref HEAD)

# 根据分支判断打包环境
if [ "$git_branch" = "release" ]; then
  TARGET="new-tea-pi-fe-prod"
  BUILD_CMD="yarn build:prod"
elif [ "$git_branch" = "stage" ]; then
  TARGET="stage-new-tea-pi-fe"
  BUILD_CMD="yarn build:stage"
else
  TARGET="test-new-tea-pi-fe"
  BUILD_CMD="yarn build:stage"
fi

rm -rf dist

echo "开始构建: $TARGET"

$BUILD_CMD

docker login -u <EMAIL> -p Djk7X3rE6nR37wmD 47.103.57.78:30002

docker build -f Dockerfile-local -t $TARGET:latest .

docker tag $TARGET 47.103.57.78:30002/new-tea-pi/$TARGET:latest

docker push 47.103.57.78:30002/new-tea-pi/$TARGET:latest

echo "构建完成: $TARGET"
