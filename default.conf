gzip on;
      gzip_disable "msie6";
      gzip_proxied any;
				#gzip_min_length 1000;
      gzip_comp_level 9;
			gzip_types image/jpeg image/png application/octet-stream application/javascript text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript;

			client_max_body_size 500M;


			server
			{
					listen       8443 ;
					add_header Access-Control-Allow-Origin *;
          add_header Content-Security-Policy upgrade-insecure-requests;
          root  /usr/share/nginx/html;


        location / {
          index  index.html;
          try_files $uri $uri/ /index.html;
        }

        location = /index.html {
          add_header Cache-Control no-cache;
        }

        location ~ \.css {
        add_header  Content-Type    text/css;
        }
        location ~ \.js {
        add_header  Content-Type    application/javascript;
        }

        location ~ \.(ini|conf|txt|sh)$ {
          deny all;
        }

			}
